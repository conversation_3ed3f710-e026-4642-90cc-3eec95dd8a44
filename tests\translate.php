<?php
// Include the translation array from the file
$translations = include(ANALYST_PLUGIN_DIR . 'data/translate.php');

// prr($translations);
function translateValues($data, $translations) {
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            // If it's an array, recursively translate values
            $data[$key] = translateValues($value, $translations);
        } elseif (is_string($value)) {
            // If it's a string, translate the value if a translation is available
            if (isset($translations[$value])) {
                $data[$key] = $translations[$value];
            }
        }
    }
    return $data;
}

$file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';

// Load JSON file
$jsonData = file_get_contents($file_path);
$data = json_decode($jsonData, true);

// Translate the values
$translatedData = translateValues($data, $translations);

// Save the translated JSON data back to the file
file_put_contents($file_path, json_encode($translatedData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

// Display a message indicating completion
echo "Translation completed and JSON file updated.";

// Function to recursively search and collect English texts for translation
function collectTextsForTranslation($data, $keysToTranslate = ['title', 'message', 'success_message', 'label', 'error_message', 'name']) {
    $textsForTranslation = array();

    foreach ($data as $key => $value) {
        if (is_array($value)) {
            // If it's an array, recursively collect texts for translation
            $textsForTranslation = array_merge($textsForTranslation, collectTextsForTranslation($value, $keysToTranslate));
        } elseif (is_string($value) && in_array($key, $keysToTranslate)) {
            // If it's a string and the key is in the list of keys to translate
            // If it doesn't contain Arabic characters or numbers,
            // add it to the texts for translation
            if (!preg_match('/[\p{Arabic}\p{N}]/u', $value)) {
                $textsForTranslation[] = $value;
            }
        }
    }
    
    return $textsForTranslation;
}

$file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';

// Load JSON file
$jsonData = file_get_contents($file_path);
$data = json_decode($jsonData, true);

// Collect all English texts for translation
$textsForTranslation = collectTextsForTranslation($data);

// Remove duplicate texts
$textsForTranslation = array_unique($textsForTranslation);

// Display all texts for translation
echo "Texts for Translation:\n<br>";
foreach ($textsForTranslation as $text) {
    echo "- $text\n<br>";
}


