<?php

function not_allowed( $label ){
    $not_allowedd =  array(
        'id',
        'is_new',
        'description_simple',
        'actor_simple',
        'preconditions_simple',
        'postconditions_simple',
        'main_flow_steps_simple',
        'alternative_flow_steps', //noted
        'trigger',
        'frequency_of_use',
        'flowchart_mermaid'

    );

    if( in_array( $label, $not_allowedd ) ) {
        return true;
    }
}
function label_handler( $label ){
    $array= array(
        'goal' => false,
        'stakeholder' => false,
        'step' => false,
        'story' => false,
        'indicators' => false,
        'condition' => false,
        'rule' => false,
        'assumption' => false,
        'event' => false,
        'requirement' => false,
        'name' => false,
        'method' => false,
        'scenario' => false,
        'input' => false,
        'system' => false,

        'case_id' => 'الحالة رقم',
        'description' => 'الوصف',
        'actor' => 'الشخصية',
        'preconditions' => 'الشروط الأساسية',
        'postconditions' => 'الشروط النهائية',
        'main_flow_steps' => 'الخطوات الأساسية', 
        'alternative_flow_steps' => 'الخطوات البديلة',
        'exception_flow_steps' => 'الخطوات الخاصة',
        'steps'=> 'الخطوات',
        'priority' => 'الأولوية',
        'business_rules' => 'اشتراطات العمل',
        'assumptions' => 'الافتراضات المسبقة',
        'special_requirements' => 'متطلبات خاصة',
        'notes_and_issues' => 'ملاحظات',
        'events_sequence' => 'تسلسل الاحداث الأحداث',
        'inputs' => 'المدخلات',
        'outputs' => 'المخرجات',
        'user_interactions' => 'التفاعلات المستخدم',
        'special_conditions' => 'حالات خاصة',
        'usage_scenarios' => 'سيناريوهات الاستخدام',
        'security_requirements' => 'متطلبات الامنية',
        'integration_with_other_systems' => 'التكامل مع الأنظمة الأخرى',
        'constraints_and_assumptions'=> 'الحدود والافتراضات',
        'testing_requirements' => 'متطلبات الاختبار',
        'backend_details' => 'تفاصيل ال API',
        'frontend_details' => 'تفاصيل الواجهات',
        'method' => 'الطريقة',
        'endpoint' => 'رابط النقطة',
        'request' => 'تفاصيل الطلب',
        'screens' => 'الشاشات',
        'components' => 'مكونات الشاشة',
        'notifications' => 'الاشعارات',
        'methods' => 'عن طريق',
        'recipient' => 'المستلم',
        'title' => 'عنوان',
        'message' => 'نص الاشعار',
        'type' => 'النوع',
        'props' => 'الخصائص',
        'response' => 'الاستجابة',
        'body' => 'المحتوى',
        'headers' => 'مرفقات',
        'status_code' => 'كود الطلب',

        





    );

    if( isset( $array[$label] ) ){
        return $array[ $label ];
    }

    return $label;
}
function value_handler( $label ,  $value ){
    if( $label == 'alternative_flow_steps' ){
        return $value['steps'];
    }

    if( $label == 'backend_details' ){
        return $value[0]['api_endpoints'];
    }

    return $value;
}
// دالة لعرض الحقول النصية
function display_text_field($value , $label = false ) {

    $label = label_handler( $label );

    if( $label ){
        echo "<strong>$label:</strong> $value<br>";
    }else {
        echo "$value<br>";
    }
}

// دالة لعرض الحقول المتكررة (repeater fields)
function display_repeater_field( array $repeater , $label =false) {
    if( not_allowed( $label ) ){
        return;
    }
    $label = label_handler( $label );


    echo "<strong><h4>$label</h4></strong>";
    
    echo "<ul>";
    foreach ($repeater as $item) {
        echo "<li>";
        foreach ($item as $sub_label => $sub_value) {

            if( not_allowed( $sub_label ) ) {
                continue;
            }

            $sub_value = value_handler( $sub_label, $sub_value );

            if ( is_array($sub_value) ) {
                display_repeater_field($sub_value , $sub_label );
            } elseif ( is_string( $sub_value ) ) {
                display_text_field($sub_value , $sub_label);
            }else {

                // if no value then skip
                // now we show it for translating
                //display_text_field($sub_value , $sub_label);
            }
        }
        echo "</li>";
    }
    echo "</ul>";
}

function display_acf_fields($post_id) {
    $fields = get_field_objects($post_id);
    foreach ($fields as $field_name => $field) {
        $label = $field['label'];
        $value = $field['value'];

        if( not_allowed( $label ) ){
            continue;
        }

        if ($field['type'] == 'repeater') {
            display_repeater_field( $value, $label );
        } else {
            display_text_field( $value, $label );
        }
    }
}

// التأكد من وجود منشور
if (have_posts()) : 
    while (have_posts()) : the_post(); ?>
        
    <div style="direction: rtl;">
        <h3><?php the_title(); ?></h3>
        <div>
            <?php
            // عرض الحقول المخصصة باستخدام ACF
            display_acf_fields(get_the_ID());
            ?>
        </div>

    </div>
    <?php endwhile;
else :
    echo 'لا توجد منشورات لعرضها.';
endif;