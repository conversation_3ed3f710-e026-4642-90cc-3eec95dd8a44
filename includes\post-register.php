<?php
function cptui_register_my_cpts() {
    $labels = [
        "name" => esc_html__( "Requirements", "workreap" ),
        "singular_name" => esc_html__( "Requirement", "workreap" ),
        "menu_name" => esc_html__( "Requirements", "workreap" ),
        "all_items" => esc_html__( "All Requirements", "workreap" ),
        "add_new" => esc_html__( "Add new", "workreap" ),
        "add_new_item" => esc_html__( "Add new Requirement", "workreap" ),
        "edit_item" => esc_html__( "Edit Requirement", "workreap" ),
        "new_item" => esc_html__( "New Requirement", "workreap" ),
        "view_item" => esc_html__( "View Requirement", "workreap" ),
        "view_items" => esc_html__( "View Requirements", "workreap" ),
        "search_items" => esc_html__( "Search Requirements", "workreap" ),
        "not_found" => esc_html__( "No Requirements found", "workreap" ),
        "not_found_in_trash" => esc_html__( "No Requirements found in trash", "workreap" ),
        "parent" => esc_html__( "Parent Requirement:", "workreap" ),
        "featured_image" => esc_html__( "Featured image for this Requirement", "workreap" ),
        "set_featured_image" => esc_html__( "Set featured image for this Requirement", "workreap" ),
        "remove_featured_image" => esc_html__( "Remove featured image for this Requirement", "workreap" ),
        "use_featured_image" => esc_html__( "Use as featured image for this Requirement", "workreap" ),
        "archives" => esc_html__( "Requirement archives", "workreap" ),
        "insert_into_item" => esc_html__( "Insert into Requirement", "workreap" ),
        "uploaded_to_this_item" => esc_html__( "Upload to this Requirement", "workreap" ),
        "filter_items_list" => esc_html__( "Filter Requirements list", "workreap" ),
        "items_list_navigation" => esc_html__( "Requirements list navigation", "workreap" ),
        "items_list" => esc_html__( "Requirements list", "workreap" ),
        "attributes" => esc_html__( "Requirements attributes", "workreap" ),
        "name_admin_bar" => esc_html__( "Requirement", "workreap" ),
        "item_published" => esc_html__( "Requirement published", "workreap" ),
        "item_published_privately" => esc_html__( "Requirement published privately.", "workreap" ),
        "item_reverted_to_draft" => esc_html__( "Requirement reverted to draft.", "workreap" ),
        "item_trashed" => esc_html__( "Requirement trashed.", "workreap" ),
        "item_scheduled" => esc_html__( "Requirement scheduled", "workreap" ),
        "item_updated" => esc_html__( "Requirement updated.", "workreap" ),
        "parent_item_colon" => esc_html__( "Parent Requirement:", "workreap" ),
    ];
    
    $args = [
        "label" => esc_html__( "Requirements", "workreap" ),
        "labels" => $labels,
        "description" => "",
        "public" => true,
        "publicly_queryable" => true,
        "show_ui" => true,
        "show_in_rest" => true,
        "rest_base" => "",
        "rest_controller_class" => "WP_REST_Posts_Controller",
        "rest_namespace" => "wp/v2",
        "has_archive" => false,
        "show_in_menu" => true,
        "show_in_nav_menus" => true,
        "delete_with_user" => false,
        "exclude_from_search" => false,
        "capability_type" => "post",
        "map_meta_cap" => true,
        "hierarchical" => false,
        "can_export" => false,
        "rewrite" => [ "slug" => "requirement", "with_front" => true ],
        "query_var" => true,
        "supports" => [ "title", "editor", "thumbnail" ],
        "show_in_graphql" => false,
    ];
    
    register_post_type( "requirement", $args );

}
    
add_action( 'init', 'cptui_register_my_cpts' );