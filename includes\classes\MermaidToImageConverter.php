<?php
/**
 * Plugin Name: Mermaid to Image Converter
 * Description: Converts Mermaid flowchart code to an image and displays it using an external API.
 * Version: 1.0
 * Author: Your Name
 */

class MermaidToImageConverter {

    private $mermaidCode;

    public function __construct($mermaidCode) {
        $this->mermaidCode = $mermaidCode;
    }

    public function generateImage() {
        $apiUrl = "https://kroki.io/mermaid/png";

        // Use cURL to send the POST request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->mermaidCode);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            // 'Accept: image/svg+xml',
            'Content-Type: text/plain'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        if ($response) {
            // Save the SVG image locally in the uploads directory
            $upload_dir = wp_upload_dir();
            $imagePath = $upload_dir['basedir'] . '/mermaid_diagram.png';
            file_put_contents($imagePath, $response);
            return $upload_dir['baseurl'] . '/mermaid_diagram.png';
        } else {
            return false;
        }
    }
}