# تعليمات استخدام ميزة تصدير المتطلبات إلى JSON

## نظرة عامة

تم إضافة ميزة جديدة إلى نظام إدارة المتطلبات تقوم بحفظ جميع بيانات المتطلبات تلقائياً في ملفات JSON عند كل عملية إضافة أو تعديل.

## الميزات الجديدة

### 1. التصدير التلقائي
- **متى يحدث**: عند حفظ أو تحديث أي متطلب
- **ما يتم حفظه**: جميع الحقول المخصصة (ACF fields) للمتطلب
- **مكان الحفظ**: مجلد `requirments/` داخل البرنامج المساعد

### 2. هيكل الملفات
- **اسم الملف**: `requirement_{requirement_id}_{post_id}.json`
- **مثال**: `requirement_4.2.1_123.json`

### 3. محتوى الملف
كل ملف JSON يحتوي على:
- معلومات المنشور الأساسية (العنوان، التاريخ، الحالة)
- رقم المتطلب
- أهداف العمل
- الجهات المعنية
- الخطوات الرئيسية والبديلة
- قصص المستخدمين
- مؤشرات الأداء
- حالات الاستخدام (مع جميع التفاصيل)

## كيفية الاستخدام

### للمطورين

1. **تفعيل الميزة**: الميزة تعمل تلقائياً بمجرد تفعيل البرنامج المساعد

2. **الوصول للملفات**: 
   ```php
   $json_exporter = new Save_Requirements_JSON();
   $files = $json_exporter->get_requirement_json_files();
   ```

3. **تصدير يدوي**:
   ```php
   $json_exporter = new Save_Requirements_JSON();
   $json_exporter->save_requirement_as_json($post_id);
   ```

### للمستخدمين

1. **إنشاء متطلب جديد**:
   - اذهب إلى لوحة التحكم → Requirements → Add New
   - املأ جميع الحقول المطلوبة
   - اضغط "Publish" أو "Update"
   - سيتم إنشاء ملف JSON تلقائياً

2. **تحديث متطلب موجود**:
   - افتح المتطلب للتحرير
   - قم بالتعديلات المطلوبة
   - اضغط "Update"
   - سيتم تحديث ملف JSON تلقائياً

## الاختبار والتحقق

### صفحة الاختبار
يمكنك الوصول إلى صفحة الاختبار عبر:
```
/wp-content/plugins/system-analyst/tests/test-json-export.php
```

هذه الصفحة تتيح لك:
- عرض جميع المتطلبات
- تصدير متطلب واحد أو جميع المتطلبات
- عرض حالة ملفات JSON
- التحقق من وجود الملفات

### عارض JSON
لعرض محتوى ملف JSON:
```
/wp-content/plugins/system-analyst/tests/view-json.php?file=filename.json
```

## استكشاف الأخطاء

### المشكلة: لا يتم إنشاء ملفات JSON

**الحلول المحتملة**:
1. تحقق من صلاحيات الكتابة في مجلد `requirments/`
2. تأكد من أن المتطلب من نوع `requirement`
3. راجع سجل الأخطاء في WordPress

### المشكلة: ملفات JSON فارغة أو غير مكتملة

**الحلول المحتملة**:
1. تأكد من ملء جميع الحقول المطلوبة
2. تحقق من أن ACF fields تعمل بشكل صحيح
3. راجع إعدادات ACF

### المشكلة: الملفات القديمة لا تُحذف

**الحلول المحتملة**:
1. تحقق من صلاحيات الحذف في المجلد
2. تأكد من أن post meta يتم حفظه بشكل صحيح

## الملفات المضافة/المعدلة

### ملفات جديدة:
- `includes/classes/save_requirements_json.php` - الكلاس الرئيسي
- `tests/test-json-export.php` - صفحة الاختبار
- `tests/view-json.php` - عارض JSON
- `requirments/README.md` - تعليمات المجلد

### ملفات معدلة:
- `class-system-analyst.php` - إضافة تحميل الكلاس الجديد

## الدعم الفني

في حالة وجود مشاكل:

1. **تحقق من السجلات**: راجع error log في WordPress
2. **اختبر الميزة**: استخدم صفحة الاختبار
3. **تحقق من الصلاحيات**: تأكد من صلاحيات القراءة/الكتابة
4. **راجع الإعدادات**: تأكد من إعدادات ACF

## ملاحظات مهمة

- الميزة تعمل فقط مع منشورات من نوع `requirement`
- يتم حفظ الملفات بتنسيق UTF-8 لدعم النصوص العربية
- الملفات تحتوي على معلومات إضافية مثل تاريخ التصدير وإصدار البرنامج
- يتم حذف الملفات القديمة تلقائياً عند تغيير رقم المتطلب أو حذف المنشور

## التطوير المستقبلي

يمكن إضافة المزيد من الميزات مثل:
- تصدير جماعي لجميع المتطلبات
- استيراد من ملفات JSON
- جدولة التصدير التلقائي
- ضغط الملفات القديمة
- إرسال الملفات عبر البريد الإلكتروني
