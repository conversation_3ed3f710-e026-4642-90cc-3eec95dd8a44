# نظام المتطلبات المعتمد على JSON

## نظرة عامة

تم تطوير نظام شامل لإدارة المتطلبات يعتمد على ملفات JSON بدلاً من قاعدة البيانات لحفظ وقراءة بيانات ACF. هذا النظام يوفر مرونة كاملة في تحرير البيانات مباشرة من محرر الأكواد.

## الميزات الجديدة

### 1. **فصل البيانات**
- **قاعدة البيانات**: تحتوي فقط على معلومات WordPress الأساسية (العنوان، التاريخ، الحالة)
- **ملفات JSON**: تحتوي على جميع حقول ACF حصرياً
- **عدم التداخل**: لا يتم حفظ أي حقل ACF في قاعدة البيانات

### 2. **هيكل المجلدات**
- **مجلد منفصل لكل متطلب**: `{requirement_id} - {post_title}/`
- **ملف البيانات**: `data.json` داخل كل مجلد
- **مثال**: `4.2.1 - التسجيل في المنصة/data.json`

### 3. **القراءة من JSON**
- **الواجهة الأمامية**: تقرأ البيانات من JSON فقط
- **دوال مخصصة**: `get_requirement_field()` بدلاً من `get_field()`
- **تحميل تلقائي**: النظام يحمل البيانات من JSON تلقائياً

### 4. **محتوى ملف data.json**
كل ملف يحتوي على:
- عنوان المتطلب
- جميع حقول ACF بالكامل (بدون معالجة)
- البيانات كما هي مدخلة في WordPress

## كيفية الاستخدام

### للمطورين

#### **الدوال الجديدة المتاحة:**

1. **قراءة حقل واحد**:
   ```php
   // بدلاً من get_field('field_name', $post_id)
   $value = get_requirement_field('field_name', $post_id);
   ```

2. **قراءة جميع بيانات المتطلب**:
   ```php
   $requirement_data = get_requirement_data($post_id);
   ```

3. **البحث بواسطة رقم المتطلب**:
   ```php
   $requirement = get_requirement_by_id('4.2.1');
   ```

4. **الحصول على جميع المتطلبات**:
   ```php
   $all_requirements = get_all_requirements(); // مرتبة حسب الرقم
   $all_requirements = get_all_requirements(false); // غير مرتبة
   ```

5. **التحقق من وجود متطلب**:
   ```php
   if (requirement_exists('4.2.1')) {
       // المتطلب موجود
   }
   ```

6. **عرض حقل مع fallback**:
   ```php
   the_requirement_field('field_name', $post_id, 'قيمة افتراضية');
   ```

7. **عرض حقل متكرر**:
   ```php
   the_requirement_repeater('business_goals', $post_id, 'goal', '، ');
   ```

#### **في Templates:**

```php
// الطريقة القديمة (لن تعمل للمتطلبات)
$business_goals = get_field('business_goals', $post_id);

// الطريقة الجديدة
$business_goals = get_requirement_field('business_goals', $post_id);

// أو استخدام الدالة المساعدة
the_requirement_field('id', $post_id);
```

### للمستخدمين

1. **إنشاء متطلب جديد**:
   - اذهب إلى لوحة التحكم → Requirements → Add New
   - املأ جميع الحقول المطلوبة
   - اضغط "Publish" أو "Update"
   - سيتم إنشاء ملف JSON تلقائياً

2. **تحديث متطلب موجود**:
   - افتح المتطلب للتحرير
   - قم بالتعديلات المطلوبة
   - اضغط "Update"
   - سيتم تحديث ملف JSON تلقائياً

## الاختبار والتحقق

### صفحة الاختبار
يمكنك الوصول إلى صفحة الاختبار عبر:
```
/wp-content/plugins/system-analyst/tests/test-json-export.php
```

هذه الصفحة تتيح لك:
- عرض جميع المتطلبات
- تصدير متطلب واحد أو جميع المتطلبات
- عرض حالة ملفات JSON
- التحقق من وجود الملفات

### عارض JSON
لعرض محتوى ملف JSON:
```
/wp-content/plugins/system-analyst/tests/view-json.php?file=filename.json
```

## استكشاف الأخطاء

### المشكلة: لا يتم إنشاء ملفات JSON

**الحلول المحتملة**:
1. تحقق من صلاحيات الكتابة في مجلد `requirments/`
2. تأكد من أن المتطلب من نوع `requirement`
3. راجع سجل الأخطاء في WordPress

### المشكلة: ملفات JSON فارغة أو غير مكتملة

**الحلول المحتملة**:
1. تأكد من ملء جميع الحقول المطلوبة
2. تحقق من أن ACF fields تعمل بشكل صحيح
3. راجع إعدادات ACF

### المشكلة: الملفات القديمة لا تُحذف

**الحلول المحتملة**:
1. تحقق من صلاحيات الحذف في المجلد
2. تأكد من أن post meta يتم حفظه بشكل صحيح

## الملفات المضافة/المعدلة

### ملفات جديدة:
- `includes/classes/save_requirements_json.php` - الكلاس الرئيسي
- `tests/test-json-export.php` - صفحة الاختبار
- `tests/view-json.php` - عارض JSON
- `requirments/README.md` - تعليمات المجلد

### ملفات معدلة:
- `class-system-analyst.php` - إضافة تحميل الكلاس الجديد

## الدعم الفني

في حالة وجود مشاكل:

1. **تحقق من السجلات**: راجع error log في WordPress
2. **اختبر الميزة**: استخدم صفحة الاختبار
3. **تحقق من الصلاحيات**: تأكد من صلاحيات القراءة/الكتابة
4. **راجع الإعدادات**: تأكد من إعدادات ACF

## ملاحظات مهمة

- الميزة تعمل فقط مع منشورات من نوع `requirement`
- يتم حفظ الملفات بتنسيق UTF-8 لدعم النصوص العربية
- الملفات تحتوي على معلومات إضافية مثل تاريخ التصدير وإصدار البرنامج
- يتم حذف الملفات القديمة تلقائياً عند تغيير رقم المتطلب أو حذف المنشور

## التطوير المستقبلي

يمكن إضافة المزيد من الميزات مثل:
- تصدير جماعي لجميع المتطلبات
- استيراد من ملفات JSON
- جدولة التصدير التلقائي
- ضغط الملفات القديمة
- إرسال الملفات عبر البريد الإلكتروني
