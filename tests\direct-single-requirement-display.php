<?php
/**
 * Direct test for displaying a single requirement
 * This demonstrates how to display requirement data from JSON
 */

// Set up basic constants
define('ANALYST_PLUGIN_DIR', dirname(__DIR__) . '/');

// Include the classes and functions
require_once ANALYST_PLUGIN_DIR . 'includes/classes/requirements-json-reader.php';

// Mock WordPress functions
if (!function_exists('get_post_type')) {
    function get_post_type($post_id) { return 'requirement'; }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $meta_key, $single = false) {
        if ($meta_key === '_requirement_folder') {
            $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
            $folders = glob($requirements_dir . '*', GLOB_ONLYDIR);
            if (!empty($folders)) {
                return basename($folders[0]);
            }
        }
        return false;
    }
}

if (!function_exists('get_field')) {
    function get_field($field_name, $post_id) { return false; }
}

// Include template functions
require_once ANALYST_PLUGIN_DIR . 'includes/template-functions.php';

// Get requirement ID from URL parameter
$requirement_id = isset($_GET['req_id']) ? htmlspecialchars($_GET['req_id']) : '';

if (empty($requirement_id)) {
    // Show available requirements
    echo '<h1>Available Requirements</h1>';
    echo '<p>Choose a requirement to display:</p>';
    
    $all_requirements = get_all_requirements();
    if (!empty($all_requirements)) {
        echo '<ul>';
        foreach ($all_requirements as $req) {
            $id = isset($req['id']) ? $req['id'] : 'unknown';
            $title = isset($req['title']) ? $req['title'] : 'No Title';
            echo '<li><a href="?req_id=' . urlencode($id) . '">' . htmlspecialchars($id) . ' - ' . htmlspecialchars($title) . '</a></li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No requirements found.</p>';
    }
    exit;
}

// Get requirement data
$requirement = get_requirement_by_id($requirement_id);

if (!$requirement) {
    echo '<h1>Requirement Not Found</h1>';
    echo '<p>Requirement ID "' . htmlspecialchars($requirement_id) . '" was not found.</p>';
    echo '<p><a href="' . $_SERVER['PHP_SELF'] . '">← Back to list</a></p>';
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($requirement['title']); ?> - متطلب <?php echo htmlspecialchars($requirement['id']); ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 3px solid #2196f3;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .requirement-id {
            background: #2196f3;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            border-right: 4px solid #2196f3;
        }
        .section h3 {
            color: #1976d2;
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .list-item {
            background: white;
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 5px;
            border-right: 3px solid #4caf50;
        }
        .use-case {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .use-case h4 {
            color: #1976d2;
            margin-top: 0;
            background: #e3f2fd;
            padding: 10px;
            margin: -20px -20px 15px -20px;
            border-radius: 8px 8px 0 0;
        }
        .field-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        .field-value {
            margin-bottom: 15px;
            padding: 8px;
            background: #f8f8f8;
            border-radius: 4px;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #757575;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .back-link:hover {
            background: #424242;
        }
        .json-source {
            background: #fff3e0;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #e65100;
        }
        .api-endpoint {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .api-method {
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="back-link">← العودة للقائمة</a>
        
        <div class="json-source">
            <strong>📄 مصدر البيانات:</strong> ملف JSON - <?php echo htmlspecialchars($requirement['_folder_name'] ?? 'غير محدد'); ?>/data.json
        </div>

        <div class="header">
            <div class="requirement-id">متطلب <?php echo htmlspecialchars($requirement['id']); ?></div>
            <h1><?php echo htmlspecialchars($requirement['title']); ?></h1>
        </div>

        <?php if (isset($requirement['business_goals']) && is_array($requirement['business_goals']) && !empty($requirement['business_goals'])): ?>
        <div class="section">
            <h3>🎯 أهداف العمل</h3>
            <?php foreach ($requirement['business_goals'] as $goal): ?>
                <div class="list-item">
                    <?php echo htmlspecialchars(is_array($goal) ? $goal['goal'] : $goal); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['stakeholders']) && is_array($requirement['stakeholders']) && !empty($requirement['stakeholders'])): ?>
        <div class="section">
            <h3>👥 الجهات المعنية</h3>
            <?php foreach ($requirement['stakeholders'] as $stakeholder): ?>
                <div class="list-item">
                    <?php echo htmlspecialchars(is_array($stakeholder) ? $stakeholder['stakeholder'] : $stakeholder); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['main_steps']) && is_array($requirement['main_steps']) && !empty($requirement['main_steps'])): ?>
        <div class="section">
            <h3>📋 الخطوات الرئيسية</h3>
            <?php foreach ($requirement['main_steps'] as $index => $step): ?>
                <div class="list-item">
                    <strong><?php echo $index + 1; ?>.</strong> 
                    <?php echo htmlspecialchars(is_array($step) ? $step['step'] : $step); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['user_stories']) && is_array($requirement['user_stories']) && !empty($requirement['user_stories'])): ?>
        <div class="section">
            <h3>📖 قصص المستخدمين</h3>
            <?php foreach ($requirement['user_stories'] as $story): ?>
                <div class="list-item">
                    <?php echo htmlspecialchars(is_array($story) ? $story['story'] : $story); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['use_cases']) && is_array($requirement['use_cases']) && !empty($requirement['use_cases'])): ?>
        <div class="section">
            <h3>🔄 حالات الاستخدام</h3>
            <?php foreach ($requirement['use_cases'] as $index => $use_case): ?>
                <div class="use-case">
                    <h4>حالة الاستخدام <?php echo $index + 1; ?>
                        <?php if (isset($use_case['case_id']) && !empty($use_case['case_id'])): ?>
                            - <?php echo htmlspecialchars($use_case['case_id']); ?>
                        <?php endif; ?>
                    </h4>

                    <?php if (isset($use_case['description']) && !empty($use_case['description'])): ?>
                        <div class="field-label">الوصف:</div>
                        <div class="field-value"><?php echo htmlspecialchars($use_case['description']); ?></div>
                    <?php elseif (isset($use_case['description_simple']) && !empty($use_case['description_simple'])): ?>
                        <div class="field-label">الوصف المبسط:</div>
                        <div class="field-value"><?php echo htmlspecialchars($use_case['description_simple']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['actor']) && !empty($use_case['actor'])): ?>
                        <div class="field-label">الفاعل:</div>
                        <div class="field-value"><?php echo htmlspecialchars($use_case['actor']); ?></div>
                    <?php elseif (isset($use_case['actor_simple']) && !empty($use_case['actor_simple'])): ?>
                        <div class="field-label">الفاعل المبسط:</div>
                        <div class="field-value"><?php echo htmlspecialchars($use_case['actor_simple']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['backend_details']) && is_array($use_case['backend_details'])): ?>
                        <div class="field-label">تفاصيل Backend:</div>
                        <div class="field-value">
                            <?php foreach ($use_case['backend_details'] as $backend): ?>
                                <?php if (isset($backend['api_endpoints']) && is_array($backend['api_endpoints'])): ?>
                                    <h5>API Endpoints:</h5>
                                    <?php foreach ($backend['api_endpoints'] as $endpoint): ?>
                                        <div class="api-endpoint">
                                            <span class="api-method"><?php echo htmlspecialchars($endpoint['method'] ?? 'GET'); ?></span>
                                            <strong><?php echo htmlspecialchars($endpoint['endpoint'] ?? ''); ?></strong>
                                            <?php if (isset($endpoint['description'])): ?>
                                                <br><small><?php echo htmlspecialchars($endpoint['description']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($use_case['frontend_details']) && is_array($use_case['frontend_details'])): ?>
                        <div class="field-label">تفاصيل Frontend:</div>
                        <div class="field-value">
                            <?php foreach ($use_case['frontend_details'] as $frontend): ?>
                                <?php if (isset($frontend['screens']) && is_array($frontend['screens'])): ?>
                                    <h5>الشاشات:</h5>
                                    <?php foreach ($frontend['screens'] as $screen): ?>
                                        <div style="margin: 10px 0;">
                                            <strong><?php echo htmlspecialchars($screen['name'] ?? 'شاشة غير محددة'); ?></strong>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h3>🔧 معلومات تقنية</h3>
            <div class="field-label">مجلد JSON:</div>
            <div class="field-value"><?php echo htmlspecialchars($requirement['_folder_name'] ?? 'غير محدد'); ?></div>
            
            <div class="field-label">عدد الحقول المحملة:</div>
            <div class="field-value"><?php echo count($requirement); ?> حقل</div>
            
            <div class="field-label">مصدر البيانات:</div>
            <div class="field-value">ملفات JSON (ليس قاعدة البيانات)</div>
            
            <div class="field-label">جميع الحقول المتاحة:</div>
            <div class="field-value">
                <details>
                    <summary>عرض جميع الحقول (<?php echo count($requirement); ?> حقل)</summary>
                    <ul style="columns: 3; column-gap: 20px; margin-top: 10px;">
                        <?php foreach (array_keys($requirement) as $field_name): ?>
                            <li><?php echo htmlspecialchars($field_name); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </details>
            </div>
        </div>
    </div>
</body>
</html>
