<?php 
class OpenAIFineTuner {
    private $apiKey;
    private $apiBaseUri = 'https://api.openai.com/v1';
    private $fileId;
    private $fineTuneId;
    private $fineTunedModelId;

    public function __construct() {
        $this->apiKey = '********************************************************';
    }

    private function makeCurlRequest($url, $headers, $postFields = null, $isPost = true) {
        $ch = curl_init($url);
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        if ($isPost) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        }

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Request Error:' . curl_error($ch);
            curl_close($ch);
            return false;
        }

        curl_close($ch);
        return $response;
    }

    public function uploadDataset($filePath) {
        // Retrieve the stored file ID from the database
        $this->fileId = get_option('openai_fine_tune_file_id');

        $file = new CURLFile($filePath, 'application/jsonl', 'dataset.jsonl');

        $uploadHeaders = [
            "Authorization: Bearer $this->apiKey",
            "Content-Type: multipart/form-data"
        ];

        $uploadPostFields = [
            'file' => $file,
            'purpose' => 'fine-tune'
        ];
        if (!$this->fileId) {
            $uploadResponse = $this->makeCurlRequest("$this->apiBaseUri/files", $uploadHeaders, $uploadPostFields);
            if (!$uploadResponse) {
                return false;
            }
            $fileData = json_decode($uploadResponse, true);
            $this->fileId = $fileData['id'];
            // Save the file ID in the WordPress database
            update_option('openai_fine_tune_file_id', $this->fileId);
        }

        return $this->fileId;
    }

    public function startFineTuning($model = 'gpt-3.5-turbo-1106', $n_epochs = 1) {
        // Retrieve the stored file ID from the database
        $this->fileId = get_option('openai_fine_tune_file_id');

        if (!$this->fileId) {
            echo "No valid file ID found. Please upload the dataset again.";
            return false;
        }
        $fineTuneHeaders = [
            "Authorization: Bearer $this->apiKey",
            "Content-Type: application/json"
        ];

        $fineTunePostFields = json_encode([
            'training_file' => $this->fileId,
            'model' => $model,
        ]);

        $fineTuneResponse = $this->makeCurlRequest("$this->apiBaseUri/fine_tuning/jobs", $fineTuneHeaders, $fineTunePostFields);
        if (!$fineTuneResponse) {
            return false;
        }

        $fineTuneData = json_decode($fineTuneResponse, true);
        prr($fineTuneData);
        if (isset($fineTuneData['error']) && !empty( $fineTuneData['error'] )) {
            echo "Error: " . $fineTuneData['error']['message'];
            return false;
        }
        $this->fineTuneId = $fineTuneData['id'];
        return $this->fineTuneId;
    }

    public function monitorFineTuning() {
        $fineTuneHeaders = [
            "Authorization: Bearer $this->apiKey",
            "Content-Type: application/json"
        ];

        while (true) {
            $statusResponse = $this->makeCurlRequest("$this->apiBaseUri/fine_tuning/jobs/$this->fineTuneId", $fineTuneHeaders, null, false);
            if (!$statusResponse) {
                return false;
            }

            $fineTuneStatus = json_decode($statusResponse, true);
            if (isset($fineTuneStatus['error']) && !empty($fineTuneStatus['error'])) {
                echo "Error: " . $fineTuneStatus['error']['message'];
                return false;
            }

            if ($fineTuneStatus['status'] == 'succeeded') {
                $this->fineTunedModelId = $fineTuneStatus['fine_tuned_model'];
                return "Fine-tuning completed successfully! Fine-tuned model ID: " . $this->fineTunedModelId;
            } elseif ($fineTuneStatus['status'] == 'failed') {
                return "Fine-tuning failed.";
            } else {
                echo "Fine-tuning status: " . $fineTuneStatus['status'] . "<br>";
                sleep(60); // Wait for a minute before checking the status again
            }
        }
    }

    public function generateText($prompt, $max_tokens = 100) {
        $generateHeaders = [
            "Authorization: Bearer $this->apiKey",
            "Content-Type: application/json"
        ];

        $generatePostFields = json_encode([
            'model' => 'ft:gpt-3.5-turbo-1106:personal:brd:9SoQoheH',
            'messages' => [["role"=> "user", "content"=> "$prompt"]],
            // 'max_tokens' => $max_tokens
        ]);

        $generateResponse = $this->makeCurlRequest("$this->apiBaseUri/chat/completions", $generateHeaders, $generatePostFields);
        if (!$generateResponse) {
            return false;
        }

        $generateData = json_decode($generateResponse, true);
        // prr($generateData);
        return $generateData['choices'][0]['message']['content'];
    }
}