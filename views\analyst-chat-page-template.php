<!DOCTYPE html>
<html lang="ar" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT RTL</title>
    <!-- إضافة Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style>
        .chat-container {
            max-width: 600px;
            margin: 50px auto;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 10px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        .user-message {
            text-align: right;
            background-color: #d1e7dd;
            margin-left: 10%;
        }
        .bot-message {
            text-align: right;
            background-color: #1e201d;
            color: #f2f2f2;
            margin-right: 10%;
        }
        .readonly {
            background-color: #e9ecef;
        }
    </style>
</head>

<body>
    <div class="container chat-container">
        <div id="chat-history" class="mb-4">
            <!-- سجل المحادثة -->
        </div>
        <div class="form-group">
            <textarea dir="rtl" name="message-input" id="message-input" class="form-control" placeholder="اكتب رسالتك هنا..." rows="10"></textarea>
            <button id="send-button" class="btn btn-primary mt-3">إرسال</button>
        </div>
    </div>

    <!-- إضافة jQuery و Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script>
        $(document).ready(function() {
            $('#send-button').click(function() {
                var message = $('#message-input').val().trim();
                var date_in_seconds = Math.round(new Date().getTime() / 1000);

                if (message === '') return;

                // عرض رسالة المستخدم
                $('#chat-history').append('<div id="message-' + date_in_seconds + '" class="message user-message alert alert-success">' + message + '</div>');

                // جعل خانة الكتابة للقراءة فقط
                $('#message-input').prop('readonly', true).addClass('readonly');

                // إرسال الرسالة عبر AJAX
                $.ajax({
                    url: '<?php echo rest_url(ANALYST_SLUG . '/v1/chat'); ?>',
                    method: 'GET',
                    data: {
                        message: message
                    },
                    success: function(response) {
                        var answer = response.data.message;

                        $('#chat-history').append('<div id="answer-' + date_in_seconds + '" class="message bot-message alert alert-info">' + answer + '</div>');
                        $('#message-input').prop('readonly', false).removeClass('readonly').val('').focus();
                    },
                    error: function() {
                        alert('حدث خطأ أثناء إرسال الرسالة.');
                        $('#message-input').prop('readonly', false).removeClass('readonly');
                    }
                });
            });

            // إرسال الرسالة عند الضغط على Enter
            $('#message-input').keypress(function(e) {
                if (e.which == 13) {
                    $('#send-button').click();
                    return false;
                }
            });
        });
    </script>

</body>
</html>
