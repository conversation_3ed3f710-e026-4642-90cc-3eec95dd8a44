<?php
class JsonLines
{
    private $jsonLines;

    function __construct($jsonContent){
        $this->jsonLines = $this->convertJsonToJsonLines($jsonContent);
    }

    function convertJsonToJsonLines($jsonContent) {
        // Decode the JSON content
        $data = json_decode($jsonContent, true);
        
        // Prepare the JSON lines array
        $jsonLines = [];

        // Process each key-value pair in the JSON data
        foreach ($data as $key => $value) {
            $messages = [];
            // Add main content as the first line
            $messages[] = ["role" => "system", "content" => "$key " . $value["title"]];
    
            // Add each section to the messages
            $this->addSectionToLines($messages, "ما هي الخطوات الرئيسية لـ" . $value["title"] . "؟", $value["main_steps"]);
            $this->addSectionToLines($messages, "ما هي الخطوات البديلة لـ" . $value["title"] . "؟", $value["alternative_steps"]);
            $this->addSectionToLines($messages, "ما هي أهداف " . $value["title"] . "؟", $value["business_goals"]);
            $this->addSectionToLines($messages, "ما هي قصص المستخدمين المتعلقة بـ" . $value["title"] . "؟", $value["user_stories"]);
            $this->addSectionToLines($messages, "ما هي مؤشرات الأداء لـ" . $value["title"] . "؟", $value["performance_indicators"]);
            
            // Add the messages to the JSON lines array
            $jsonLines[] = json_encode(["messages" => $messages], JSON_UNESCAPED_UNICODE);
        }

        return implode("\n", $jsonLines);
    }

    private function addSectionToLines(&$messages, $question, $contentArray) {
        $messages[] = ["role" => "user", "content" => $question];
        $content = implode(" ", $contentArray);
        $messages[] = ["role" => "assistant", "content" => $content];
    }

    function getJsonLines() {
        return $this->jsonLines;
    }

    function saveToFile($filename) {
        file_put_contents(ANALYST_PLUGIN_DIR . 'data/' . $filename, $this->jsonLines);
    }
}