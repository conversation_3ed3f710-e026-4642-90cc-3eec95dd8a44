<?php

function generate_table_row($case){

    
}

function not_allowed( $label ){
    $not_allowedd =  array(
        'case_id',
        'id',
        'is_new',
        'description_simple',
        'actor_simple',
        'preconditions_simple',
        'postconditions_simple',
        'main_flow_steps_simple',
        'trigger',
        'frequency_of_use',
        'flowchart_mermaid',
        'رقم التسلسل',
        'priority',
        'main_flow_steps'
    );

    if( in_array( $label, $not_allowedd ) ) {
        return true;
    }
}

function label_handler( $label ){
    $array= array(
        'goal' => false,
        'stakeholder' => false,
        'step' => false,
        'story' => false,
        'indicators' => false,
        'condition' => false,
        'rule' => false,
        'assumption' => false,
        'event' => false,
        'requirement' => false,
        'name' => false,
        'method' => false,
        'scenario' => false,
        'input' => false,
        'system' => false,
    );

    $enable_translate =  false;
    if( $enable_translate ){
        $array = array_merge( $array , array(
            'case_id' => 'الحالة رقم',
            'description' => 'الوصف',
            'actor' => 'الشخصية',
            'preconditions' => 'الظروف المسبقة',
            'postconditions' => 'النتائج',
            'main_flow_steps' => 'الخطوات الأساسية', 
            'alternative_flow_steps' => 'الخطوات البديلة',
            'exception_flow_steps' => 'خطوات استثنائية',
            'steps'=> 'الخطوات',
            'priority' => 'الأولوية',
            'business_rules' => 'اشتراطات العمل',
            'assumptions' => 'الافتراضات المسبقة',
            'special_requirements' => 'متطلبات خاصة',
            'notes_and_issues' => 'ملاحظات',
            'events_sequence' => 'تسلسل الأحداث',
            'inputs' => 'المدخلات',
            'outputs' => 'المخرجات',
            'user_interactions' => 'التفاعلات المستخدم',
            'special_conditions' => 'حالات خاصة',
            'usage_scenarios' => 'سيناريوهات الاستخدام',
            'security_requirements' => 'متطلبات الامنية',
            'integration_with_other_systems' => 'التكامل مع الأنظمة الأخرى',
            'constraints_and_assumptions'=> 'الحدود والافتراضات',
            'testing_requirements' => 'متطلبات الاختبار',
            'backend_details' => 'تفاصيل ال API',
            'frontend_details' => 'تفاصيل الواجهات',
            'method' => 'الطريقة',
            'endpoint' => 'رابط النقطة',
            'request' => 'تفاصيل الطلب',
            'screens' => 'الشاشات',
            'components' => 'مكونات الشاشة',
            'notifications' => 'الاشعارات',
            'methods' => 'عن طريق',
            'recipient' => 'المستلم',
            'title' => 'عنوان',
            'message' => 'نص الاشعار',
            'type' => 'النوع',
            'props' => 'الخصائص',
            'response' => 'الاستجابة',
            'body' => 'المحتوى',
            'headers' => 'مرفقات',
            'status_code' => 'كود الطلب',
            'الجهات المعنية' => 'المستخدمين',
    
        ));
    }

    if( isset( $array[$label] ) ){
        return $array[ $label ];
    }

    return $label;
}
function value_handler( $label ,  $value ){

    if( $label == 'alternative_flow_steps' ){
        return $value['steps'];
    }

    if( $label == 'backend_details' ){
        return $value[0]['api_endpoints'];
    }

    return $value;
}
// دالة لعرض الحقول النصية
function display_text_field($value , $label = false ) {

    $label = label_handler( $label );

    if( $label ){
        echo "<strong>$label:</strong> $value<br>";
    }else {
        echo "$value<br>";
    }
}
function generateTableForCases($data) {
    if (empty($data)) {
        return "<p>No data available</p>";
    }

    
    $id =  get_field('id');
    $id =  "3. .$id .2 ";
    
    foreach( $data as $case_id =>   $case ){
        echo "<h4>$id".".". ($case_id +1) ."</h4>";
        echo "
        <table>
            <thead>
                <tr>
                    <th>العنوان</th>
                    <th>التفاصيل</th>
                </tr>
            </thead>
            <tbody>
        ";

        foreach($case as $sub_label => $sub_value){

            if( not_allowed( $sub_label ) ) {
                continue;
            }

            if( empty( $sub_value ) ) {
                continue;
            }

            if( $sub_label === 'backend_details'){
                //
            }elseif( $sub_label === 'frontend_details' ){
                //
            }elseif( $sub_label === 'notifications' ){
                //
            }else {
                echo '<tr>';
                    echo  "<td>".label_handler($sub_label)."</td>";
                    echo  "<td>";
                            generate_main_case_info_value( $sub_label , $sub_value );
                    echo"</td>";
                echo '</tr>';
            }
        // generate_table_row($case);
        // generate table footer
        }
        echo "
        </tbody>
        </table>
        ";

    }
}
function generate_main_case_info_repeater_value( $repeater ){

    echo "<ul>";
    foreach ($repeater as $item) {
        echo "<li>";
        foreach ($item as $sub_label => $sub_value) {

            if( not_allowed( $sub_label ) ) {
                continue;
            }
            if(empty($sub_value)) {
                continue;
            }
            $sub_value = value_handler( $sub_label, $sub_value );

            if( $sub_label === 'تفاصيل ال API')
            {
                //
            }
            elseif ( is_array($sub_value) ) {
                display_repeater_field($sub_value , $sub_label );
            } elseif ( is_string( $sub_value ) ) {
                display_text_field($sub_value , $sub_label);
            }else {
                // if no value then skip
                // now we show it for translating
                //display_text_field($sub_value , $sub_label);
            }
        }
        echo "</li>";
    }
    echo "</ul>";

}




// دالة لعرض الحقول المتكررة (repeater fields)
function display_repeater_field( array $repeater , $label =false) {
    if( not_allowed( $label ) ){
        return;
    }
    $label = label_handler( $label );


    echo "<tr><td>$label</td>";
    
    echo "<td><ul>";
    foreach ($repeater as $item) {
        echo "<li>";
        foreach ($item as $sub_label => $sub_value) {

            if( not_allowed( $sub_label ) ) {
                continue;
            }
            if(empty($sub_value)) {
                continue;
            }
            $sub_value = value_handler( $sub_label, $sub_value );

            if( $sub_label === 'تفاصيل ال API')
            {
                //
            }
            elseif ( is_array($sub_value) ) {
                display_repeater_field($sub_value , $sub_label );
            } elseif ( is_string( $sub_value ) ) {
                display_text_field($sub_value , $sub_label);
            }else {
                // if no value then skip
                // now we show it for translating
                //display_text_field($sub_value , $sub_label);
            }
        }
        echo "</li>";
    }
    echo "</ul></td></tr>";

}
function generate_main_case_info_value( $sub_label , $sub_value ){

    if( is_string(( $sub_value))){
        echo $sub_value;
    }elseif( is_array( $sub_value ) ) {
        generate_main_case_info_repeater_value( $sub_value );         
    }
}

function display_acf_fields($post_id) {
    $fields = get_field_objects($post_id);
    $cases = [];
    ?>
    <table>
        <thead>
            <tr>
                <th>العنوان</th>
                <th>التفاصيل</th>
            </tr>
        </thead>
        <tbody>
        <?php
        foreach ($fields as $field_name => $field) {
            $label = $field['label'];
            $value = $field['value'];

            if( not_allowed( $label ) ){
                continue;
            }
            if( $field['name'] == 'use_cases'){
                $cases = $field;
                continue;

            }

            display_repeater_field( $value, $label);
            
           
        }
        ?>
        </tbody>
    </table>
        <h3>
            <?php
            $id =  get_field('id');
            $id =  "3. $id ";
            
            echo "$id".".2 حالات الإستخدام"
            ?>
        </h3>    
    <?php
    $cases = $fields['use_cases']['value'];

    generateTableForCases($cases);

        
        
       
   
}

// التأكد من وجود منشور
if (have_posts()) : 
    while (have_posts()) : the_post(); ?>
        
    <div style="direction: rtl;">
        <h2>
            <?php
                $id =  get_field('id');
                $id =  "3. $id ";
                echo $id;
                the_title();
            ?>
        </h2>
        <h3>
            <?php
            echo "$id".".1 التفاصيل العامة</h3>"
            ?>
        </h3>

        <style>
.table_component {
    overflow: auto;
    width: 100%;
}

.table_component table {
    border: 1px solid #dededf;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 1px;
}

.table_component caption {
    caption-side: top;
}

.table_component th {
    border: 1px solid #dededf;
    background-color: #eceff1;
    color: #000000;
    padding: 0px;
}

.table_component td {
    border: 1px solid #dededf;
    background-color: #ffffff;
    color: #000000;
    margin: 0px;
}
ul{
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 0px;
    padding-bottom: 0px;
}

</style>
    <div class="table_component" role="region" tabindex="0">
        <?php display_acf_fields(get_the_ID()); ?>
    </div>
    </div>
    <?php endwhile;
else :
    echo 'لا توجد منشورات لعرضها.';
endif;