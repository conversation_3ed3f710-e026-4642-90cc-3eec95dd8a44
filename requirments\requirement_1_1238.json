{"requirement_id": "1", "wordpress_post_id": 1238, "export_info": {"plugin_version": "0.0.2", "export_date": "2025-06-07T01:35:55+00:00", "wordpress_version": "6.8.1"}, "requirement_data": {"post_id": 1238, "title": "التسجيل في المنصة", "slug": "%d8%a7%d9%84%d8%aa%d8%b3%d8%ac%d9%8a%d9%84-%d9%81%d9%8a-%d8%a7%d9%84%d9%85%d9%86%d8%b5%d8%a9", "status": "publish", "created_date": "2024-06-10 16:41:47", "modified_date": "2025-06-07 01:35:52", "export_date": "2025-06-07 01:35:55", "id": "1", "business_goals": ["التأكد من أن كل مستخدم للمنصة قادر على تقديم الخدمة أو طلب الخدمة بطريقة آمنة وفعالة ...", "<PERSON><PERSON><PERSON> أن المعلومات المقدمة من المستخدمين صحيحة وحديثة"], "stakeholders": ["المستخدمون (العملاء، مقدمو الخدمات)", "إداريي النظام"], "main_steps": ["المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته", "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية", "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لاداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "المستخدم يتلقى إشعارات النظام (أو البريد في حالة استخدامه) تحتوي على تحديث حالة التسجيل (مقبول، مرفوض وسبب الرفض)"], "alternative_steps": ["إذا تم رفض حساب المستخدم، المستخدم يمكنه المراجعة والتعديل وإعادة تقديم الطلب"], "user_stories": ["كمستخدم عميل، أريد أن أقوم بالتسجيل في التطبيق بسرعة وسهولة حتى أتمكن من البدء في استخدام المنصة ، من أجل استخدام الخدمات المتوفرة", "كمقدم خدمة أريد أن أقوم بالتسجيل في التطبيق، من أجل عرض خدماتي و الحصول على فرص عمل جديدة وبناء شراكات", "كمستخدم، أنا بحاجة لتوثيق رقم الهاتف الخاص بي لضمان أمان حسابي", "كمستخدم، أنا بحاجة لتقديم وثائق تحقيق الشخصية الخاصة بي للتأكد من اعتمادي من النظام", "موظف إداري في المنصة، أرغب في تلقي إشعارات عند تسجيل مستخدم جديد أو مقدم خدمة جديد حتى أتمكن من فحص وثائقهم والتحقق منها للتأكد من التزام المنصة بمعايير الجودة والسلامة", "موظف إداري في المنصة، يحتاج إلى مراجعة طلبات الالتحاق, مراجعتها إما القبول أو وضع سبب الرفض", "كمستخدم، أريد تلقي تحديثات حول حالة طلب التسجيل الخاص بي، من أجل معرفة ما إذا كان مقبولًا أم مرفوضًا أم في انتظار المراجعة"], "performance_indicators": ["عدد المسجلين مع تصنيف الفئات (تاجر، ناقل، وسيط....../ تسجيل مقبول، تسجيل مرفوض، إعادة تسجيل....)", "الفترة الزمنية للتسجيل"], "use_cases": [{"is_new": false, "case_id": "1.1", "description_simple": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته", "actor_simple": "المستخدم (العميل أو مقدم الخدمة أو الناقل)", "description": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق إدخال بياناته الشخصية وإنشاء حساب جديد", "actor": "المستخدم (العميل أو مقدم الخدمة)", "trigger": "المستخدم يرغب في إنشاء حساب جديد", "priority": "عالي", "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "preconditions_simple": ["يجب أن يكون المستخدم غير مسجل سابقًا في النظام"], "postconditions_simple": ["المستخدم يكون لديه حساب جديد في النظام"], "main_flow_steps_simple": ["فتح صفحة التسجيل", "إدخال البيانات الشخصية المطلوبة ", "قراءة سياسة الخصوصية والموافقة عليها", "النقر على زر 'تسجيل'"], "preconditions": ["المستخدم غير مسجل سابقًا في النظام"], "postconditions": ["المستخدم يكون لديه حساب جديد في النظام"], "main_flow_steps": ["فتح صفحة التسجيل", "إدخال البيانات الشخصية المطلوبة (الاسم، البريد الإلكتروني، رقم الهاتف، كلمة المرور)", "قراءة سياسة الخصوصية والموافقة عليها", "النقر على زر 'تسجيل'", "النظام يتحقق من صحة البيانات المدخلة", "النظام ينشئ حسابًا جديدًا للمستخدم"], "events_sequence": ["المستخدم يفتح صفحة التسجيل", "المستخدم يدخل البيانات الشخصية المطلوبة", "المستخدم يقرأ  سياسة الخصوصية ويوافق عليها", "المستخدم ينقر على زر 'تسجيل'", "النظام يتحقق من صحة البيانات", "النظام ينشئ حسابًا جديدًا للمستخدم"], "business_rules": ["يجب أن تكون كلمة المرور قوية (تحتوي على حروف وأرقام ورموز)"], "assumptions": ["المستخدم (الناق<PERSON> ) يمتلك رقم هاتف صالح", "باقي المستخدمين يمتلكون رقم هاتف وبريد الكتروني صالحين"], "alternative_flow_steps": [{"condition": "إذا كانت البيانات المدخلة غير صحيحة", "steps": ["النظام يعرض رسالة خطأ توضح البيانات غير الصحيحة", "المستخدم يعدل البيانات المدخلة", "النقر على زر 'تسجيل' مرة أخرى"]}], "exception_flow_steps": [{"condition": "إذا كان المستخدم مسجل مسبقًا", "steps": ["النظام يعرض رسالة تفيد بأن البريد الإلكتروني أو رقم الهاتف مستخدم مسبقًا", "المستخدم يمكنه استخدام خيار 'نسيت كلمة المرور' لاستعادة الوصول إلى الحساب"]}]}, {"is_new": false, "case_id": "1.2", "description_simple": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP", "actor_simple": "المستخدم", "description": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لإداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "actor": "المستخدم", "trigger": "المستخدم يرغب في توثيق رقم هاتفه كجزء من عملية التسجيل", "priority": "عالي", "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "preconditions_simple": ["أن يمتلك المستخدم رقم هاتف صحيح", "إدخال رقم الهاتف بشكل صحيح"], "postconditions_simple": ["تم توثيق رقم الهاتف بنجاح"], "main_flow_steps_simple": ["إدخال رقم الهاتف", "النقر على زر 'إرسال OTP'", "استلام رسالة OTP على الهاتف", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'توثيق'"], "preconditions": ["إكمال خطوات التسجيل السابقة", "فتح صفحة توثيق رقم الهاتف"], "postconditions": ["تم توثيق رقم الهاتف بنجاح"], "main_flow_steps": ["المستخدم يفتح صفحة توثيق رقم الهاتف", "إدخال رقم الهاتف", "النقر على زر 'إرسال رمز التحقق'", "استلام رسالة OTP على الهاتف المدخل", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'تحقق'"], "events_sequence": ["المستخدم يفتح صفحة توثيق رقم الهاتف", "إدخال رقم الهاتف", "النقر على زر 'إرسال رمز التحقق'", "استلام رسالة OTP على الهاتف المدخل", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'تحقق'"], "assumptions": ["المستخدم يمتلك رقم هاتف صالح ويستطيع استلام رسائل OTP"], "alternative_flow_steps": [{"condition": "إذا لم يستلم المستخدم رسالة OTP", "steps": ["النقر على زر 'إعادة إرسال الرمز'", "استلام رمز OTP جديد", "إدخال الرمز الجديد في الحقل المخصص", "النقر على زر 'تحقق'"]}], "exception_flow_steps": [{"condition": "إذا أدخل المستخدم رمز OTP غير صحيح", "steps": ["النظام يعرض رسالة خطأ تطلب إدخال الرمز الصحيح", "إعادة إدخال رمز OTP صحيح", "النقر على زر 'تحقق' مرة أخرى"]}]}, {"is_new": false, "case_id": "1.3", "description_simple": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "actor_simple": "المستخدم", "description": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "actor": "المستخدم", "trigger": "النظام يطلب من المستخدم وثائق تعريفية", "priority": "عالي", "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "preconditions_simple": ["إكمال خطوات التسجيل الأساسية", "توافر الوثائق المطلوبة"], "postconditions_simple": ["تم تحميل الوثائق التعريفية المطلوبة"], "main_flow_steps_simple": ["اختيار نوع الوثيقة التعريفية", "تحميل صورة أو نسخة من الوثيقة", "النقر على زر 'تحميل الوثيقة'"], "preconditions": ["إكمال خطوات التسجيل السابقة", "توافر الوثائق المطلوبة "], "postconditions": ["تم تحميل الوثائق المطلوبة "], "main_flow_steps": ["المستخدم يكمل خطوات التسجيل", "المستخدم يوثق رقم الهاتف عبر OTP ", "النظام يطلب من المستخدم تحميل وثائق تعريفية ", "المستخدم يرفع المستندات المطلوبة ", "النظام يراجع المستندات"], "events_sequence": ["النظام يطلب تحميل وثائق تعريفية", "المستخدم يقوم بتحميل الوثائق التعريفية", "النظام يتحقق من صحة المستندات"], "assumptions": ["المستخدم يمتلك الوثائق اللازمة "], "alternative_flow_steps": [{"condition": "إذا  كان هناك خطأ في مستند", "steps": ["النظام يرسل رسالة بالخطأ للمستخدم", "المستخدم يقوم بإعادة تحميل الوثائق المطلوبة", "النظام يتحقق من الوثائق مرة أخرى"]}]}, {"is_new": false, "case_id": "1.4", "description_simple": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل", "actor_simple": "المستخدم", "description": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل سواء كان مقبولًا أو مرفوضًا مع سبب الرفض إذا كان متاحًا", "actor": "المستخدم", "trigger": "إكمال المستخدم لخطوات التسجيل وانتظار الموافقة", "priority": "عالي", "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "preconditions_simple": ["إكمال جميع خطوات التسجيل السابقة"], "postconditions_simple": ["تم إشعار المستخدم بحالة التسجيل"], "main_flow_steps_simple": ["انتظار مراجعة وثائق التسجيل من قبل النظام", "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)", "متابعة التعليمات حسب حالة التسجيل"], "preconditions": ["إكمال جميع خطوات التسجيل السابقة"], "postconditions": ["تم إشعار المستخدم بحالة التسجيل"], "main_flow_steps": ["انتظار مراجعة وثائق التسجيل من قبل النظام", "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)", "متابعة التعليمات حسب حالة التسجيل"], "events_sequence": ["النظام يراجع وثائق التسجيل", "النظام يرسل إشعارًا للمستخدم بحالة التسجيل", "المستخدم يتلقى الإشعار ويتابع التعليمات المناسبة"], "assumptions": ["النظام يقوم بمراجعة الوثائق في وقت مناسب"]}, {"is_new": false, "case_id": "1.5", "description_simple": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor_simple": "المستخدم", "description": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor": "المستخدم", "trigger": "استلام إشعار برفض الحساب", "priority": "عالي", "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "preconditions_simple": ["استلام إشعار برفض الحساب", "توافر سبب الرفض"], "postconditions_simple": ["إعاده تقديم طلب التسجيل بعد التعديل"], "main_flow_steps_simple": ["قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "preconditions": ["استلام إشعار برفض الحساب", "توافر سبب الرفض"], "postconditions": ["إعادة تقديم طلب التسجيل بعد التعديل"], "main_flow_steps": ["قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "events_sequence": ["استلام إشعار برفض الحساب", "قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "assumptions": ["المستخدم يمكنه تعديل البيانات أو الوثائق المطلوبة"]}]}}