<?php $step = 3; ?>
<?php

function validate_advanced_use_case($use_case, $use_case_id) {
    $schema = [
        "type" => "object",
        "properties" => [
            "description" => ["type" => "string"],
            "actor" => ["type" => "string"],
            "preconditions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "postconditions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "main_flow_steps" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "alternative_flow_steps" => [
                "type" => "array",
                "items" => [
                    "type" => "object",
                    "properties" => [
                        "condition" => ["type" => "string"],
                        "steps" => [
                            "type" => "array",
                            "items" => ["type" => "string"]
                        ]
                    ],
                    "required" => ["condition", "steps"]
                ]
            ],
            "exception_flow_steps" => [
                "type" => "array",
                "items" => [
                    "type" => "object",
                    "properties" => [
                        "condition" => ["type" => "string"],
                        "steps" => [
                            "type" => "array",
                            "items" => ["type" => "string"]
                        ]
                    ],
                    "required" => ["condition", "steps"]
                ]
            ],
            "trigger" => ["type" => "string"],
            "priority" => ["type" => "string"],
            "business_rules" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "assumptions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "frequency_of_use" => ["type" => "string"],
            "special_requirements" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "notes_and_issues" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "events_sequence" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "inputs" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "outputs" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "user_interactions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "special_conditions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "usage_scenarios" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "security_requirements" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "integration_with_other_systems" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "constraints_and_assumptions" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "testing_requirements" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "backend_details" => [
                "type" => "object",
                "properties" => [
                    "api_endpoints" => [
                        "type" => "array",
                        "items" => [
                            "type" => "object",
                            "properties" => [
                                "method" => ["type" => "string"],
                                "endpoint" => ["type" => "string"],
                                "description" => ["type" => "string"],
                                "request" => [
                                    "type" => "object",
                                    "properties" => [
                                        "body" => ["type" => "object"],
                                        "headers" => ["type" => "object"]
                                    ],
                                    "required" => ["body", "headers"]
                                ],
                                "response" => ["type" => "object"]
                            ],
                            "required" => ["method", "endpoint", "description", "request", "response"]
                        ]
                    ]
                ],
                "required" => ["api_endpoints"]
            ],
            "frontend_details" => [
                "type" => "object",
                "properties" => [
                    "screens" => [
                        "type" => "array",
                        "items" => [
                            "type" => "object",
                            "properties" => [
                                "name" => ["type" => "string"],
                                "components" => [
                                    "type" => "array",
                                    "items" => ["type" => "object"]
                                ]
                            ],
                            "required" => ["name", "components"]
                        ]
                    ]
                ],
                "required" => ["screens"]
            ],
            "notifications" => [
                "type" => "array",
                "items" => [
                    "type" => "object",
                    "properties" => [
                        "methods" => [
                            "type" => "array",
                            "items" => ["type" => "string"]
                        ],
                        "recipient" => ["type" => "string"],
                        "title" => ["type" => "string"],
                        "message" => ["type" => "string"]
                    ],
                    "required" => ["methods", "recipient", "title", "message"]
                ]
            ],
            "flowchart_mermaid" => ["type" => "string"]
        ],
        "required" => ["description", "actor", "preconditions", "postconditions", "main_flow_steps", "alternative_flow_steps", "exception_flow_steps", "trigger", "priority", "business_rules", "assumptions", "frequency_of_use", "special_requirements", "notes_and_issues", "events_sequence", "inputs", "outputs", "user_interactions", "special_conditions", "usage_scenarios", "security_requirements", "integration_with_other_systems", "constraints_and_assumptions", "testing_requirements", "backend_details", "frontend_details", "notifications", "flowchart_mermaid"]
    ];

    return validate_json_schema($use_case, $schema, $use_case_id);
}

function validate_json_schema($data, $schema, $use_case_id) {
    $is_valid = true;
    $errors = [];

    foreach ($schema['required'] as $field) {
        if (!is_array($data) || !array_key_exists($field, $data)) {
            $errors[] = "Missing required field: $field (ID: $use_case_id)";
            $is_valid = false;
        }
    }

    foreach ($schema['properties'] as $key => $property) {
        if (array_key_exists($key, $data)) {
            if ($property['type'] === 'array') {
                if (!is_array($data[$key])) {
                    $errors[] = "Invalid type for field: $key. Expected array, got " . gettype($data[$key]) . " (ID: $use_case_id)";
                    $is_valid = false;
                } else {
                    foreach ($data[$key] as $item) {
                        if ($property['items']['type'] === 'object') {
                            if (!is_array($item) && !is_object($item)) {
                                $errors[] = "Invalid type for items in array field: $key. Expected object, got " . gettype($item) . " (ID: $use_case_id)";
                                $is_valid = false;
                            }
                        } elseif (gettype($item) !== $property['items']['type']) {
                            $errors[] = "Invalid type for items in array field: $key. Expected " . $property['items']['type'] . ", got " . gettype($item) . " (ID: $use_case_id)";
                            $is_valid = false;
                        }
                    }
                }
            } elseif ($property['type'] === 'object') {
                if (!is_array($data[$key]) && !is_object($data[$key])) {
                    $errors[] = "Invalid type for field: $key. Expected object, got " . gettype($data[$key]) . " (ID: $use_case_id)";
                    $is_valid = false;
                }
            } elseif (gettype($data[$key]) !== $property['type']) {
                $errors[] = "Invalid type for field: $key. Expected " . $property['type'] . ", got " . gettype($data[$key]) . " (ID: $use_case_id)";
                $is_valid = false;
            }
        }
    }

    foreach ($data as $key => $value) {
        if (!array_key_exists($key, $schema['properties'])) {
            $errors[] = "Unexpected field: $key (ID: $use_case_id)";
            $is_valid = false;
        }
    }

    foreach ($errors as $error) {
        echo $error . "<br>";
    }

    return $is_valid;
}

$file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';

if (!file_exists($file_path)) {
    die("Error: File not found.");
}

$file_contents = file_get_contents($file_path);

if ($file_contents === false) {
    die("Error: Unable to read the file.");
}

$json_data = json_decode($file_contents, true);

if ($json_data === null) {
    die("Error: Invalid JSON data.");
}
// ترجمة المفاتيح في advanced_use_cases
foreach ($json_data as $section => $content) {
    if (is_array($content) && isset($content['advanced_use_cases']) && is_array($content['advanced_use_cases'])) {
        foreach ($content['advanced_use_cases'] as $use_case_id => $use_case) {
            if( validate_advanced_use_case($use_case, $use_case_id) ) {
                echo "<b>$section</b> is valide advanced_use_case ID: $use_case_id<hr>";
                continue;
            }
            if (!validate_advanced_use_case($use_case, $use_case_id)) {
                // prr($use_case_id);
                echo "<b>$section</b> advanced_use_case ID: $use_case_id<hr>";
            } 
        }
    }
}
?>




<?php
if( $step == 2){
    function validate_use_case($use_case) {
        $schema = [
            "type" => "object",
            "properties" => [
                "id" => ["type" => "string"],
                "description" => ["type" => "string"],
                "actor" => ["type" => "string"],
                "preconditions" => [
                    "type" => "array",
                    "items" => ["type" => "string"]
                ],
                "postconditions" => [
                    "type" => "array",
                    "items" => ["type" => "string"]
                ],
                "main_flow_steps" => [
                    "type" => "array",
                    "items" => ["type" => "string"]
                ]
            ],
            "required" => ["id", "description", "actor", "preconditions", "postconditions", "main_flow_steps"]
        ];
    
        return validate_json_schema($use_case, $schema);
    }
    
    function validate_json_schema($data, $schema) {
        $is_valid = true;
        $errors = [];
    
        foreach ($schema['required'] as $field) {
            if (!is_array($data) || !array_key_exists($field, $data)) {
                $errors[] = "Missing required field: $field";
                $is_valid = false;
            }
        }
    
        foreach ($schema['properties'] as $key => $property) {
            if (array_key_exists($key, $data)) {
                if ($property['type'] === 'array') {
                    if (!is_array($data[$key])) {
                        $errors[] = "Invalid type for field: $key. Expected array, got " . gettype($data[$key]);
                        $is_valid = false;
                    } else {
                        foreach ($data[$key] as $item) {
                            if ($property['items']['type'] === 'object') {
                                if (!is_array($item) && !is_object($item)) {
                                    $errors[] = "Invalid type for items in array field: $key. Expected object, got " . gettype($item);
                                    $is_valid = false;
                                }
                            } elseif (gettype($item) !== $property['items']['type']) {
                                $errors[] = "Invalid type for items in array field: $key. Expected " . $property['items']['type'] . ", got " . gettype($item);
                                $is_valid = false;
                            }
                        }
                    }
                } elseif ($property['type'] === 'object') {
                    if (!is_array($data[$key]) && !is_object($data[$key])) {
                        $errors[] = "Invalid type for field: $key. Expected object, got " . gettype($data[$key]);
                        $is_valid = false;
                    }
                } elseif (gettype($data[$key]) !== $property['type']) {
                    $errors[] = "Invalid type for field: $key. Expected " . $property['type'] . ", got " . gettype($data[$key]);
                    $is_valid = false;
                }
            }
        }
    
        foreach ($data as $key => $value) {
            if (!array_key_exists($key, $schema['properties'])) {
                $errors[] = "Unexpected field: $key";
                $is_valid = false;
            }
        }
    
        foreach ($errors as $error) {
            echo $error . "<br>";
        }
    
        return $is_valid;
    }
    
    $file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
    
    if (!file_exists($file_path)) {
        die("Error: File not found.");
    }
    
    $file_contents = file_get_contents($file_path);
    
    if ($file_contents === false) {
        die("Error: Unable to read the file.");
    }
    
    $json_data = json_decode($file_contents, true);
    
    if ($json_data === null) {
        die("Error: Invalid JSON data.");
    }
    
    foreach ($json_data as $section => $content) {
        if (is_array($content)) {
            if (isset($content['use_cases']) && is_array($content['use_cases'])) {
                foreach ($content['use_cases'] as $key => $use_case) {
                    if (!validate_use_case($use_case)) {
                        echo "<b>$section</b> use_case key: $key<hr>";
                    }
                }
            } else {
                echo "Invalid or missing use_cases in section: $section<hr>";
            }
        } else {
            echo "Invalid content format in section: $section<hr>";
        }
    }    
}
?>

<?php 
if( $step == 1 ){
    // دالة للتحقق من صحة الحقول المطلوبة ونوع البيانات، وعدم وجود حقول إضافية
    function validate_json_schema($data, $schema) {
        $is_valid = true;
        $errors = [];

        // التحقق من الحقول المطلوبة
        foreach ($schema['required'] as $field) {
            if (!is_array($data) || !array_key_exists($field, $data)) {
                $errors[] = "Missing required field: $field";
                $is_valid = false;
            }
        }

        // التحقق من نوع الحقول
        foreach ($schema['properties'] as $key => $property) {
            if (array_key_exists($key, $data)) {
                if ($property['type'] === 'array') {
                    if (!is_array($data[$key])) {
                        $errors[] = "Invalid type for field: $key. Expected array, got " . gettype($data[$key]);
                        $is_valid = false;
                    } else {
                        // التحقق من نوع العناصر في المصفوفة
                        foreach ($data[$key] as $item) {
                            if ($property['items']['type'] === 'object') {
                                // لا تحقق من صحة البنية الداخلية للعناصر الفرعية هنا
                                if (!is_array($item) && !is_object($item)) {
                                    $errors[] = "Invalid type for items in array field: $key. Expected object, got " . gettype($item);
                                    $is_valid = false;
                                }
                            } elseif (gettype($item) !== $property['items']['type']) {
                                $errors[] = "Invalid type for items in array field: $key. Expected " . $property['items']['type'] . ", got " . gettype($item);
                                $is_valid = false;
                            }
                        }
                    }
                } elseif ($property['type'] === 'object') {
                    if (!is_array($data[$key]) && !is_object($data[$key])) {
                        $errors[] = "Invalid type for field: $key. Expected object, got " . gettype($data[$key]);
                        $is_valid = false;
                    }
                } elseif (gettype($data[$key]) !== $property['type']) {
                    $errors[] = "Invalid type for field: $key. Expected " . $property['type'] . ", got " . gettype($data[$key]);
                    $is_valid = false;
                }
            }
        }

        // التحقق من عدم وجود حقول إضافية
        foreach ($data as $key => $value) {
            if (!array_key_exists($key, $schema['properties'])) {
                $errors[] = "Unexpected field: $key";
                $is_valid = false;
            }
        }

        foreach ($errors as $error) {
            echo $error . "<br>";
        }

        return $is_valid;
    }

    // المخطط المتوقع للعناصر الأساسية داخل المتطلب
    $schema = [
        "type" => "object",
        "properties" => [
            "title" => ["type" => "string"],
            "business_goals" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "stakeholders" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "main_steps" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "alternative_steps" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "user_stories" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "performance_indicators" => [
                "type" => "array",
                "items" => ["type" => "string"]
            ],
            "use_cases" => [
                "type" => "array",
                "items" => ["type" => "object"]
            ],
            "advanced_use_cases" => [
                "type" => "array",
                "items" => ["type" => "object"]
            ]
        ],
        "required" => ["title", "business_goals", "stakeholders", "main_steps", "use_cases"]
    ];

    $file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';

    // تحقق من وجود الملف وقراءته
    if (!file_exists($file_path)) {
        die("Error: File not found.");
    }

    $file_contents = file_get_contents($file_path);

    if ($file_contents === false) {
        die("Error: Unable to read the file.");
    }

    $json_data = json_decode($file_contents, true);

    if ($json_data === null) {
        die("Error: Invalid JSON data.");
    }

    function validate_elements($elements, $schema, $section) {
        foreach ($elements as $key => $element) {
            if (!validate_json_schema($element, $schema)) {
                echo " <b> $section</b> key: $key<hr>";
            }
        }
    }

    foreach ($json_data as $section => $content) {
        if (is_array($content)) {
            validate_elements([$content], $schema, $section);
        } else {
            echo "Invalid content format in section: $section<hr>";
        }
    }

}
?>

