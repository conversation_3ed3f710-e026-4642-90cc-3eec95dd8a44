<?php

/**
 * Class to handle saving requirement custom fields as JSON files
 */
class Save_Requirements_JSON {

    public function __construct() {
        // Hook into post save action for requirement post type
        add_action('acf/save_post', array($this, 'save_requirement_as_json'), 20);
        add_action('before_delete_post', array($this, 'delete_requirement_json'));

        // Prevent ACF fields from being saved to database for requirements
        add_filter('acf/pre_update_value', array($this, 'prevent_acf_database_save'), 10, 4);
    }

    /**
     * Save requirement custom fields as JSON when post is saved
     *
     * @param int $post_id The post ID being saved
     */
    public function save_requirement_as_json($post_id) {
        // Check if this is a requirement post type
        if (get_post_type($post_id) !== 'requirement') {
            return;
        }

        // Avoid infinite loops and autosave
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }

        // Get all ACF fields for this requirement
        $requirement_data = $this->get_requirement_data($post_id);

        if (empty($requirement_data)) {
            return;
        }

        // Save as JSON file
        $this->save_json_file($post_id, $requirement_data);
    }

    /**
     * Get all ACF field data for a requirement
     *
     * @param int $post_id The requirement post ID
     * @return array The requirement data
     */
    private function get_requirement_data($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return array();
        }

        // Start with just title and all ACF fields
        $data = array(
            'title' => $post->post_title,
        );

        // Get all ACF fields for this post
        $fields = get_fields($post_id);

        if ($fields && is_array($fields)) {
            // Add all ACF fields to the data
            foreach ($fields as $field_name => $field_value) {
                $data[$field_name] = $field_value;
            }
        }

        return $data;
    }

    /**
     * Prevent ACF fields from being saved to database for requirement posts
     *
     * @param mixed $value The field value
     * @param int $post_id The post ID
     * @param array $field The field array
     * @param mixed $original The original value
     * @return mixed Return null to prevent saving, or original value to allow
     */
    public function prevent_acf_database_save($value, $post_id, $field, $original) {
        // Only prevent for requirement post type
        if (get_post_type($post_id) === 'requirement') {
            // Return null to prevent saving to database
            return null;
        }

        // Allow saving for other post types
        return $value;
    }



    /**
     * Save requirement data as JSON file
     *
     * @param int $post_id The post ID
     * @param array $data The requirement data to save
     */
    private function save_json_file($post_id, $data) {
        // Create main requirements directory if it doesn't exist
        $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
        if (!file_exists($requirements_dir)) {
            wp_mkdir_p($requirements_dir);
        }

        // Get requirement ID and title for folder name
        $requirement_id = isset($data['id']) ? $data['id'] : $post_id;
        $post_title = isset($data['title']) ? $data['title'] : 'Untitled';

        // Clean title for folder name (remove special characters)
        $clean_title = $this->sanitize_folder_name($post_title);

        // Create folder name: {requirement_id} - {post_title}
        $folder_name = $requirement_id . ' - ' . $clean_title;
        $requirement_folder = $requirements_dir . $folder_name . '/';

        // Delete old folder if requirement ID or title changed
        $this->delete_old_requirement_folder($post_id, $folder_name);

        // Create requirement folder if it doesn't exist
        if (!file_exists($requirement_folder)) {
            wp_mkdir_p($requirement_folder);
        }

        // Save data.json file (only title and ACF fields)
        $file_path = $requirement_folder . 'data.json';

        // Convert to JSON with pretty formatting
        $json_string = wp_json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        if ($json_string === false) {
            error_log('Failed to encode requirement data to JSON for post ID: ' . $post_id);
            return false;
        }

        // Save to file
        $result = file_put_contents($file_path, $json_string);

        if ($result === false) {
            error_log('Failed to save requirement JSON file: ' . $file_path);
            return false;
        }

        // Log successful save
        error_log('Successfully saved requirement JSON: ' . $folder_name . '/data.json');

        // Store the folder name in post meta for easy reference
        update_post_meta($post_id, '_requirement_folder', $folder_name);

        return true;
    }

    /**
     * Sanitize folder name by removing special characters
     *
     * @param string $name The folder name to sanitize
     * @return string Sanitized folder name
     */
    private function sanitize_folder_name($name) {
        // Remove or replace special characters
        $name = str_replace(array('/', '\\', ':', '*', '?', '"', '<', '>', '|'), '-', $name);
        // Remove multiple consecutive dashes
        $name = preg_replace('/-+/', '-', $name);
        // Trim dashes from start and end
        $name = trim($name, '-');
        // Limit length
        if (strlen($name) > 100) {
            $name = substr($name, 0, 100);
        }
        return $name;
    }

    /**
     * Delete old requirement folder for a post (in case of folder name changes)
     *
     * @param int $post_id The post ID
     * @param string $current_folder_name The current folder name to keep
     */
    private function delete_old_requirement_folder($post_id, $current_folder_name) {
        $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';

        if (!is_dir($requirements_dir)) {
            return;
        }

        // Get the old folder name from post meta
        $old_folder_name = get_post_meta($post_id, '_requirement_folder', true);

        if ($old_folder_name && $old_folder_name !== $current_folder_name) {
            $old_folder_path = $requirements_dir . $old_folder_name;
            if (is_dir($old_folder_path)) {
                $this->delete_directory_recursive($old_folder_path);
                error_log('Deleted old requirement folder: ' . $old_folder_name);
            }
        }
    }

    /**
     * Recursively delete a directory and all its contents
     *
     * @param string $dir_path The directory path to delete
     * @return bool Success status
     */
    private function delete_directory_recursive($dir_path) {
        if (!is_dir($dir_path)) {
            return false;
        }

        $files = array_diff(scandir($dir_path), array('.', '..'));

        foreach ($files as $file) {
            $file_path = $dir_path . DIRECTORY_SEPARATOR . $file;
            if (is_dir($file_path)) {
                $this->delete_directory_recursive($file_path);
            } else {
                unlink($file_path);
            }
        }

        return rmdir($dir_path);
    }

    /**
     * Delete requirement folder when post is deleted
     *
     * @param int $post_id The post ID being deleted
     */
    public function delete_requirement_json($post_id) {
        if (get_post_type($post_id) !== 'requirement') {
            return;
        }

        $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';

        // Get the folder name from post meta
        $folder_name = get_post_meta($post_id, '_requirement_folder', true);

        if ($folder_name) {
            $folder_path = $requirements_dir . $folder_name;
            if (is_dir($folder_path)) {
                $this->delete_directory_recursive($folder_path);
                error_log('Deleted requirement folder on post deletion: ' . $folder_name);
            }
        }
    }

    /**
     * Get list of all requirement folders and their data.json files
     *
     * @return array List of requirement folders with their info
     */
    public function get_requirement_json_files() {
        $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
        $folders = array();

        if (!is_dir($requirements_dir)) {
            return $folders;
        }

        // Get all directories in requirements folder
        $directories = glob($requirements_dir . '*', GLOB_ONLYDIR);

        foreach ($directories as $dir) {
            $folder_name = basename($dir);
            $data_json_path = $dir . '/data.json';

            $folder_info = array(
                'folder_name' => $folder_name,
                'folder_path' => $dir,
                'data_json_exists' => file_exists($data_json_path),
                'data_json_path' => $data_json_path,
                'modified' => is_dir($dir) ? filemtime($dir) : 0,
                'modified_formatted' => is_dir($dir) ? date('Y-m-d H:i:s', filemtime($dir)) : 'Unknown'
            );

            if (file_exists($data_json_path)) {
                $folder_info['data_json_size'] = filesize($data_json_path);
                $folder_info['data_json_modified'] = filemtime($data_json_path);
                $folder_info['data_json_modified_formatted'] = date('Y-m-d H:i:s', filemtime($data_json_path));

                // Try to read the JSON to get requirement info
                $json_content = file_get_contents($data_json_path);
                if ($json_content) {
                    $data = json_decode($json_content, true);
                    if ($data && is_array($data)) {
                        $folder_info['requirement_id'] = isset($data['id']) ? $data['id'] : 'Unknown';
                        $folder_info['title'] = isset($data['title']) ? $data['title'] : 'Unknown';
                    }
                }
            }

            $folders[] = $folder_info;
        }

        return $folders;
    }
}

// Initialize the class
new Save_Requirements_JSON();