<?php 

function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function encode($data) {
    return base64url_encode(gzcompress($data));
}

$mermaidCode = "graph LR\nA[تسجيل الدخول] --> B[لوحة البيانات الإدارية]\nB --> C[عرض البيانات]\nC --> D{هل البيانات كافية؟}\nD -- نعم --> E[عرض البيانات والإحصائيات]\nD -- لا --> F[عرض رسالة عدم توفر البيانات]";

// $mermaidCode = encode($mermaidCode);
// $diagram = new MermaidToImageConverter($mermaidCode );
// $imageUrl = $diagram->generateImage();

// $imageUrl = "https://kroki.io/mermaid/svg/" . $mermaidCode;

// prr($imageUrl);

// if (!extension_loaded('imagick')) {
//     echo 'Imagick extension is not installed';
//     exit;
// }
?>
<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Mermaid
    mermaid.initialize({
        startOnLoad: true,
        // theme: 'forest', // Change the theme name here dark forest neutral slate
        fontFamily: '"Helvetica Neue",sans-serif', // Change the font family name here
        fontSize: 16,
        theme: 'base',
        themeVariables: {
        primaryColor: '#FFFAB7',
        primaryTextColor: '#000',
        primaryBorderColor: '#8576FF',
        lineColor: '#FFA62F',
        secondaryColor: '#f9f9f9',
        tertiaryColor: '#000'
        }
    });

    // Function to convert SVG to canvas and then to image
    function convertSvgToImage(element) {
        const svgElement = element.querySelector('svg');

        if (svgElement) {
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            // Extract the max-width from the SVG's style attribute
            const style = svgElement.getAttribute('style');
            const maxWidthMatch = style.match(/max-width:\s*([\d.]+)px/);
            const maxWidth = maxWidthMatch ? parseFloat(maxWidthMatch[1]) : svgElement.clientWidth;

            img.onload = function() {
                // Calculate the aspect ratio
                const aspectRatio = img.width / img.height;

                // Set canvas dimensions based on the max-width and aspect ratio
                const scale = 1; // Adjust the scale factor as needed
                canvas.width = maxWidth * scale;
                canvas.height = (maxWidth / aspectRatio) * scale;

                // Draw the image on the canvas
                ctx.scale(scale, scale);
                ctx.drawImage(img, 0, 0, maxWidth, maxWidth / aspectRatio);

                // Convert canvas to data URL (PNG or JPEG)
                const imgData = canvas.toDataURL('image/png'); // Change to 'image/jpeg' for JPEG format

                // Create an image element
                const imgElement = document.createElement('img');
                imgElement.src = imgData;

                // Insert the image inside the Mermaid div
                element.insertBefore(imgElement, svgElement);

                // Remove the original SVG
                element.removeChild(svgElement);

                // Optionally, provide a download link
                // const link = document.createElement('a');
                // link.href = imgData;
                // link.download = 'diagram.png'; // Change to 'diagram.jpg' for JPEG format
                // link.textContent = 'Download Diagram';
                // element.parentNode.insertBefore(link, imgElement.nextSibling);
            };

            // Set the source of the image to be the SVG data
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
    }

    // Convert all Mermaid diagrams after rendering
    document.querySelectorAll('.mermaid').forEach((element) => {
        // Use a mutation observer to detect when the SVG is added to the DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeName === 'svg') {
                        observer.disconnect();
                        convertSvgToImage(element);
                    }
                });
            });
        });

        observer.observe(element, { childList: true });
    });

    // Trigger Mermaid rendering
    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
});
</script>
<img src="<?php echo $imageUrl; ?>" alt="">
<div id="code-show"></div>
<body>
  Here is a mermaid diagram:
  <div class="mermaid">
  graph LR\n   A[مقدم خدمة النقل يسجل الدخول إلى النظام] --> B[مقدم خدمة النقل يذهب إلى صفحة إدارة المركبات]\n B --> C[مقدم خدمة النقل يختار السيارة المراد تعديلها]\n    C --> D[مقدم خدمة النقل يقوم بتعديل المعلومات المطلوبة]\n   D --> E[مقدم خدمة النقل يرفع الوثائق الجديدة إن وجدت]\n    E --> F[مقدم خدمة النقل يحفظ التعديلات]\n    F --> G[النظام يتحقق من صحة الوثائق الجديدة]\n    G --> H[في حالة صحة الوثائق يتم تحديث معلومات السيارة]\n    H --> I[إشعار الإداريين بالتعديلات الجديدة]\n    G --> J[في حال فشل التحقق من صحة الوثائق الجديدة عرض رسالة خطأ]\n    J --> K[طلب إعادة تحميل وثائق صالحة]\n    C --> L[في حالة عدم وجود سيارة مسجلة مسبقًا عرض رسالة خطأ]\n    L --> M[إعطاء المستخدم خيارًا لتسجيل سيارة جديدة]


  </div>
  <div class="mermaid">
  graph LR
A[إنشاء طلب] --> B[استدعاء بيانات الطلب]
B --> C[نشر الطلب لكافة المركبات في محيط ٥٠ كم]
  </div>
  <div class="mermaid">
  graph LR
    A[المستخدم يفتح صفحة التحقق من البريد الإلكتروني] --> B[ينقر على رابط التحقق في البريد الإلكتروني]
    B --> C[النظام يتحقق من الرابط]
    C --> D[تأكيد عنوان البريد الإلكتروني]
  </div>
  <div class="mermaid">
      graph LR
        A[إكمال خطوات التسجيل] --> B[النظام يراجع مستندات التسجيل]
        B --> C[النظام يرسل إشعار حالة التسجيل]
        C --> D[المستخدم يتلقى الإشعار ويتبع التعليمات]
  </div>
  <div class="mermaid">
  graph TD;
    A[البداية] --> B[فتح صفحة تعديل الحساب];
    B --> C[تحديث المعلومات الشخصية أو كلمة المرور];
    C --> D[النقر على 'حفظ'];
    D --> E[تم التحديث بنجاح];

  </div>
</body>
<?php
// // Check if Imagick is installed
// if (!extension_loaded('imagick')) {
//     echo 'Imagick extension is not installed';
//     exit;
// }

// // Path to the input SVG file
// $inputSvg = 'https://analyst.xbees.net/wp-content/uploads/mermaid_diagram.svg';

// // Path to the output PNG file
// $outputPng = 'https://analyst.xbees.net/wp-content/uploads/mermaid_diagram.png';

// try {
//     // Create a new Imagick object
//     $imagick = new Imagick();

//     // Read the SVG file
//     $imagick->readImage($inputSvg);

//     // Set the image format to PNG
//     $imagick->setImageFormat('png');

//     // Write the image to the output file
//     $imagick->writeImage($outputPng);

//     // Clear the Imagick object
//     $imagick->clear();
//     $imagick->destroy();

//     echo 'SVG successfully converted to PNG!';
// } catch (Exception $e) {
//     echo 'Error: ' . $e->getMessage();
// }

// echo phpinfo();