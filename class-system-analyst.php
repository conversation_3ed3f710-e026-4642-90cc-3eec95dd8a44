<?php

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) exit;
if ( ! class_exists( 'System_Analyst' ) ) :

	/**
	 * Main System_Analyst Class.
	 *
	 * @package		ANALYST
	 * @subpackage	Classes/System_Analyst
	 * @since		0.0.1
	 * <AUTHOR>
	 */
	final class System_Analyst {

		/**
		 * The real instance
		 *
		 * @access	private
		 * @since	0.0.1
		 * @var		object|System_Analyst
		 */
		private static $instance;

		/**
		 * Throw error on object clone.
		 *
		 * Cloning instances of the class is forbidden.
		 *
		 * @access	public
		 * @since	0.0.1
		 * @return	void
		 */
		public function __clone() {
			_doing_it_wrong( __FUNCTION__, __( 'You are not allowed to clone this class.', 'system-analyst' ), '0.0.1' );
		}

		/**
		 * Disable unserializing of the class.
		 *
		 * @access	public
		 * @since	0.0.1
		 * @return	void
		 */
		public function __wakeup() {
			_doing_it_wrong( __FUNCTION__, __( 'You are not allowed to unserialize this class.', 'system-analyst' ), '0.0.1' );
		}

		/**
		 * Main System_Analyst Instance.
		 *
		 * Insures that only one instance of System_Analyst exists in memory at any one
		 * time. Also prevents needing to define globals all over the place.
		 *
		 * @access		public
		 * @since		0.0.1
		 * @static
		 * @return		object|System_Analyst	The one true System_Analyst
		 */
		public static function instance() {
			if ( ! isset( self::$instance ) && ! ( self::$instance instanceof System_Analyst ) ) {
				self::$instance					= new System_Analyst;
				self::$instance->base_hooks();
				self::$instance->includes();

				//Fire the plugin logic
				new ANALYST_CHAT;
				new USE_CASES;
				new Requirement;

				/**
				 * Fire a custom action to allow dependencies
				 * after the successful plugin setup
				 */
				do_action( 'ANALYST/plugin_loaded' );
			}

			return self::$instance;
		}

		/**
		 * Include required files.
		 *
		 * @access  private
		 * @since   0.0.1
		 * @return  void
		 */
		private function includes() {
			require_once ANALYST_PLUGIN_DIR . 'includes/helpers.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/post-register.php';
			//require_once ANALYST_PLUGIN_DIR . 'includes/acf_save_as_json.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/save_requirements_json.php';

			require_once ANALYST_PLUGIN_DIR . 'includes/classes/chat.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/classes/use-case.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/classes/requirement.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/classes/acf-fields.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/classes/JSON_Import_Handler.php';
			require_once ANALYST_PLUGIN_DIR . 'includes/classes/MermaidToImageConverter.php';

			//$mermaidCode = "graph LR\nA[تسجيل الدخول] --> B[لوحة البيانات الإدارية]\nB --> C[عرض البيانات]\nC --> D{هل البيانات كافية؟}\nD -- نعم --> E[عرض البيانات والإحصائيات]\nD -- لا --> F[عرض رسالة عدم توفر البيانات]";
			//$diagram = new MermaidToImageConverter($mermaidCode);
			//$imageUrl = $diagram->generateImage();

		}

		/**
		 * Add base hooks for the core functionality
		 *
		 * @access  private
		 * @since   0.0.1
		 * @return  void
		 */
		private function base_hooks() {
			add_action( 'plugins_loaded', array( self::$instance, 'load_textdomain' ) );

			add_action( 'plugin_action_links_' . ANALYST_PLUGIN_BASE, array( $this, 'add_plugin_action_link' ), 20 );
		}

		/**
		 * Loads the plugin language files.
		 *
		 * @access  public
		 * @since   0.0.1
		 * @return  void
		 */
		public function load_textdomain() {
			load_plugin_textdomain( 'system-analyst', FALSE, dirname( ANALYST_PLUGIN_DIR ) . '/languages/' );
		}

		/**
		* Adds action links to the plugin list table
		*
		* @access	public
		* @since	0.0.1
		*
		* @param	array	$links An array of plugin action links.
		*
		* @return	array	An array of plugin action links.
		*/
		public function add_plugin_action_link( $links ) {

			$links['our_shop'] = sprintf( '<a href="%s" title="Settings" style="font-weight:700;">%s</a>', 'https://pluginplate.com/plugin-boilerplate/', __( 'Settings', 'system-analyst' ) );

			return $links;
		}
	}
endif; // End if class_exists check.