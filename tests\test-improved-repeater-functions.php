<?php
/**
 * Test improved repeater functions
 * This file tests the new functions designed specifically for array/repeater fields
 */

// Set up basic constants
define('ANALYST_PLUGIN_DIR', dirname(__DIR__) . '/');

// Mock WordPress functions
if (!function_exists('get_post_type')) {
    function get_post_type($post_id) { return 'requirement'; }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $meta_key, $single = false) {
        if ($meta_key === '_requirement_folder') {
            $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
            $folders = glob($requirements_dir . '*', GLOB_ONLYDIR);
            if (!empty($folders)) {
                return basename($folders[0]);
            }
        }
        return false;
    }
}

if (!function_exists('get_field')) {
    function get_field($field_name, $post_id) { return false; }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $args = 1) { return true; }
}

// Include the classes and functions
require_once ANALYST_PLUGIN_DIR . 'includes/classes/requirements-json-reader.php';
require_once ANALYST_PLUGIN_DIR . 'includes/template-functions.php';

echo '<h1>🔧 Test Improved Repeater Functions</h1>';

// Test 1: Basic field access
echo '<h2>Test 1: Basic Field Access</h2>';

$test_post_id = 123;

try {
    // Test simple fields first
    $simple_fields = array('id', 'title');
    
    foreach ($simple_fields as $field_name) {
        $value = get_requirement_field($field_name, $test_post_id);
        echo '<div style="margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 5px;">';
        echo '<strong>' . $field_name . ':</strong> ';
        echo '<span style="color: ' . (empty($value) ? 'red' : 'green') . ';">';
        echo htmlspecialchars($value ?: 'Empty');
        echo '</span>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
}

// Test 2: Array/Repeater fields
echo '<h2>Test 2: Array/Repeater Fields</h2>';

$repeater_fields = array(
    'business_goals' => 'goal',
    'stakeholders' => 'stakeholder',
    'main_steps' => 'step',
    'user_stories' => 'story',
    'performance_indicators' => 'indicators'
);

foreach ($repeater_fields as $field_name => $sub_field) {
    echo '<div style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 8px; border-left: 4px solid #2196f3;">';
    echo '<h3>' . $field_name . '</h3>';
    
    try {
        // Test get_requirement_field (raw data)
        $raw_data = get_requirement_field($field_name, $test_post_id);
        echo '<h4>📊 Raw Data:</h4>';
        echo '<ul>';
        echo '<li><strong>Type:</strong> ' . gettype($raw_data) . '</li>';
        echo '<li><strong>Count:</strong> ' . (is_array($raw_data) ? count($raw_data) : 'Not array') . '</li>';
        echo '<li><strong>Empty:</strong> ' . (empty($raw_data) ? 'Yes' : 'No') . '</li>';
        echo '</ul>';
        
        if (is_array($raw_data) && !empty($raw_data)) {
            echo '<details>';
            echo '<summary>Show first item structure</summary>';
            echo '<pre style="background: white; padding: 10px; border-radius: 3px; margin: 10px 0;">';
            echo htmlspecialchars(json_encode($raw_data[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo '</pre>';
            echo '</details>';
        }
        
        // Test get_requirement_repeater_values (processed data)
        $processed_values = get_requirement_repeater_values($field_name, $test_post_id, $sub_field);
        echo '<h4>🔄 Processed Values:</h4>';
        echo '<ul>';
        echo '<li><strong>Count:</strong> ' . count($processed_values) . '</li>';
        echo '<li><strong>Values:</strong></li>';
        echo '</ul>';
        
        if (!empty($processed_values)) {
            echo '<ol style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
            foreach (array_slice($processed_values, 0, 3) as $value) {
                echo '<li>' . htmlspecialchars(substr($value, 0, 100)) . (strlen($value) > 100 ? '...' : '') . '</li>';
            }
            if (count($processed_values) > 3) {
                echo '<li><em>... and ' . (count($processed_values) - 3) . ' more items</em></li>';
            }
            echo '</ol>';
        } else {
            echo '<p style="color: red;">❌ No values extracted</p>';
        }
        
        // Test display_requirement_repeater (formatted output)
        echo '<h4>🎨 Formatted Display:</h4>';
        
        echo '<p><strong>List format:</strong></p>';
        echo '<div style="background: white; padding: 10px; border-radius: 3px; border: 1px solid #ddd;">';
        display_requirement_repeater($field_name, $test_post_id, $sub_field, 'list');
        echo '</div>';
        
        echo '<p><strong>Comma format:</strong></p>';
        echo '<div style="background: white; padding: 10px; border-radius: 3px; border: 1px solid #ddd;">';
        display_requirement_repeater($field_name, $test_post_id, $sub_field, 'comma');
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<p style="color: red;">❌ Error with ' . $field_name . ': ' . $e->getMessage() . '</p>';
    }
    
    echo '</div>';
}

// Test 3: Complex repeater (use_cases)
echo '<h2>Test 3: Complex Repeater (use_cases)</h2>';

try {
    $use_cases = get_requirement_field('use_cases', $test_post_id);
    
    echo '<div style="margin: 20px 0; padding: 15px; background: #fff3e0; border-radius: 8px;">';
    echo '<h3>Use Cases Analysis</h3>';
    
    echo '<p><strong>Type:</strong> ' . gettype($use_cases) . '</p>';
    echo '<p><strong>Count:</strong> ' . (is_array($use_cases) ? count($use_cases) : 'Not array') . '</p>';
    
    if (is_array($use_cases) && !empty($use_cases)) {
        $first_use_case = $use_cases[0];
        
        echo '<h4>First Use Case Fields:</h4>';
        echo '<ul style="columns: 2; column-gap: 20px;">';
        if (is_array($first_use_case)) {
            foreach (array_keys($first_use_case) as $key) {
                echo '<li>' . htmlspecialchars($key) . '</li>';
            }
        }
        echo '</ul>';
        
        // Test specific sub-fields
        $sub_fields = array('case_id', 'description', 'description_simple', 'actor', 'actor_simple');
        
        foreach ($sub_fields as $sub_field) {
            $values = get_requirement_repeater_values('use_cases', $test_post_id, $sub_field);
            echo '<p><strong>' . $sub_field . ':</strong> ' . count($values) . ' values found</p>';
            
            if (!empty($values)) {
                echo '<div style="background: white; padding: 8px; border-radius: 3px; margin: 5px 0;">';
                echo '<small>' . htmlspecialchars(substr($values[0], 0, 150)) . '...</small>';
                echo '</div>';
            }
        }
        
        // Test nested repeaters
        $nested_repeaters = array('preconditions', 'postconditions', 'main_flow_steps');
        
        echo '<h4>Nested Repeaters:</h4>';
        foreach ($nested_repeaters as $nested_field) {
            if (isset($first_use_case[$nested_field]) && is_array($first_use_case[$nested_field])) {
                echo '<p><strong>' . $nested_field . ':</strong> ' . count($first_use_case[$nested_field]) . ' items</p>';
                
                // Try to extract values from nested repeater
                $nested_values = array();
                foreach ($first_use_case[$nested_field] as $item) {
                    if (is_array($item)) {
                        foreach ($item as $value) {
                            if (is_string($value) && !empty(trim($value))) {
                                $nested_values[] = $value;
                                break;
                            }
                        }
                    }
                }
                
                if (!empty($nested_values)) {
                    echo '<div style="background: white; padding: 8px; border-radius: 3px; margin: 5px 0;">';
                    echo '<small>' . htmlspecialchars(substr($nested_values[0], 0, 100)) . '...</small>';
                    echo '</div>';
                }
            }
        }
    }
    
    echo '</div>';
    
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Error with use_cases: ' . $e->getMessage() . '</p>';
}

// Test 4: Performance test
echo '<h2>Test 4: Performance Test</h2>';

try {
    $start_time = microtime(true);
    
    // Test multiple calls
    for ($i = 0; $i < 5; $i++) {
        foreach ($repeater_fields as $field_name => $sub_field) {
            $values = get_requirement_repeater_values($field_name, $test_post_id, $sub_field);
        }
    }
    
    $end_time = microtime(true);
    $execution_time = ($end_time - $start_time) * 1000;
    
    echo '<div style="background: #e8f5e8; padding: 15px; border-radius: 5px;">';
    echo '<p><strong>Performance Results:</strong></p>';
    echo '<ul>';
    echo '<li>Total time: ' . number_format($execution_time, 2) . ' ms</li>';
    echo '<li>Average per field: ' . number_format($execution_time / (5 * count($repeater_fields)), 2) . ' ms</li>';
    echo '<li>Status: ' . ($execution_time < 500 ? '✅ Good' : '⚠️ Needs optimization') . '</li>';
    echo '</ul>';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Performance test error: ' . $e->getMessage() . '</p>';
}

// Summary
echo '<h2>📋 Test Summary</h2>';

echo '<div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h3>✅ What Should Work Now:</h3>';
echo '<ul>';
echo '<li><strong>Array Detection:</strong> System properly identifies array fields</li>';
echo '<li><strong>Value Extraction:</strong> Sub-field values are extracted correctly</li>';
echo '<li><strong>Multiple Formats:</strong> Data can be displayed in different formats</li>';
echo '<li><strong>Error Handling:</strong> Graceful handling of missing or malformed data</li>';
echo '</ul>';

echo '<h3>🔧 New Functions Available:</h3>';
echo '<ul>';
echo '<li><code>get_requirement_repeater_values($field, $post_id, $sub_field)</code> - Extract values from repeater</li>';
echo '<li><code>display_requirement_repeater($field, $post_id, $sub_field, $format)</code> - Display formatted output</li>';
echo '</ul>';

echo '<h3>📝 Usage Examples:</h3>';
echo '<pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">';
echo htmlspecialchars('<?php
// Get business goals as array
$goals = get_requirement_repeater_values("business_goals", $post_id, "goal");

// Display as list
display_requirement_repeater("stakeholders", $post_id, "stakeholder", "list");

// Display as comma-separated
display_requirement_repeater("main_steps", $post_id, "step", "comma");
?>');
echo '</pre>';
echo '</div>';

?>
