<?php
/**
 * Test file for JSON export functionality
 * This file can be accessed via browser to test the JSON export feature
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if we have the required classes
if (!class_exists('Save_Requirements_JSON')) {
    die('Save_Requirements_JSON class not found. Make sure the plugin is activated.');
}

echo '<h1>Test JSON-Based Requirements System</h1>';

// Test JSON reader
if (class_exists('Requirements_JSON_Reader')) {
    echo '<div style="background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px;">';
    echo '<strong>✓ JSON Reader Class:</strong> Loaded successfully';
    echo '</div>';
} else {
    echo '<div style="background: #ffebee; padding: 10px; margin: 10px 0; border-radius: 5px;">';
    echo '<strong>✗ JSON Reader Class:</strong> Not found';
    echo '</div>';
}

// Get all requirement posts
$args = array(
    'post_type' => 'requirement',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'orderby' => 'ID',
    'order' => 'ASC'
);

$requirements = get_posts($args);

if (empty($requirements)) {
    echo '<p>No requirements found. Please create some requirements first.</p>';
    exit;
}

echo '<h2>Found ' . count($requirements) . ' requirements:</h2>';

// Create instance of the JSON exporter
$json_exporter = new Save_Requirements_JSON();

echo '<table border="1" cellpadding="5" cellspacing="0">';
echo '<tr><th>Post ID</th><th>Title</th><th>Requirement ID (DB)</th><th>Requirement ID (JSON)</th><th>Action</th><th>JSON File Status</th></tr>';

foreach ($requirements as $requirement) {
    $post_id = $requirement->ID;
    $title = $requirement->post_title;
    $req_id_db = get_field('id', $post_id); // From database (should be empty now)
    $req_id_json = get_requirement_field('id', $post_id); // From JSON
    $folder_name = get_post_meta($post_id, '_requirement_folder', true);

    echo '<tr>';
    echo '<td>' . $post_id . '</td>';
    echo '<td>' . esc_html($title) . '</td>';
    echo '<td>' . ($req_id_db ? $req_id_db : '<span style="color: green;">Empty (Good!)</span>') . '</td>';
    echo '<td>' . ($req_id_json ? $req_id_json : '<span style="color: red;">Not found</span>') . '</td>';

    // Test export button
    echo '<td>';
    if (isset($_GET['export']) && $_GET['export'] == $post_id) {
        // Trigger export
        $json_exporter->save_requirement_as_json($post_id);
        echo '<strong>Export triggered!</strong>';
    } else {
        echo '<a href="?export=' . $post_id . '">Export to JSON</a>';
    }
    echo '</td>';

    // Check JSON folder status
    echo '<td>';
    if ($folder_name) {
        $folder_path = ANALYST_PLUGIN_DIR . 'requirments/' . $folder_name;
        $data_json_path = $folder_path . '/data.json';
        if (is_dir($folder_path) && file_exists($data_json_path)) {
            $file_size = filesize($data_json_path);
            $file_time = date('Y-m-d H:i:s', filemtime($data_json_path));
            echo '<span style="color: green;">✓ Folder exists</span><br>';
            echo 'Folder: ' . esc_html($folder_name) . '<br>';
            echo 'data.json Size: ' . number_format($file_size) . ' bytes<br>';
            echo 'Modified: ' . $file_time . '<br>';
            echo '<a href="view-json.php?folder=' . urlencode($folder_name) . '" target="_blank">View JSON</a>';
        } else {
            echo '<span style="color: red;">✗ Folder or data.json missing</span>';
        }
    } else {
        echo '<span style="color: orange;">No folder recorded</span>';
    }
    echo '</td>';

    echo '</tr>';
}

echo '</table>';

// Show list of all requirement folders
echo '<h2>All Requirement Folders in Requirements Directory:</h2>';
$requirement_folders = $json_exporter->get_requirement_json_files();

if (empty($requirement_folders)) {
    echo '<p>No requirement folders found in the requirements directory.</p>';
} else {
    echo '<table border="1" cellpadding="5" cellspacing="0">';
    echo '<tr><th>Folder Name</th><th>Requirement ID</th><th>Title</th><th>data.json Status</th><th>File Size</th><th>Modified</th><th>Actions</th></tr>';

    foreach ($requirement_folders as $folder) {
        echo '<tr>';
        echo '<td>' . esc_html($folder['folder_name']) . '</td>';
        echo '<td>' . (isset($folder['requirement_id']) ? $folder['requirement_id'] : 'N/A') . '</td>';
        echo '<td>' . (isset($folder['title']) ? esc_html($folder['title']) : 'N/A') . '</td>';

        // data.json status
        echo '<td>';
        if ($folder['data_json_exists']) {
            echo '<span style="color: green;">✓ Exists</span>';
        } else {
            echo '<span style="color: red;">✗ Missing</span>';
        }
        echo '</td>';

        echo '<td>' . (isset($folder['data_json_size']) ? number_format($folder['data_json_size']) . ' bytes' : 'N/A') . '</td>';
        echo '<td>' . (isset($folder['data_json_modified_formatted']) ? $folder['data_json_modified_formatted'] : $folder['modified_formatted']) . '</td>';
        echo '<td>';
        if ($folder['data_json_exists']) {
            echo '<a href="view-json.php?folder=' . urlencode($folder['folder_name']) . '" target="_blank">View JSON</a>';
        } else {
            echo 'No data.json';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</table>';
}

// Show requirements directory path
echo '<h3>Requirements Directory:</h3>';
echo '<p><code>' . ANALYST_PLUGIN_DIR . 'requirments/' . '</code></p>';

// Test manual export for all requirements
echo '<h3>Bulk Actions:</h3>';
if (isset($_GET['export_all'])) {
    echo '<p><strong>Exporting all requirements...</strong></p>';
    foreach ($requirements as $requirement) {
        $json_exporter->save_requirement_as_json($requirement->ID);
        echo '<p>Exported: ' . esc_html($requirement->post_title) . ' (ID: ' . $requirement->ID . ')</p>';
    }
    echo '<p><strong>Bulk export completed!</strong></p>';
    echo '<p><a href="' . $_SERVER['PHP_SELF'] . '">Refresh page</a></p>';
} else {
    echo '<p><a href="?export_all=1">Export All Requirements to JSON</a></p>';
}

// Test JSON-based functions
echo '<h2>Test JSON-Based Functions:</h2>';

if (function_exists('get_all_requirements')) {
    echo '<h3>All Requirements from JSON:</h3>';
    $json_requirements = get_all_requirements();

    if (empty($json_requirements)) {
        echo '<p>No requirements found in JSON files.</p>';
    } else {
        echo '<table border="1" cellpadding="5" cellspacing="0">';
        echo '<tr><th>Requirement ID</th><th>Title</th><th>Folder</th><th>Use Cases Count</th></tr>';

        foreach ($json_requirements as $req) {
            echo '<tr>';
            echo '<td>' . (isset($req['id']) ? esc_html($req['id']) : 'N/A') . '</td>';
            echo '<td>' . (isset($req['title']) ? esc_html($req['title']) : 'N/A') . '</td>';
            echo '<td>' . (isset($req['_folder_name']) ? esc_html($req['_folder_name']) : 'N/A') . '</td>';

            $use_cases_count = 0;
            if (isset($req['use_cases']) && is_array($req['use_cases'])) {
                $use_cases_count = count($req['use_cases']);
            }
            echo '<td>' . $use_cases_count . '</td>';
            echo '</tr>';
        }

        echo '</table>';
    }

    // Test statistics
    if (function_exists('get_requirements_stats')) {
        echo '<h3>Requirements Statistics:</h3>';
        $stats = get_requirements_stats();
        echo '<ul>';
        echo '<li><strong>Total Requirements:</strong> ' . $stats['total_count'] . '</li>';
        echo '<li><strong>Requirements with Use Cases:</strong> ' . $stats['with_use_cases'] . '</li>';
        echo '<li><strong>Total Use Cases:</strong> ' . $stats['total_use_cases'] . '</li>';
        echo '</ul>';
    }

} else {
    echo '<p style="color: red;">JSON-based functions not available. Check if template-functions.php is loaded.</p>';
}

// Test specific requirement lookup
if (isset($_GET['test_req_id']) && !empty($_GET['test_req_id'])) {
    $test_id = sanitize_text_field($_GET['test_req_id']);
    echo '<h3>Test Requirement Lookup for ID: ' . esc_html($test_id) . '</h3>';

    if (function_exists('get_requirement_by_id')) {
        $req_data = get_requirement_by_id($test_id);
        if ($req_data) {
            echo '<div style="background: #e8f5e8; padding: 10px; border-radius: 5px;">';
            echo '<strong>✓ Found requirement:</strong><br>';
            echo 'Title: ' . esc_html($req_data['title']) . '<br>';
            echo 'ID: ' . esc_html($req_data['id']) . '<br>';
            echo '</div>';
        } else {
            echo '<div style="background: #ffebee; padding: 10px; border-radius: 5px;">';
            echo '<strong>✗ Requirement not found</strong>';
            echo '</div>';
        }
    }
}

echo '<h3>Test Requirement Lookup:</h3>';
echo '<form method="get">';
echo '<input type="text" name="test_req_id" placeholder="Enter requirement ID (e.g., 4.2.1)" value="' . (isset($_GET['test_req_id']) ? esc_attr($_GET['test_req_id']) : '') . '">';
echo '<input type="submit" value="Test Lookup">';
echo '</form>';

?>
