<?php
/**
 * Test file for JSON export functionality
 * This file can be accessed via browser to test the JSON export feature
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if we have the required classes
if (!class_exists('Save_Requirements_JSON')) {
    die('Save_Requirements_JSON class not found. Make sure the plugin is activated.');
}

echo '<h1>Test JSON Export Functionality</h1>';

// Get all requirement posts
$args = array(
    'post_type' => 'requirement',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'orderby' => 'ID',
    'order' => 'ASC'
);

$requirements = get_posts($args);

if (empty($requirements)) {
    echo '<p>No requirements found. Please create some requirements first.</p>';
    exit;
}

echo '<h2>Found ' . count($requirements) . ' requirements:</h2>';

// Create instance of the JSON exporter
$json_exporter = new Save_Requirements_JSON();

echo '<table border="1" cellpadding="5" cellspacing="0">';
echo '<tr><th>Post ID</th><th>Title</th><th>Requirement ID</th><th>Action</th><th>JSON File Status</th></tr>';

foreach ($requirements as $requirement) {
    $post_id = $requirement->ID;
    $title = $requirement->post_title;
    $req_id = get_field('id', $post_id);
    $folder_name = get_post_meta($post_id, '_requirement_folder', true);

    echo '<tr>';
    echo '<td>' . $post_id . '</td>';
    echo '<td>' . esc_html($title) . '</td>';
    echo '<td>' . ($req_id ? $req_id : 'Not set') . '</td>';

    // Test export button
    echo '<td>';
    if (isset($_GET['export']) && $_GET['export'] == $post_id) {
        // Trigger export
        $json_exporter->save_requirement_as_json($post_id);
        echo '<strong>Export triggered!</strong>';
    } else {
        echo '<a href="?export=' . $post_id . '">Export to JSON</a>';
    }
    echo '</td>';

    // Check JSON folder status
    echo '<td>';
    if ($folder_name) {
        $folder_path = ANALYST_PLUGIN_DIR . 'requirments/' . $folder_name;
        $data_json_path = $folder_path . '/data.json';
        if (is_dir($folder_path) && file_exists($data_json_path)) {
            $file_size = filesize($data_json_path);
            $file_time = date('Y-m-d H:i:s', filemtime($data_json_path));
            echo '<span style="color: green;">✓ Folder exists</span><br>';
            echo 'Folder: ' . esc_html($folder_name) . '<br>';
            echo 'data.json Size: ' . number_format($file_size) . ' bytes<br>';
            echo 'Modified: ' . $file_time . '<br>';
            echo '<a href="view-json.php?folder=' . urlencode($folder_name) . '" target="_blank">View JSON</a>';
        } else {
            echo '<span style="color: red;">✗ Folder or data.json missing</span>';
        }
    } else {
        echo '<span style="color: orange;">No folder recorded</span>';
    }
    echo '</td>';

    echo '</tr>';
}

echo '</table>';

// Show list of all requirement folders
echo '<h2>All Requirement Folders in Requirements Directory:</h2>';
$requirement_folders = $json_exporter->get_requirement_json_files();

if (empty($requirement_folders)) {
    echo '<p>No requirement folders found in the requirements directory.</p>';
} else {
    echo '<table border="1" cellpadding="5" cellspacing="0">';
    echo '<tr><th>Folder Name</th><th>Requirement ID</th><th>Title</th><th>data.json Status</th><th>File Size</th><th>Modified</th><th>Actions</th></tr>';

    foreach ($requirement_folders as $folder) {
        echo '<tr>';
        echo '<td>' . esc_html($folder['folder_name']) . '</td>';
        echo '<td>' . (isset($folder['requirement_id']) ? $folder['requirement_id'] : 'N/A') . '</td>';
        echo '<td>' . (isset($folder['title']) ? esc_html($folder['title']) : 'N/A') . '</td>';

        // data.json status
        echo '<td>';
        if ($folder['data_json_exists']) {
            echo '<span style="color: green;">✓ Exists</span>';
        } else {
            echo '<span style="color: red;">✗ Missing</span>';
        }
        echo '</td>';

        echo '<td>' . (isset($folder['data_json_size']) ? number_format($folder['data_json_size']) . ' bytes' : 'N/A') . '</td>';
        echo '<td>' . (isset($folder['data_json_modified_formatted']) ? $folder['data_json_modified_formatted'] : $folder['modified_formatted']) . '</td>';
        echo '<td>';
        if ($folder['data_json_exists']) {
            echo '<a href="view-json.php?folder=' . urlencode($folder['folder_name']) . '" target="_blank">View JSON</a>';
        } else {
            echo 'No data.json';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</table>';
}

// Show requirements directory path
echo '<h3>Requirements Directory:</h3>';
echo '<p><code>' . ANALYST_PLUGIN_DIR . 'requirments/' . '</code></p>';

// Test manual export for all requirements
echo '<h3>Bulk Actions:</h3>';
if (isset($_GET['export_all'])) {
    echo '<p><strong>Exporting all requirements...</strong></p>';
    foreach ($requirements as $requirement) {
        $json_exporter->save_requirement_as_json($requirement->ID);
        echo '<p>Exported: ' . esc_html($requirement->post_title) . ' (ID: ' . $requirement->ID . ')</p>';
    }
    echo '<p><strong>Bulk export completed!</strong></p>';
    echo '<p><a href="' . $_SERVER['PHP_SELF'] . '">Refresh page</a></p>';
} else {
    echo '<p><a href="?export_all=1">Export All Requirements to JSON</a></p>';
}

?>
