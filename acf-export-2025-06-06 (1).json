[{"key": "group_6650d3764f233", "title": "acf Details v6", "fields": [{"key": "field_6650d43fec5e2", "label": "رقم التسلسل", "name": "id", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": ""}, {"key": "field_6650d50638474", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> العمل", "name": "business_goals", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 1, "max": 0, "layout": "table", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_6650d51938475", "label": "goal", "name": "goal", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d50638474"}]}, {"key": "field_6650d52b38476", "label": "الجهات المعنية", "name": "stakeholders", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 1, "max": 0, "layout": "block", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_6650d52b38477", "label": "stakeholder", "name": "stakeholder", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d52b38476"}]}, {"key": "field_6650d54138478", "label": "الخطوات الرئيسية", "name": "main_steps", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 1, "max": 0, "layout": "row", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_6650d54138479", "label": "step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d54138478"}]}, {"key": "field_6650d55c3847a", "label": "الخطوات البديلة", "name": "alternative_steps", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "collapsed": "", "min": 1, "max": 0, "layout": "table", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_6650d55c3847b", "label": "step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d55c3847a"}]}, {"key": "field_6650d5733847c", "label": "قصص المستخدمين", "name": "user_stories", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "collapsed": "", "min": 1, "max": 0, "layout": "table", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_6650d5733847d", "label": "story", "name": "story", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d5733847c"}]}, {"key": "field_6650d5863847e", "label": "مؤشرات الأداء", "name": "performance_indicators", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "collapsed": "", "min": 1, "max": 0, "layout": "table", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_6650d5863847f", "label": "indicators", "name": "indicators", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_6650d5863847e"}]}, {"key": "field_1650d43fec5e2", "label": "حالات الاستخدام", "name": "use_cases", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_8f", "min": 0, "max": 0, "layout": "block", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_665e5810e1059", "label": "هل الحالة جديدة", "name": "is_new", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "نعم", "ui_off_text": "لا", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_665e07faea1d5", "label": "رقم الحالة", "name": "case_id", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8a", "label": "Description Simple", "name": "description_simple", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8b", "label": "Actor Simple", "name": "actor_simple", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_2650d43fec5e2", "label": "Preconditions Simple", "name": "preconditions_simple", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_3650d43fec5e2", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_2650d43fec5e2"}]}, {"key": "field_8d", "label": "Postconditions Simple", "name": "postconditions_simple", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8d1", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8d"}]}, {"key": "field_8e", "label": "Main Flow Steps Simple", "name": "main_flow_steps_simple", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8e1", "label": "Step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8e"}]}, {"key": "field_8f", "label": "Description", "name": "description", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8g", "label": "Actor", "name": "actor", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8h", "label": "Preconditions", "name": "preconditions", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8h1", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8h"}]}, {"key": "field_8i", "label": "Postconditions", "name": "postconditions", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8i1", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8i"}]}, {"key": "field_8j", "label": "Main Flow Steps", "name": "main_flow_steps", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8j1", "label": "Step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8j"}]}, {"key": "field_8t", "label": "Events Sequence", "name": "events_sequence", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8t1", "label": "Event", "name": "event", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8t"}]}, {"key": "field_8k", "label": "Alternative Flow Steps", "name": "alternative_flow_steps", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_66600e9a201d2", "label": "condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_8k"}, {"key": "field_8k2", "label": "Steps", "name": "steps", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_8k", "sub_fields": [{"key": "field_8k1", "label": "Step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8k2"}]}]}, {"key": "field_8l", "label": "Exception Flow Steps", "name": "exception_flow_steps", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8l1", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8l"}, {"key": "field_8k2255", "label": "Steps", "name": "steps", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_8l", "sub_fields": [{"key": "field_8k12", "label": "Step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8k2255"}]}]}, {"key": "field_8m", "label": "<PERSON><PERSON>", "name": "trigger", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8n", "label": "Priority", "name": "priority", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8o", "label": "Business Rules", "name": "business_rules", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8k2222", "label": "Rule", "name": "rule", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8o"}]}, {"key": "field_8p", "label": "Assumptions", "name": "assumptions", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8k333312", "label": "assumption", "name": "assumption", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8p"}]}, {"key": "field_8q", "label": "Frequency of Use", "name": "frequency_of_use", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_1650d43fec5e2"}, {"key": "field_8r", "label": "Special Requirements", "name": "special_requirements", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8k333344", "label": "requirement", "name": "requirement", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8r"}]}, {"key": "field_8s", "label": "Notes and Issues", "name": "notes_and_issues", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8k1144", "label": "note", "name": "note", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8s"}]}, {"key": "field_8u", "label": "Inputs", "name": "inputs", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8u1", "label": "Input", "name": "input", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8u"}]}, {"key": "field_8v", "label": "Outputs", "name": "outputs", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8v1", "label": "Output", "name": "output", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8v"}]}, {"key": "field_8w", "label": "User Interactions", "name": "user_interactions", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8w1", "label": "Interaction", "name": "interaction", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8w"}]}, {"key": "field_8x", "label": "Special Conditions", "name": "special_conditions", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8x1", "label": "Condition", "name": "condition", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8x"}]}, {"key": "field_8y", "label": "Usage Scenarios", "name": "usage_scenarios", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8y1", "label": "<PERSON><PERSON><PERSON>", "name": "scenario", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8y"}]}, {"key": "field_8z", "label": "Security Requirements", "name": "security_requirements", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8z1", "label": "Requirement", "name": "requirement", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8z"}]}, {"key": "field_8aa", "label": "Integration with Other Systems", "name": "integration_with_other_systems", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8aa1", "label": "System", "name": "system", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8aa"}]}, {"key": "field_8ab", "label": "Constraints and Assumptions", "name": "constraints_and_assumptions", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8ab1", "label": "Constraint", "name": "constraint", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ab"}]}, {"key": "field_8ac", "label": "Testing Requirements", "name": "testing_requirements", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8ac1", "label": "Requirement", "name": "requirement", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ac"}]}, {"key": "field_8ad", "label": "Backend Details", "name": "backend_details", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "block", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8ad1", "label": "API Endpoints", "name": "api_endpoints", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "row", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "parent_repeater": "field_8ad", "sub_fields": [{"key": "field_8ad1a", "label": "Method", "name": "method", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ad1"}, {"key": "field_8ad1b", "label": "Endpoint", "name": "endpoint", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ad1"}, {"key": "field_8ad1c", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8ad1"}, {"key": "field_8ad1d", "label": "Request", "name": "request", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_8ad1", "sub_fields": [{"key": "field_8ad1d1", "label": "Body", "name": "body", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8ad1d"}, {"key": "field_8ad1d2", "label": "Headers", "name": "headers", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8ad1d"}]}, {"key": "field_8ad1e", "label": "Response", "name": "response", "aria-label": "", "type": "repeater", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "rows_per_page": 20, "layout": "table", "button_label": "Add Row", "collapsed": "", "acfe_repeater_stylised_button": 0, "parent_repeater": "field_8ad1", "sub_fields": [{"key": "field_8ad1e1", "label": "Status Code", "name": "status_code", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "step": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ad1e"}, {"key": "field_8ad1e2", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8ad1e"}, {"key": "field_8ad1e3", "label": "Body", "name": "body", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8ad1e"}]}]}]}, {"key": "field_8ae", "label": "Frontend Details", "name": "frontend_details", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "block", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8ae1", "label": "Screens", "name": "screens", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "row", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "parent_repeater": "field_8ae", "sub_fields": [{"key": "field_8ae1a", "label": "Name", "name": "name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8ae1"}, {"key": "field_665ed52aa1966", "label": "Components", "name": "components", "aria-label": "", "type": "flexible_content", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_flexible_advanced": 1, "acfe_flexible_stylised_button": 0, "acfe_flexible_hide_empty_message": 0, "acfe_flexible_empty_message": "", "acfe_flexible_layouts_templates": 0, "acfe_flexible_layouts_placeholder": 0, "acfe_flexible_layouts_thumbnails": 0, "acfe_flexible_layouts_settings": 0, "acfe_flexible_async": [], "acfe_flexible_add_actions": [], "acfe_flexible_remove_button": [], "acfe_flexible_layouts_state": "user", "acfe_flexible_modal_edit": {"acfe_flexible_modal_edit_enabled": "0", "acfe_flexible_modal_edit_size": "large"}, "acfe_flexible_modal": {"acfe_flexible_modal_enabled": "0", "acfe_flexible_modal_title": false, "acfe_flexible_modal_size": "full", "acfe_flexible_modal_col": "4", "acfe_flexible_modal_categories": false}, "layouts": {"layout_665ed50f51500": {"key": "layout_665ed50f51500", "name": "form", "label": "Form", "display": "block", "sub_fields": [{"key": "field_665ed568a1969", "label": "inputs", "name": "inputs", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_665ed59fa196a", "label": "type", "name": "type", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_665ed568a1969"}, {"key": "field_665ed5a8a196b", "label": "label", "name": "label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_665ed568a1969"}, {"key": "field_665ed5b1a196c", "label": "validation", "name": "validation", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "acfe_textarea_code": 1, "new_lines": "", "parent_repeater": "field_665ed568a1969"}, {"key": "field_665ed5bda196d", "label": "options", "name": "options", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": "", "acfe_textarea_code": 0, "parent_repeater": "field_665ed568a1969"}]}, {"key": "field_665f401cd404c", "label": "submit_btn_message", "name": "submit_btn_message", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_665f4027d404d", "label": "success_message", "name": "success_message", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_665f403bd404e", "label": "success_redirect", "name": "success_redirect", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "min": "", "max": "", "acfe_flexible_render_template": false, "acfe_flexible_render_style": false, "acfe_flexible_render_script": false, "acfe_flexible_thumbnail": false, "acfe_flexible_settings": false, "acfe_flexible_settings_size": "medium", "acfe_flexible_modal_edit_size": false, "acfe_flexible_category": false}, "layout_665edf63c6370": {"key": "layout_665edf63c6370", "name": "list", "label": "list", "display": "block", "sub_fields": [{"key": "field_665edf6dc6371", "label": "Items", "name": "items", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "table", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_665ee030c6372", "label": "content", "name": "content", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": "", "acfe_textarea_code": 0, "parent_repeater": "field_665edf6dc6371"}]}], "min": "", "max": "", "acfe_flexible_render_template": false, "acfe_flexible_render_style": false, "acfe_flexible_render_script": false, "acfe_flexible_thumbnail": false, "acfe_flexible_settings": false, "acfe_flexible_settings_size": "medium", "acfe_flexible_modal_edit_size": false, "acfe_flexible_category": false}, "layout_665ed6f8b73c8": {"key": "layout_665ed6f8b73c8", "name": "table", "label": "Table", "display": "block", "sub_fields": [{"key": "field_665ed713b73c9", "label": "columns", "name": "columns", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "acfe_repeater_stylised_button": 0, "rows_per_page": 20, "sub_fields": [{"key": "field_665ed72cb73ca", "label": "title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_665ed713b73c9"}, {"key": "field_665ed732b73cb", "label": "content-render-format", "name": "content-render-format", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_665ed713b73c9"}]}], "min": "", "max": "", "acfe_flexible_render_template": false, "acfe_flexible_render_style": false, "acfe_flexible_render_script": false, "acfe_flexible_thumbnail": false, "acfe_flexible_settings": false, "acfe_flexible_settings_size": "medium", "acfe_flexible_modal_edit_size": false, "acfe_flexible_category": false}, "layout_665ee1edc6375": {"key": "layout_665ee1edc6375", "name": "textblock", "label": "textBlock", "display": "block", "sub_fields": [{"key": "field_665ee20fc6376", "label": "text", "name": "text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "min": "", "max": "", "acfe_flexible_render_template": false, "acfe_flexible_render_style": false, "acfe_flexible_render_script": false, "acfe_flexible_thumbnail": false, "acfe_flexible_settings": false, "acfe_flexible_settings_size": "medium", "acfe_flexible_modal_edit_size": false, "acfe_flexible_category": false}}, "min": "", "max": "", "button_label": "Add Row", "acfe_flexible_layouts_previews": false, "parent_repeater": "field_8ae1"}]}]}, {"key": "field_8af", "label": "Notifications", "name": "notifications", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "block", "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "parent_repeater": "field_1650d43fec5e2", "sub_fields": [{"key": "field_8af1", "label": "Methods", "name": "methods", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "acfe_textarea_code": 0, "maxlength": "", "rows": "", "placeholder": "", "new_lines": "", "parent_repeater": "field_8af"}, {"key": "field_8af2", "label": "Recipient", "name": "recipient", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8af"}, {"key": "field_8af3", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_8af"}, {"key": "field_8af4", "label": "Message", "name": "message", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_8af"}]}, {"key": "field_8ag", "label": "Flowchart Mermaid", "name": "flowchart_mermaid", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "acfe_textarea_code": 0, "parent_repeater": "field_1650d43fec5e2"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "requirement"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": ["block_editor", "the_content", "excerpt", "discussion", "comments", "revisions", "slug", "author", "format", "page_attributes", "featured_image", "categories", "tags", "send-trackbacks"], "active": true, "description": "", "show_in_rest": 0, "acfe_display_title": "", "acfe_autosync": "", "acfe_form": 0, "acfe_meta": "", "acfe_note": ""}, {"key": "post_type_665005bf4d8d9", "title": "Requirements", "menu_order": 0, "active": true, "post_type": "requirement", "advanced_configuration": true, "import_source": "", "import_date": "", "labels": {"name": "Requirements", "singular_name": "Requirement", "menu_name": "Requirments", "all_items": "All Requirments", "edit_item": "Edit Requirement", "view_item": "View Requirement", "view_items": "View Requirments", "add_new_item": "Add New Requirement", "add_new": "Add New Requirement", "new_item": "New Requirement", "parent_item_colon": "Parent Requirement:", "search_items": "Search Requirments", "not_found": "No requirments found", "not_found_in_trash": "No requirments found in Trash", "archives": "Requirement Archives", "attributes": "Requirement Attributes", "featured_image": "", "set_featured_image": "", "remove_featured_image": "", "use_featured_image": "", "insert_into_item": "Insert into requirement", "uploaded_to_this_item": "Uploaded to this requirement", "filter_items_list": "Filter requirments list", "filter_by_date": "Filter requirments by date", "items_list_navigation": "Requirments list navigation", "items_list": "Requirments list", "item_published": "Requirement published.", "item_published_privately": "Requirement published privately.", "item_reverted_to_draft": "Requirement reverted to draft.", "item_scheduled": "Requirement scheduled.", "item_updated": "Requirement updated.", "item_link": "Requirement Link", "item_link_description": "A link to a requirement."}, "description": "", "public": true, "hierarchical": false, "exclude_from_search": false, "publicly_queryable": true, "show_ui": true, "show_in_menu": true, "admin_menu_parent": "", "show_in_admin_bar": true, "show_in_nav_menus": true, "show_in_rest": true, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Posts_Controller", "menu_position": "", "menu_icon": "", "rename_capabilities": false, "singular_capability_name": "post", "plural_capability_name": "posts", "supports": ["title"], "taxonomies": "", "has_archive": false, "has_archive_slug": "", "rewrite": {"permalink_rewrite": "post_type_key", "with_front": "1", "feeds": "0", "pages": "1"}, "query_var": "post_type_key", "query_var_name": "", "can_export": true, "delete_with_user": false, "register_meta_box_cb": "", "enter_title_here": ""}]