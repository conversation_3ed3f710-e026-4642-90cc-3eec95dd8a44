{"title": "التسجيل في المنصة", "business_goals": ["التأكد من أن كل مستخدم للمنصة قادر على تقديم الخدمة أو طلب الخدمة بطريقة آمنة وفعالة", "<PERSON><PERSON><PERSON> أن المعلومات المقدمة من المستخدمين صحيحة وحديثة"], "stakeholders": ["المستخدمون (العملاء، مقدمو الخدمات)", "إداريي النظام"], "main_steps": ["المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته", "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية", "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لاداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "المستخدم يتلقى إشعارات النظام (أو البريد في حالة استخدامه) تحتوي على تحديث حالة التسجيل (مقبول، مرفوض وسبب الرفض)"], "alternative_steps": ["إذا تم رفض حساب المستخدم، المستخدم يمكنه المراجعة والتعديل وإعادة تقديم الطلب"], "user_stories": ["كمستخدم عميل، أريد أن أقوم بالتسجيل في التطبيق بسرعة وسهولة حتى أتمكن من البدء في استخدام المنصة ، من أجل استخدام الخدمات المتوفرة", "كمقدم خدمة أريد أن أقوم بالتسجيل في التطبيق، من أجل عرض خدماتي و الحصول على فرص عمل جديدة وبناء شراكات", "كمستخدم، أنا بحاجة لتوثيق رقم الهاتف الخاص بي لضمان أمان حسابي", "كمستخدم، أنا بحاجة لتقديم وثائق تحقيق الشخصية الخاصة بي للتأكد من اعتمادي من النظام", "موظف إداري في المنصة، أرغب في تلقي إشعارات عند تسجيل مستخدم جديد أو مقدم خدمة جديد حتى أتمكن من فحص وثائقهم والتحقق منها للتأكد من التزام المنصة بمعايير الجودة والسلامة", "موظف إداري في المنصة، يحتاج إلى مراجعة طلبات الالتحاق, مراجعتها إما القبول أو وضع سبب الرفض", "كمستخدم، أريد تلقي تحديثات حول حالة طلب التسجيل الخاص بي، من أجل معرفة ما إذا كان مقبولًا أم مرفوضًا أم في انتظار المراجعة"], "performance_indicators": ["عدد المسجلين مع تصنيف الفئات (تاجر، ناقل، وسيط....../ تسجيل مقبول، تسجيل مرفوض، إعادة تسجيل....)", "الفترة الزمنية للتسجيل"], "use_cases": {"1.1": {"description-simple": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته", "actor-simple": "المستخدم (العميل أو مقدم الخدمة)", "preconditions-simple": ["يجب أن يكون المستخدم غير مسجل سابقًا في النظام"], "postconditions-simple": ["المستخدم يكون لديه حساب جديد في النظام"], "main_flow_steps-simple": ["فتح صفحة التسجيل", "إدخال البيانات الشخصية المطلوبة", "النقر على زر 'تسجيل'"], "description": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق إدخال بياناته الشخصية وإنشاء حساب جديد", "actor": "المستخدم (العميل أو مقدم الخدمة)", "preconditions": ["المستخدم غير مسجل سابقًا في النظام"], "postconditions": ["المستخدم يكون لديه حساب جديد في النظام"], "main_flow_steps": ["فتح صفحة التسجيل", "إدخال البيانات الشخصية المطلوبة (الاسم، البريد الإلكتروني، رقم الهاتف، كلمة المرور)", "النقر على زر 'تسجيل'", "النظام يتحقق من صحة البيانات المدخلة", "النظام ينشئ حسابًا جديدًا للمستخدم"], "alternative_flow_steps": [{"condition": "إذا كانت البيانات المدخلة غير صحيحة", "steps": ["النظام يعرض رسالة خطأ توضح البيانات غير الصحيحة", "المستخدم يعدل البيانات المدخلة", "النقر على زر 'تسجيل' مرة أخرى"]}], "exception_flow_steps": [{"condition": "إذا كان المستخدم مسجل مسبقًا", "steps": ["النظام يعرض رسالة تفيد بأن البريد الإلكتروني أو رقم الهاتف مستخدم مسبقًا", "المستخدم يمكنه استخدام خيار 'نسيت كلمة المرور' لاستعادة الوصول إلى الحساب"]}], "trigger": "المستخدم يرغب في إنشاء حساب جديد", "priority": "عالي", "business_rules": ["يجب أن تكون كلمة المرور قوية (تحتوي على حروف وأرقام ورموز)"], "assumptions": ["المستخدم يمتلك بريد إلكتروني ورقم هاتف صالحين"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["المستخدم يفتح صفحة التسجيل", "المستخدم يدخل البيانات الشخصية المطلوبة", "المستخدم ينقر على زر 'تسجيل'", "النظام يتحقق من صحة البيانات", "النظام ينشئ حسابًا جديدًا للمستخدم"], "inputs": ["الاسم", "الب<PERSON>يد الإلكتروني", "رقم الهاتف", "كلمة المرور"], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم جديد، أرغب في إنشاء حساب على النظام حتى أتمكن من استخدام الخدمات المتاحة"], "security_requirements": ["يجب التحقق من صحة البريد الإلكتروني ورقم الهاتف لتجنب التسجيلات الزائفة"], "integration_with_other_systems": [], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من إنشاء الحساب بنجاح", "اختبارات الأمان للتحقق من قوة كلمة المرور وصحة البيانات المدخلة"], "backend_details": {"api_endpoints": [{"method": "POST", "endpoint": "/api/user/register", "description": "تسجيل مستخدم جديد", "request": {"body": {"name": "string", "email": "string", "phone": "string", "password": "string"}, "headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"userId": "string", "message": "User registered successfully"}}, "400": {"description": "Bad Request"}}}]}, "frontend_details": {"screens": [{"name": "RegistrationScreen", "components": [{"type": "Form", "props": {"inputs": [{"label": "Name", "type": "text", "validation": "required", "error_message": "Name is required"}, {"label": "Email", "type": "email", "validation": "required|email", "error_message": "Valid email is required"}, {"label": "Phone", "type": "text", "validation": "required|phone", "error_message": "Valid phone number is required"}, {"label": "Password", "type": "password", "validation": "required|min:8|contains:letters,numbers", "error_message": "Password must be at least 8 characters long and contain letters and numbers"}], "submit_btn_message": "Register", "success_message": "Registration successful", "success_redirect": "LoginScreen"}}]}]}, "notifications": [{"methods": ["email"], "recipient": "المستخدم", "title": "Registration Successful", "message": "Your account has been successfully created. Welcome to our platform!"}], "flowchart_mermaid": "graph LR\n    A[User opens registration page] --> B[User enters personal details]\n    B --> C[User clicks 'Register']\n    C --> D[System validates data]\n    D --> E[System creates new account]"}, "1.2": {"description-simple": "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية", "actor-simple": "المستخدم", "preconditions-simple": ["فتح صفحة شروط الخدمة وسياسة الخصوصية"], "postconditions-simple": ["تمت الموافقة على الشروط والسياسات"], "main_flow_steps-simple": ["عرض شروط الخدمة وسياسة الخصوصية", "النقر على مربع الموافقة", "النقر على زر 'أوافق'"], "description": "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية قبل إنشاء الحساب", "actor": "المستخدم", "preconditions": ["فتح صفحة شروط الخدمة وسياسة الخصوصية"], "postconditions": ["تمت الموافقة على الشروط والسياسات"], "main_flow_steps": ["عرض شروط الخدمة وسياسة الخصوصية", "النقر على مربع الموافقة", "النقر على زر 'أوافق'"], "alternative_flow_steps": [], "exception_flow_steps": [], "trigger": "المستخدم يرغب في التسجيل ويجب عليه الموافقة على الشروط والسياسات", "priority": "عالي", "business_rules": [], "assumptions": ["المستخدم لديه الوقت الكافي لقراءة الشروط والسياسات"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["فتح صفحة شروط الخدمة وسياسة الخصوصية", "قراءة الشروط والسياسات", "الموافقة على الشروط والسياسات بالنقر على المربع والنقر على زر 'أوافق'"], "inputs": [], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم جديد، أرغب في قراءة والموافقة على شروط الخدمة وسياسة الخصوصية قبل إنشاء حسابي لضمان فهمي للالتزامات والحقوق"], "security_requirements": [], "integration_with_other_systems": [], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من عرض الشروط والسياسات بشكل صحيح", "اختبارات للتحقق من أن الموافقة على الشروط والسياسات مطلوبة قبل المتابعة"], "backend_details": {"api_endpoints": [{"method": "GET", "endpoint": "/api/terms-and-conditions", "description": "عرض شروط الخدمة وسياسة الخصوصية", "request": {"headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"terms": "string", "privacyPolicy": "string"}}, "400": {"description": "Bad Request"}}}, {"method": "POST", "endpoint": "/api/accept-terms", "description": "الموافقة على شروط الخدمة وسياسة الخصوصية", "request": {"body": {"userId": "string", "acceptedTerms": "boolean"}, "headers": {"Authorization": "Bearer token", "Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "Terms accepted"}}, "400": {"description": "Bad Request"}}}]}, "frontend_details": {"screens": [{"name": "TermsAndConditionsScreen", "components": [{"type": "TextBlock", "props": {"text": "Terms and conditions content here.."}}, {"type": "Checkbox", "props": {"label": "I agree to the terms and conditions", "id": "terms-checkbox"}}, {"type": "<PERSON><PERSON>", "props": {"label": "Agree", "onClick": "submitAcceptance"}}]}]}, "notifications": [], "flowchart_mermaid": "graph LR\n    A[User opens terms and conditions page] --> B[User reads terms and conditions]\n    B --> C[User checks the 'I agree' checkbox]\n    C --> D[User clicks 'Agree']"}, "1.3": {"description-simple": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP", "actor-simple": "المستخدم", "preconditions-simple": ["إدخال رقم الهاتف بشكل صحيح"], "postconditions-simple": ["تم توثيق رقم الهاتف بنجاح"], "main_flow_steps-simple": ["إدخال رقم الهاتف", "النقر على زر 'إرسال OTP'", "استلام رسالة OTP على الهاتف", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'توثيق'"], "description": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لإداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "actor": "المستخدم", "preconditions": ["إكمال خطوات التسجيل السابقة", "فتح صفحة توثيق رقم الهاتف"], "postconditions": ["تم توثيق رقم الهاتف بنجاح"], "main_flow_steps": ["المستخدم يفتح صفحة توثيق رقم الهاتف", "إدخال رقم الهاتف", "النقر على زر 'إرسال رمز التحقق'", "استلام رسالة OTP على الهاتف المدخل", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'تحقق'"], "alternative_flow_steps": [{"condition": "إذا لم يستلم المستخدم رسالة OTP", "steps": ["النقر على زر 'إعادة إرسال الرمز'", "استلام رمز OTP جديد", "إدخال الرمز الجديد في الحقل المخصص", "النقر على زر 'تحقق'"]}], "exception_flow_steps": [{"condition": "إذا أدخل المستخدم رمز OTP غير صحيح", "steps": ["النظام يعرض رسالة خطأ تطلب إدخال الرمز الصحيح", "إعادة إدخال رمز OTP صحيح", "النقر على زر 'تحقق' مرة أخرى"]}], "trigger": "المستخدم يرغب في توثيق رقم هاتفه كجزء من عملية التسجيل", "priority": "عالي", "business_rules": [], "assumptions": ["المستخدم يمتلك رقم هاتف صالح ويستطيع استلام رسائل OTP"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["المستخدم يفتح صفحة توثيق رقم الهاتف", "إدخال رقم الهاتف", "النقر على زر 'إرسال رمز التحقق'", "استلام رسالة OTP على الهاتف المدخل", "إدخال رمز OTP في الحقل المخصص", "النقر على زر 'تحقق'"], "inputs": ["رقم الهاتف", "<PERSON><PERSON><PERSON> OTP"], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم جديد، أرغب في توثيق رقم هاتفي لضمان أمان حسابي"], "security_requirements": ["يجب التحقق من صحة رمز OTP لضمان توثيق رقم الهاتف بشكل صحيح"], "integration_with_other_systems": ["منصة نفاذ لتوثيق رقم الهاتف"], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من عملية إرسال واستلام رمز OTP", "اختبارات الأمان للتحقق من صحة رمز OTP المدخل"], "backend_details": {"api_endpoints": [{"method": "POST", "endpoint": "/api/user/verify-phone", "description": "إرسال رمز OTP لتوثيق رقم الهاتف", "request": {"body": {"phoneNumber": "string"}, "headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "OTP sent successfully"}}, "400": {"description": "Bad Request"}}}, {"method": "POST", "endpoint": "/api/user/confirm-otp", "description": "التحقق من رمز OTP", "request": {"body": {"phoneNumber": "string", "otp": "string"}, "headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "Phone verified successfully"}}, "400": {"description": "Invalid OTP"}}}]}, "frontend_details": {"screens": [{"name": "PhoneVerificationScreen", "components": [{"type": "Form", "props": {"inputs": [{"label": "Phone Number", "type": "text", "validation": "required|phone", "error_message": "Valid phone number is required"}, {"label": "OTP", "type": "text", "validation": "required", "error_message": "OTP is required"}], "submit_btn_message": "Verify", "success_message": "Phone verified successfully", "success_redirect": "NextRegistrationStep"}}]}]}, "notifications": [{"methods": ["SMS"], "recipient": "المستخدم", "title": "OTP Code", "message": "Your OTP code is: {code}"}], "flowchart_mermaid": "graph LR\n    A[User opens phone verification page] --> B[User enters phone number]\n    B --> C[User clicks 'Send OTP']\n    C --> D[User receives OTP]\n    D --> E[User enters OTP]\n    E --> F[User clicks 'Verify']\n    F --> G[Phone verified successfully]"}, "1.4": {"description-simple": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "actor-simple": "المستخدم", "preconditions-simple": ["إكمال خطوات التسجيل الأساسية", "توافر الوثائق المطلوبة"], "postconditions-simple": ["تم تحميل الوثائق التعريفية المطلوبة"], "main_flow_steps-simple": ["اختيار نوع الوثيقة التعريفية", "تحميل صورة أو نسخة من الوثيقة", "النقر على زر 'تحميل الوثيقة'"], "description": "المستخدم يقوم بتأكيد عنوان بريده الإلكتروني عبر رابط التحقق المرسل إلى بريده الإلكتروني أثناء عملية التسجيل", "actor": "المستخدم", "preconditions": ["إكمال خطوات التسجيل السابقة", "فتح صفحة تأكيد البريد الإلكتروني"], "postconditions": ["تم تأكيد عنوان البريد الإلكتروني بنجاح"], "main_flow_steps": ["المستخدم يفتح صفحة تأكيد البريد الإلكتروني", "النقر على رابط التحقق المرسل إلى البريد الإلكتروني", "النظام يتحقق من صحة الرابط", "النظام يؤكد عنوان البريد الإلكتروني"], "alternative_flow_steps": [{"condition": "إذا لم يستلم المستخدم رسالة التحقق", "steps": ["النقر على زر 'إعادة إرسال رسالة التحقق'", "استلام رسالة تحقق جديدة", "النقر على رابط التحقق الجديد في البريد الإلكتروني"]}], "exception_flow_steps": [{"condition": "إذا أدخل المستخدم رابط تحقق غير صالح", "steps": ["النظام يعرض رسالة خطأ تطلب إعادة المحاولة", "إعادة النقر على رابط تحقق صالح", "النقر على زر 'إعادة إرسال رسالة التحقق' إذا لزم الأمر"]}], "trigger": "المستخدم يرغب في تأكيد عنوان بريده الإلكتروني", "priority": "عالي", "business_rules": [], "assumptions": ["المستخدم يمتلك بريد إلكتروني صالح ويستطيع الوصول إليه"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["المستخدم يفتح صفحة تأكيد البريد الإلكتروني", "النقر على رابط التحقق المرسل إلى البريد الإلكتروني", "النظام يتحقق من صحة الرابط", "النظام يؤكد عنوان البريد الإلكتروني"], "inputs": ["رابط التحقق"], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم جديد، أرغب في تأكيد عنوان بريدي الإلكتروني لضمان صحة الاتصال بي"], "security_requirements": ["يجب التحقق من صحة رابط التحقق لضمان تأكيد البريد الإلكتروني بشكل صحيح"], "integration_with_other_systems": [], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من عملية إرسال واستلام رابط التحقق", "اختبارات الأمان للتحقق من صحة رابط التحقق المدخل"], "backend_details": {"api_endpoints": [{"method": "POST", "endpoint": "/api/user/verify-email", "description": "التحقق من البريد الإلكتروني للمستخدم", "request": {"body": {"userId": "string", "verificationToken": "string"}, "headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "Email verified successfully"}}, "400": {"description": "Bad Request"}}}]}, "frontend_details": {"screens": [{"name": "EmailVerificationScreen", "components": [{"type": "TextBlock", "props": {"text": "A verification link has been sent to your email. Please click on the link to verify your email address"}}, {"type": "<PERSON><PERSON>", "props": {"label": "Resend Verification Email", "onClick": "resendVerificationEmail"}}]}]}, "notifications": [{"methods": ["email"], "recipient": "المستخدم", "title": "Email Verification", "message": "Please click on the following link to verify your email address: {verification_link}"}], "flowchart_mermaid": "graph LR\n    A[User opens email verification page] --> B[Clicks on verification link in email]\n    B --> C[System verifies link]\n    C --> D[Email address confirmed]"}, "1.5": {"description-simple": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل", "actor-simple": "المستخدم", "preconditions-simple": ["إكمال جميع خطوات التسجيل السابقة"], "postconditions-simple": ["تم إشعار المستخدم بحالة التسجيل"], "main_flow_steps-simple": ["انتظار مراجعة وثائق التسجيل من قبل النظام", "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)", "متابعة التعليمات حسب حالة التسجيل"], "description": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل سواء كان مقبولًا أو مرفوضًا مع سبب الرفض إذا كان متاحًا", "actor": "المستخدم", "preconditions": ["إكمال جميع خطوات التسجيل السابقة"], "postconditions": ["تم إشعار المستخدم بحالة التسجيل"], "main_flow_steps": ["انتظار مراجعة وثائق التسجيل من قبل النظام", "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)", "متابعة التعليمات حسب حالة التسجيل"], "alternative_flow_steps": [], "exception_flow_steps": [], "trigger": "إكمال المستخدم لخطوات التسجيل وانتظار الموافقة", "priority": "عالي", "business_rules": [], "assumptions": ["النظام يقوم بمراجعة الوثائق في وقت مناسب"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["النظام يراجع وثائق التسجيل", "النظام يرسل إشعارًا للمستخدم بحالة التسجيل", "المستخدم يتلقى الإشعار ويتابع التعليمات المناسبة"], "inputs": [], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم، أرغب في تلقي إشعار بحالة تسجيل حسابي لمعرفة ما إذا كان قد تم قبوله أو رفضه وما هي الخطوات التالية"], "security_requirements": [], "integration_with_other_systems": [], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من إرسال واستلام الإشعارات بحالة التسجيل", "اختبارات للتحقق من ظهور سبب الرفض في الإشعارات في حال وجوده"], "backend_details": {"api_endpoints": [{"method": "POST", "endpoint": "/api/user/registration-status", "description": "إرسال إشعار حالة التسجيل", "request": {"body": {"userId": "string", "status": "string", "rejectionReason": "string"}, "headers": {"Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "Notification sent successfully"}}, "400": {"description": "Bad Request"}}}]}, "frontend_details": {"screens": [{"name": "RegistrationStatusScreen", "components": [{"type": "TextBlock", "props": {"text": "Your registration status will be updated here"}}]}]}, "notifications": [{"methods": ["push notification", "in the notification bar", "email"], "recipient": "المستخدم", "title": "Registration Status Update", "message": "Your registration has been {status}. {rejectionReason}"}], "flowchart_mermaid": "graph LR\n    A[Complete registration steps] --> B[System reviews registration documents]\n    B --> C[System sends registration status notification]\n    C --> D[User receives notification and follows instructions]"}, "1.6": {"description-simple": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor-simple": "المستخدم", "preconditions-simple": ["استلام إشعار برفض الحساب", "توافر سبب الرفض"], "postconditions-simple": ["إعادة تقديم طلب التسجيل بعد التعديل"], "main_flow_steps-simple": ["قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "description": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor": "المستخدم", "preconditions": ["استلام إشعار برفض الحساب", "توافر سبب الرفض"], "postconditions": ["إعادة تقديم طلب التسجيل بعد التعديل"], "main_flow_steps": ["قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "alternative_flow_steps": [], "exception_flow_steps": [], "trigger": "استلام إشعار برفض الحساب", "priority": "عالي", "business_rules": [], "assumptions": ["المستخدم يمكنه تعديل البيانات أو الوثائق المطلوبة"], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": [], "notes_and_issues": [], "events_sequence": ["استلام إشعار برفض الحساب", "قراءة سبب الرفض", "تعديل البيانات أو الوثائق المطلوبة", "إعادة تقديم طلب التسجيل"], "inputs": [], "outputs": [], "user_interactions": [], "special_conditions": [], "usage_scenarios": ["كمستخدم، أرغب في إعادة تقديم طلب التسجيل بعد تعديله لضمان قبولي في النظام"], "security_requirements": [], "integration_with_other_systems": [], "constraints_and_assumptions": [], "testing_requirements": ["اختبارات وظيفية للتحقق من عملية إعادة التقديم بعد التعديل"], "backend_details": {"api_endpoints": [{"method": "POST", "endpoint": "/api/user/resubmit-registration", "description": "إعادة تقديم طلب التسجيل بعد التعديل", "request": {"body": {"userId": "string", "updatedData": "object"}, "headers": {"Authorization": "Bearer token", "Content-Type": "application/json"}}, "response": {"200": {"description": "Success", "body": {"status": "string", "message": "Registration resubmitted successfully"}}, "400": {"description": "Bad Request"}}}]}, "frontend_details": {"screens": [{"name": "ResubmitRegistrationScreen", "components": [{"type": "Form", "props": {"inputs": [{"label": "Updated Data", "type": "text", "validation": "required", "error_message": "Updated data is required"}], "submit_btn_message": "Resubmit", "success_message": "Registration resubmitted successfully", "success_redirect": "RegistrationStatusScreen"}}]}]}, "notifications": [], "flowchart_mermaid": "rrrr"}}}