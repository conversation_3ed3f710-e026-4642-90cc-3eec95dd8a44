<?php
function getRequirementById($id) {

    $file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';

    // Load JSON file
    $jsonData = file_get_contents($file_path);

    $data = json_decode($jsonData, true);

    if (isset($data[$id])) {
        return $data[$id];
    } else {
        return null; // or handle the error as per your requirement
    }
}

function getRequirementNameById($id) {

    $file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';

    // Load JSON file
    $jsonData = file_get_contents($file_path);

    $data = json_decode($jsonData, true);

    if (isset($data[$id]['title'])) {
        return $data[$id]['title'];
    } else {
        return null; // or handle the error as per your requirement
    }
}

function not_allowed( $label ){
    $not_allowedd =  array(
        'case_id',
        'id',
        'is_new',
        'description_simple',
        'actor_simple',
        'preconditions_simple',
        'postconditions_simple',
        'main_flow_steps_simple',
        'trigger',
        'frequency_of_use',
        'flowchart_mermaid',
        'رقم التسلسل',
        'priority',
        'main_flow_steps'
    );

    if( in_array( $label, $not_allowedd ) ) {
        return true;
    }
}
function generateRequirementTable($requirementData) {
    $translations = [
        "title" => "العنوان",
        "business_goals" => "أهداف العمل",
        "stakeholders" => "أصحاب المصلحة",
        "main_steps" => "الخطوات الرئيسية",
        "alternative_steps" => "الخطوات البديلة",
        "user_stories" => "قصص المستخدم",
        "performance_indicators" => "مؤشرات الأداء",
        "use_cases" => "حالات الاستخدام" // This will be excluded
    ];

    $html = '<table class="rtl">';
    $html .= '<thead><tr ><th>العنوان</th><th>التفاصيل</th></tr></thead>';
    $html .= '<tbody>';

    foreach ($requirementData as $key => $value) {
        if ($key === "use_cases") {
            continue; // Skip "use_cases"
        }

        $html .= '<tr>';
        $html .= '<td>' . (isset($translations[$key]) ? $translations[$key] : $key) . '</td>';
        $html .= '<td>';
        if (is_array($value)) {
            $html .= '<ul style="list-style-type: disc; padding-left: 20px;">';
            foreach ($value as $item) {
                $html .= '<li>' . $item . '</li>';
            }
            $html .= '</ul>';
        } else {
            $html .= $value;
        }
        $html .= '</td>';
        $html .= '</tr>';
    }

    $html .= '</tbody>';
    $html .= '</table>';
    return $html;
}

function generateUseCasesTable($useCasesData , $label ) {
       // Translation array for the titles
       $translations = [
        "description" => "الوصف",
        "description-simple" => "الوصف",
        "actor" => "الممثل",
        "actor-simple" => "الممثل",
        "preconditions" => "الشروط المسبقة",
        "preconditions-simple" => "الشروط المسبقة",
        "postconditions" => "الشروط اللاحقة",
        "postconditions-simple" => "الشروط اللاحقة",
        "main_flow_steps" => "الخطوات الرئيسية",
        "main_flow_steps-simple" => "الخطوات الرئيسية",
        "alternative_flow_steps" => "الخطوات البديلة",
        "exception_flow_steps" => "الخطوات الاستثنائية",
        "trigger" => "المحفز",
        "priority" => "الأولوية",
        "business_rules" => "القواعد التجارية",
        "assumptions" => "الافتراضات",
        "frequency_of_use" => "تكرار الاستخدام",
        "special_requirements" => "المتطلبات الخاصة",
        "notes_and_issues" => "الملاحظات والمشاكل",
        "events_sequence" => "تسلسل الأحداث",
        "inputs" => "المدخلات",
        "outputs" => "المخرجات",
        "user_interactions" => "تفاعلات المستخدم",
        "special_conditions" => "الشروط الخاصة",
        "usage_scenarios" => "سيناريوهات الاستخدام",
        "security_requirements" => "متطلبات الأمان",
        "integration_with_other_systems" => "التكامل مع الأنظمة الأخرى",
        "constraints_and_assumptions" => "القيود والافتراضات",
        "testing_requirements" => "متطلبات الاختبار",
        "flowchart_mermaid" => "مخطط التدفق",
        'backend_details' => 'تفاصيل ال API',
        'frontend_details' => 'تفاصيل الواجهات',
        'notifications' => 'الاشعارات',
    ];

    $html = '';

    foreach ($useCasesData as $useCaseNumber => $useCase) {
        $pure_case_id = explode( '.' , $useCaseNumber );
        $pure_case_id = $label .'.' . $pure_case_id[1];

        // Add Use Case Title
        if (isset($useCase['description'])) {
            $html .= '<h4>' . $pure_case_id . '- ' . $useCase['description'] . '</h4>';
        } else {
            $html .= '<h4>' . $pure_case_id . '</h4>';
        }

        // Add Flowchart if exists
        if (isset($useCase['flowchart_mermaid'])) {
            $html .= '<div class="mermaid">' .  str_replace('TD', 'LR', $useCase['flowchart_mermaid'])  . '</div>';
        }

        // Add Use Case Details Table
        $html .= '<table>';
        $html .= '<thead><tr><th scope="col" style="width:15%;">العنوان</th><th>التفاصيل</th scope="col" style="width:85%;"></tr></thead>';
        $html .= '<tbody>';

        foreach ($useCase as $key => $value) {
            


            if ($key === 'flowchart_mermaid') {
                continue; // Skip flowchart_mermaid key for now
            }

            if( not_allowed( $key )  ) {
                continue; 
            }
            if( empty($value) ) {
                continue; 
            }

            $html .= '<tr>';
            $html .= '<td>' . (isset($translations[$key]) ? $translations[$key] : $key) . '</td>';
            $html .= '<td>';
            if (is_array($value)) {
                if( $key === 'alternative_flow_steps' || $key === 'exception_flow_steps') {
                    foreach ($value as $val) {
                        // prr($val);
                        $html .= '<strong>'. $val['condition'] .'</strong>';
                        $html .= '<ul>';
                        foreach ((array)$val['steps']as $step) {
                            $html .= '<li>' . $step . '</li>';
                        }
                        $html .= '</ul>';
                    }
                } elseif( $key === 'notifications' ){ 
                    if( !empty($value) ) {
                        // prr($value);
                        foreach ($value as $val) {
                            // prr($val); 
                            $html .= '<strong>عن طريق : </strong> ' .implode(', ', $val['methods'] ) . '<br>';
                            $html .= '<strong>المستقبل : </strong><span>'. array_to_multiline_string($val['recipient']) ?? '' .'</span> ';
                            $html .= '<strong>العنوان : </strong><span>'. $val['title'] .'</span><br>';
                            $html .= '<strong>الرسالة : </strong><span>'. array_to_multiline_string($val['message']) ?? '' .'</span> ';
    
                        } 
                    }
                } elseif( $key === 'backend_details' ){
                      $html .= renderBackendDetailsTable($value);
                } elseif( $key === 'frontend_details' ) {
                      $html .= renderFrontendDetails($value);
                } else {
                    $html .= '<ul>';
                    foreach ($value as $item) {
                        $html .= '<li>' . (is_array($item) ? array_to_multiline_string($item) : $item) . '</li>';
                    }
                    $html .= '</ul>';
                }
            } else {
                $html .= $value;
            }
            $html .= '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';

        
    }

    return $html;
}

function renderBackendDetailsTable($backendDetails) {
    $html = '';
    $html .= '<table>';
    
    foreach ($backendDetails['api_endpoints'] as $endpoint) {
        // Merge Method and Endpoint into one row
        $html .= '<tr>';
        $html .= '<td colspan="3"><strong style="color:red;">Method</strong>: ' . $endpoint['method'] . ' <strong style="color:green;">Endpoint</strong>: ' . $endpoint['endpoint'] . '</td>';
        $html .= '</tr>';
        
        $html .= '<tr>';
        $html .= '<td><strong>Request Body</strong></td>';
        $html .= '<td colspan="2"><strong>Request Headers</strong></td>';
        $html .= '</tr>';
        
        $html .= '<tr>';
        $html .= '<td>';
        if (isset($endpoint['request']['body'])) {
            foreach ($endpoint['request']['body'] as $key => $value) {
                $html .= '<strong>' . $key . ':</strong> ' . array_to_multiline_string($value) . '<br>';
            }
        }
        $html .= '</td>';
        $html .= '<td colspan="2">';
        if (isset($endpoint['request']['headers'])) {
            foreach ($endpoint['request']['headers'] as $key => $value) {
                $html .= '<strong>' . $key . ':</strong> ' . $value . '<br>';
            }
        }
        $html .= '</td>';
        $html .= '</tr>';
        
        $responseCount = count($endpoint['response']);
        $html .= '<tr>';
        $html .= '<td rowspan="' . ($responseCount + 1) . '"><strong>Response</strong></td>';
        $html .= '<td><strong>Status</strong></td>';
        $html .= '<td><strong>Body</strong></td>';
        $html .= '</tr>';
        
        foreach ($endpoint['response'] as $status => $response) {
            $html .= '<tr>';
            $html .= '<td>' . $status . '</td>';
            $html .= '<td>';
            if (isset($response['body'])) {
                foreach ($response['body'] as $key => $value) {
                    $html .= '<strong>' . $key . ':</strong> ' . $value . '<br>';
                }
            }
            if( isset($response['description']) ){
                $html .= '<strong>Description: </strong> ' . $response['description'] . '<br>';
            }
            $html .= '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</table>';
    return $html;
}


function array_to_multiline_string($data) {
    if( is_array($data) ) {
        $html = '<ul>';
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if ($key === 'inputs' || $key === 'items' || $key === 'columns') {
                    $html .= inputs_to_multiline_string($value);
                } else {
                    $html .= '<li><strong>' . ucfirst($key) . ':</strong> ' . array_to_multiline_string($value) . '</li>';
                }
            } else {
                if (!is_numeric($key)) {
                    $html .= '<li><strong>' . ucfirst($key) . ':</strong> ' . $value . '</li>';
                }
            }
        }
        $html .= '</ul>';
    } else {
        $html = $data;
    }  
    return $html;
}

function inputs_to_multiline_string($inputs) {
    $html = '<ul>';
    foreach ($inputs as $input) {
        $html .= '<li>';
        if(is_array($input)) {
            foreach ($input as $key => $value) {
                if( is_array($value) ) {
                    $html .= array_to_multiline_string($value);
                }else {
                    $html .= '<strong>' . ucfirst($key) . ':</strong> ' . $value . '<br>';
                }
            }
        } else {
            $html .= $input . '<br>';
        }
        $html = rtrim($html, ', ');
        $html .= '</li>';
    }
    $html .= '</ul>';
    return $html;
}

function renderFrontendDetails($frontendDetails) {
    $html = '';
    
    foreach ($frontendDetails['screens'] as $screen) {
        $html .= '<strong>' . $screen['name'] . '</strong>';
        foreach ($screen['components'] as $component) {
            $html .= '<ul>';
            $html .= '<li><strong style="color:blue;">' . $component['type'] . '</strong></li>';
            if (isset($component['props'])) {
                $html .= array_to_multiline_string($component['props']);
            }
            if (isset($component['props']['submit_btn_message'])) {
                $html .= '<li><strong>رسالة زر الإرسال:</strong> ' . $component['props']['submit_btn_message'] . '</li>';
            }
            if (isset($component['props']['success_message'])) {
                $html .= '<li><strong>رسالة النجاح:</strong> ' . $component['props']['success_message'] . '</li>';
            }
            if (isset($component['props']['success_redirect'])) {
                $html .= '<li><strong>إعادة توجيه النجاح:</strong> ' . $component['props']['success_redirect'] . '</li>';
            }
            $html .= '</ul>';
        }
    }
    
    return $html;
}

// function array_to_multiline_string($array) {
//     if (is_array($array)) {
//         $html = '<ul>';
//         foreach ($array as $key => $value) {
//             $html .= '<li>';
//             if (is_array($value)) {
//                 $html .= "$key: " . array_to_multiline_string($value);
//             } else {
//                 $html .= "$key: $value";
//             }
//             $html .= '</li>';
//         }
//         $html .= '</ul>';
//         return $html;
//     } else {
//         return $array;
//     }
// }