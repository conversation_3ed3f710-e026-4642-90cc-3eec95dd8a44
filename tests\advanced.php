<?php
$file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';

if (!file_exists($file_path)) {
    die("Error: File not found.");
}

$file_contents = file_get_contents($file_path);

if ($file_contents === false) {
    die("Error: Unable to read the file.");
}

$json_data = json_decode($file_contents, true);

$data = [];

if( isset($_GET['case']) ){
    
}

$advanced = [];
foreach ($json_data as $key => $req ) {
    foreach( $req['use_cases'] as $k => $case ) {
        $advanced[$key] = $case['frontend_details'];
    }

}

function listTypes($data) {
    $types = [];
    
    array_walk_recursive($data, function($value, $key) use (&$types) {
        if ($key === 'type') {
            $types[$value] = $value;
        }
    });
    
    return array_unique($types);
}

prr( listTypes($advanced) );