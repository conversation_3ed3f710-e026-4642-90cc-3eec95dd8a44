<!DOCTYPE html>
<html lang="ar" dir="rtl" class="mdl-js"><head>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT RTL</title>
    <!-- إضافة Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style>
        .reqs {
            margin: 50px auto;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 10px;
        }
        .reqs h3{
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container reqs">      

    <?php
    $content = file_get_contents( ANALYST_PLUGIN_DIR. 'data/brd_reqs_with-full.json' );
    $content = json_decode( $content, true );
    $counter = 0;
    $limit = 1;
    foreach ($content as $key => $req ) {
        render_req( $key , $req );
        $limit--;
        if( $limit < 0 ) {
            break;
        }
    }
    ?>
    </div>
    <div class="container reqs">      
        <?php prr( $content['4.2.1'] ); ?>
    </div>
    <!-- إضافة jQuery و Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
</body>
</html>