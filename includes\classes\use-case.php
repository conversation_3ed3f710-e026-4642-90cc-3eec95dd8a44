<?php

class USE_CASES{

    public $page_template_file  = 'usecases_sync.php';

    public function __construct(){
        add_filter('theme_page_templates', array( $this, 'custom_page_template_register' ) );
        add_filter('template_include', array( $this, 'custom_page_template_file_path' ) );
        add_action('rest_api_init', array( $this, 'api_endpoint_register' ));
        add_action('admin_enqueue_scripts', array($this , 'custom_admin_scripts' ) );
        add_action('wp_ajax_get_use_case', array( $this , 'get_use_case' ) );
        add_action('wp_ajax_save_use_case', array ( $this , 'save_use_case' ) );
        add_action('wp_ajax_load_advanced_use_cases', array ( $this ,  'load_advanced_use_cases') ) ;
    }

    public function custom_page_template_register($templates) {
        $templates[ $this->page_template_file ] = 'use cases';
        return $templates;
    }

    public function custom_page_template_file_path($template) {
        global $post;

        if (!$post) {
            return $template;
        }

        if (get_post_meta( $post->ID, '_wp_page_template', true ) == $this->page_template_file ) {
            $file = ANALYST_PLUGIN_DIR . 'views/' . $this->page_template_file;
            if (file_exists($file)) {
                return $file;
            }
        }

        return $template;
    }

    public function api_endpoint_register() {
        // http://analyst/wp-json/system_analyst/v1/chat
        register_rest_route( ANALYST_SLUG . '/v1', '/casestudy', array(
            'methods' => 'GET',
            'callback' => array($this, 'handle_api_request'),
            'permission_callback' => '__return_true', // يمكن تعديل هذا للتحقق من الأذونات
        ));
    }

    // معالجة الطلب
    public function handle_api_request( $request ) {

        require_once ANALYST_PLUGIN_DIR . 'includes/classes/OpenAIFineTuner.php';
        $message = $request->get_param('message');

        if (empty($message)) {
            return new WP_REST_Response(['error' => 'No message provided.'], 400);
        }
        $fineTuner = new OpenAIFineTuner();
        $response = $fineTuner->generateText( $message );
        $return = array(
            'message' => $response ,
            'ID'      => 1
        );
        wp_send_json_success( $return );
    }

    public function sync_render(){
        view( 'usecases_sync' , [] , ANALYST_PLUGIN_DIR );
    }
    
    function custom_admin_scripts() {
        wp_enqueue_script('sync-script', ANALYST_PLUGIN_URL . 'views/assets/js/sync.js' , array('jquery'), time(), true);
        $file_path = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
        if (file_exists($file_path)) {
            $json_data = file_get_contents($file_path);
            $reqs = json_decode($json_data, true);
            $cases = [];

            foreach ((array)$reqs as $key => $req) {
                foreach ($req['use_cases'] as $case_key => $case) {
                    $cases[ $case_key ] = $case ;
                }
            }

            wp_localize_script('sync-script', 'useCases', $cases);
        }
    }
    
    function get_use_case() {
        $use_case_id = sanitize_text_field($_REQUEST['use_case_id']);
        $file_path_full = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
        $file_path_advanced = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
        $case = [];
        $is_advanced = false;
    
        if (file_exists($file_path_advanced)) {
            $json_data = file_get_contents($file_path_advanced);
            $reqs = json_decode($json_data, true);
            list($req_key, $case_id) = $use_case_id;
    
            if (isset($reqs[$req_key])) {
                foreach ($reqs[$req_key]['advanced_use_cases'] as $use_case) {
                    if ($use_case['id'] === $case_id) {
                        $case = $use_case;
                        $is_advanced = true;
                        break;
                    }
                }
            }
        }
    
        if (!$is_advanced && file_exists($file_path_full)) {
            $json_data_full = file_get_contents($file_path_full);
            $reqs_full = json_decode($json_data_full, true);
            list($req_key, $case_id) = explode('-', $use_case_id);
    
            if (isset($reqs_full[$req_key])) {
                foreach ($reqs_full[$req_key]['use_cases'] as $use_case) {
                    if ($use_case['id'] === $case_id) {
                        $case = $use_case;
                        break;
                    }
                }
            }
        }
    
        if (!empty($case)) {
            wp_send_json_success(['case' => $case, 'is_advanced' => $is_advanced]);
        } else {
            wp_send_json_error('Case not found.');
        }
    }
    

    function save_use_case() {
        
        $use_case_id = sanitize_text_field($_REQUEST['use_case_id']);
        $updated_use_case = json_decode(stripslashes(sanitize_textarea_field($_REQUEST['updated_use_case'])), true);

        // تحقق من أن قيمة use_case_id تتطابق مع قيمة id داخل الحقل المدخل
        if ($use_case_id !== $updated_use_case['id']) {
            wp_send_json_error(['message' => 'قيمة use_case_id لا تتطابق مع قيمة id داخل الحقل المدخل.']);
            return;
        }

        $file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
        
        if (file_exists($file_path)) {
            $json_data = file_get_contents($file_path);
            $reqs = json_decode($json_data, true);
        } else {
            $reqs = [];
        }

        // Split the use_case_id to get the requirement key and the case id
        list($req_key, $case_id) = explode('-', $use_case_id, 2);

        if (!isset($reqs[$req_key])) {
            $file_path_full = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-full.json';
            if (file_exists($file_path_full)) {
                $json_data_full = file_get_contents($file_path_full);
                $reqs_full = json_decode($json_data_full, true);
                if (isset($reqs_full[$req_key])) {
                    $reqs[$req_key] = $reqs_full[$req_key];
                    $reqs[$req_key]['advanced_use_cases'] = [];
                }
            } else {
                wp_send_json_error(['message' => 'الملف الأصلي غير موجود.']);
                return;
            }
        }

        // Check if the use case already exists in the advanced use cases
        $updated = false;
        foreach ($reqs[$req_key]['advanced_use_cases'] as &$advanced_use_case) {
            if ($advanced_use_case['id'] === $updated_use_case['id']) {
                $advanced_use_case = $updated_use_case;
                $advanced_use_case['last_updated'] = date('Y-m-d H:i:s');
                $updated = true;
                break;
            }
        }

        // If not updated, add as new
        if (!$updated) {
            $updated_use_case['last_updated'] = date('Y-m-d H:i:s');
            $reqs[$req_key]['advanced_use_cases'][] = $updated_use_case;
        }

        // Sort the use cases by id within each requirement
        usort($reqs[$req_key]['advanced_use_cases'], function($a, $b) {
            return strcmp($a['id'], $b['id']);
        });

        // Sort the parent requirements by id
        uksort($reqs, function($a, $b) {
            return strcmp($a, $b);
        });

        file_put_contents($file_path, json_encode($reqs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        wp_send_json_success(['message' => 'تم حفظ الحالة بنجاح!']);
    }

    function load_advanced_use_cases() {
        $file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
        
        if (file_exists($file_path)) {
            $json_data = file_get_contents($file_path);
            $reqs = json_decode($json_data, true);
            wp_send_json_success($reqs);
        } else {
            wp_send_json_error(['message' => 'ملف البيانات غير موجود.']);
        }
    }
            
    
}
new ANALYST_CHAT;