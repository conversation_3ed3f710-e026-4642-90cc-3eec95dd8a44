{"1": {"1.1": [{"screens": [{"name": "شاشة التسجيل", "components": [{"type": "form", "props": {"inputs": [{"label": "الاسم", "type": "text", "validation": "required", "error_message": "الاسم مطلوب"}, {"label": "الب<PERSON>يد الإلكتروني", "type": "email", "validation": "required|email", "error_message": "البريد الإلكتروني صالح مطلوب"}, {"label": "الهاتف", "type": "text", "validation": "required|phone", "error_message": "رقم هاتف صالح مطلوب"}, {"label": "كلمة المرور", "type": "password", "validation": "required|min:8|contains:letters,numbers", "error_message": "Password must be at least 8 characters long and contain letters and numbers"}], "submit_btn_message": "Register", "success_message": "تم التسجيل بنجاح", "success_redirect": "شاشة تسجيل الدخول"}}]}]}], "1.2": [{"screens": [{"name": "شاشة الشروط والأحكام", "components": [{"type": "textBlock", "props": {"text": "Terms and conditions content here.."}}, {"type": "Checkbox", "props": {"label": "أو<PERSON><PERSON><PERSON> على الشروط والأحكام", "id": "terms-checkbox"}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافقة", "onClick": "submitAcceptance"}}]}]}], "1.3": [{"screens": [{"name": "شاشة التحقق من الهاتف", "components": [{"type": "form", "props": {"inputs": [{"label": "رقم الهاتف", "type": "text", "validation": "required|phone", "error_message": "رقم هاتف صالح مطلوب"}, {"label": "<PERSON><PERSON><PERSON> التحقق", "type": "text", "validation": "required", "error_message": "<PERSON><PERSON>ز التحقق مطلوب"}], "submit_btn_message": "Verify", "success_message": "تم التحقق من الهاتف بنجاح", "success_redirect": "NextRegistrationStep"}}]}]}], "1.4": [{"screens": [{"name": "شاشة التحقق من البريد الإلكتروني", "components": [{"type": "textBlock", "props": {"text": "A verification link has been sent to your email. Please click on the link to verify your email address"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إعادة إرسال بريد التحقق", "onClick": "resendVerificationEmail"}}]}]}], "1.5": [{"screens": [{"name": "شاشة حالة التسجيل", "components": [{"type": "textBlock", "props": {"text": "Your registration status will be updated here"}}]}]}], "1.6": [{"screens": [{"name": "شاشة إعادة تقديم التسجيل", "components": [{"type": "form", "props": {"inputs": [{"label": "تم تحديث البيانات", "type": "text", "validation": "required", "error_message": "تحديث البيانات مطلوب"}], "submit_btn_message": "Resubmit", "success_message": "تم إعادة تقديم التسجيل بنجاح", "success_redirect": "شاشة حالة التسجيل"}}]}]}]}, "2": {"2.1": [{"screens": [{"name": "لوحة التحكم الإدارية", "components": [{"type": "textBlock", "props": {"text": "لوحة التحكم"}}, {"type": "List", "props": {"items": [{"type": "text", "props": {"text": "إشعار 1: حدث مهم في النظام"}}, {"type": "text", "props": {"text": "إشعار 2: تحديث النظام المجدول"}}]}}]}]}], "2.2": [{"screens": [{"name": "إعدادات الإشعارات", "components": [{"type": "textBlock", "props": {"text": "إعدادات الإشعارات"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات البريد الإلكتروني", "id": "email-notifications"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات Push", "id": "push-notifications"}}, {"type": "<PERSON><PERSON>", "props": {"label": "ح<PERSON><PERSON> الإعدادات", "onClick": "saveNotificationSettings"}}]}]}], "2.3": [{"screens": [{"name": "مركز الإشعارات", "components": [{"type": "textBlock", "props": {"text": "مركز الإشعارات"}}, {"type": "List", "props": {"id": "notification-list", "items": []}}, {"type": "<PERSON><PERSON>", "props": {"label": "تحديث", "onClick": "refreshNotifications"}}]}]}], "2.4": [{"screens": [{"name": "تفضيلات الإشعارات", "components": [{"type": "textBlock", "props": {"text": "إعدادات الإشعارات"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات البريد الإلكتروني", "id": "email-notifications"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات Push", "id": "push-notifications"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات SMS", "id": "sms-notifications"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حفظ التفضيلات", "onClick": "saveNotificationPreferences"}}]}]}], "2.5": [{"screens": [{"name": "تفضيلات الإشعارات", "components": [{"type": "textBlock", "props": {"text": "إعدادات استلام الإشعارات"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات البريد الإلكتروني", "id": "email-notifications"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات Push", "id": "push-notifications"}}, {"type": "Checkbox", "props": {"label": "تمكين إشعارات SMS", "id": "sms-notifications"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حفظ التفضيلات", "onClick": "saveNotificationPreferences"}}]}]}]}, "3": {"3.1": [{"screens": [{"name": "شاشة تعديل الحساب", "components": [{"type": "textBlock", "props": {"text": "Edit your account information"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "الاسم", "id": "name-input", "validation": "Please enter a valid name"}}, {"type": "email", "props": {"label": "الب<PERSON>يد الإلكتروني", "id": "email-input", "validation": "Please enter a valid email"}}, {"type": "password", "props": {"label": "كلمة المرور الحالية", "id": "current-password-input", "validation": "Please enter your current password"}}, {"type": "password", "props": {"label": "كلمة المرور الجديدة", "id": "new-password-input", "validation": "Please enter a new password"}}], "submit_btn_message": "Save Changes", "success_message": "تم تحديث حسابك بنجاح", "success_redirect": "AccountOverviewScreen"}}]}]}], "3.2": [{"screens": [{"name": "شاشة تعديل الحساب", "components": [{"type": "textBlock", "props": {"text": "Edit your account information"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "الاسم", "id": "name-input", "validation": "Please enter a valid name"}}, {"type": "email", "props": {"label": "الب<PERSON>يد الإلكتروني", "id": "email-input", "validation": "Please enter a valid email"}}, {"type": "password", "props": {"label": "كلمة المرور الجديدة", "id": "new-password-input", "validation": "Please enter a new password"}}], "submit_btn_message": "Save Changes", "success_message": "تم تحديث حسابك بنجاح", "success_redirect": "AccountOverviewScreen"}}]}]}], "3.3": [{"screens": [{"name": "شاشة تغيير كلمة المرور", "components": [{"type": "form", "props": {"inputs": [{"label": "كلمة المرور الحالية", "type": "password", "validation": "required", "error_message": "كلمة المرور الحالية مطلوبة"}, {"label": "كلمة المرور الجديدة", "type": "password", "validation": "required|min:8|contains:letters,numbers", "error_message": "Password must be at least 8 characters long and contain letters and numbers"}, {"label": "تأكيد كلمة المرور الجديدة", "type": "password", "validation": "required|same:newPassword", "error_message": "يجب أن تتطابق كلمات المرور"}], "submit_btn_message": "Save Changes", "success_message": "تم تغيير كلمة المرور بنجاح", "success_redirect": "ProfileScreen"}}]}]}], "3.4": [{"screens": [{"name": "شاشة تغيير كلمة المرور", "components": [{"type": "form", "props": {"inputs": [{"label": "كلمة المرور القديمة", "type": "password", "validation": "كلمة المرور القديمة مطلوبة"}, {"label": "كلمة المرور الجديدة", "type": "password", "validation": "كلمة المرور الجديدة يجب أن تكون أكثر من 8 رموز وتشمل حروف وأرقام"}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم تغيير كلمة المرور بنجاح", "success_redirect": "ProfileScreen"}}]}]}], "3.5": [{"screens": [{"name": "شاشة تعديل الملف الشخصي", "components": [{"type": "form", "props": {"inputs": [{"label": "الاسم الكامل", "type": "text", "validation": "الاسم الكامل مطلوب"}, {"label": "الب<PERSON>يد الإلكتروني", "type": "email", "validation": "البريد الإلكتروني مطلوب"}, {"label": "كلمة المرور القديمة", "type": "password", "validation": "كلمة المرور القديمة مطلوبة"}, {"label": "كلمة المرور الجديدة", "type": "password", "validation": "كلمة المرور الجديدة يجب أن تكون أكثر من 8 رموز وتشمل حروف وأرقام"}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم تحديث البيانات بنجاح", "success_redirect": "ProfileScreen"}}]}]}]}, "4": {"4.1": [{"screens": [{"name": "شاشة تسجيل الدخول", "components": [{"type": "form", "props": {"inputs": [{"label": "رقم الهاتف", "type": "text", "validation": "رقم الها<PERSON><PERSON> مطلوب"}, {"label": "كلمة المرور", "type": "password", "validation": "كلمة المرور مطلوبة"}], "submit_btn_message": "تسجيل الدخول", "success_message": "تم تسجيل الدخول بنجاح", "success_redirect": "HomeScreen"}}]}]}], "4.2": [{"screens": [{"name": "شاشة تسجيل الدخول", "components": [{"type": "form", "props": {"inputs": [{"label": "رقم الهاتف", "type": "text", "validation": "رقم الها<PERSON><PERSON> مطلوب"}, {"label": "<PERSON><PERSON><PERSON> التحقق", "type": "text", "validation": "OTP مطلوب"}], "submit_btn_message": "تسجيل الدخول", "success_message": "تم تسجيل الدخول بنجاح", "success_redirect": "HomeScreen"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال OTP", "onClick": "requestOtp"}}]}]}], "4.3": [{"screens": [{"name": "الصفحة الرئيسية", "components": [{"type": "textBlock", "props": {"text": "Welcome to the Home Screen"}}, {"type": "<PERSON><PERSON>", "props": {"label": "الانتقال إلى لوحة التحكم", "onClick": "navigateToDashboard"}}]}, {"name": "لوحة التحكم", "components": [{"type": "textBlock", "props": {"text": "لوحة التحكم"}}, {"type": "text", "props": {"text": "You are logged in"}}]}]}], "4.4": [{"screens": [{"name": "نسيت كلمة المرور", "components": [{"type": "textBlock", "props": {"text": "نسيت كلمة المرور"}}, {"type": "form", "props": {"inputs": [{"label": "رقم الهاتف", "type": "text", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال رقم الهاتف"}}], "submit_btn_message": "إرسال رمز إعادة التعيين", "success_message": "تم إرسال رمز إعادة التعيين إلى رقم هاتفك", "success_redirect": "إعادة تعيين كلمة المرور"}}]}, {"name": "إعادة تعيين كلمة المرور", "components": [{"type": "textBlock", "props": {"text": "إعادة تعيين كلمة المرور"}}, {"type": "form", "props": {"inputs": [{"label": "رمز إعادة التعيين", "type": "text", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال رمز إعادة التعيين"}}, {"label": "كلمة المرور الجديدة", "type": "password", "validation": {"required": true, "min_length": 8, "pattern": "^(?=.*[a-zA-Z])(?=.*\\d).+$", "error_message": "يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل وتحتوي على حروف وأرقام"}}], "submit_btn_message": "إعادة تعيين كلمة المرور", "success_message": "تمت إعادة تعيين كلمة المرور بنجاح", "success_redirect": "<PERSON><PERSON>"}}]}]}], "4.5": [{"screens": [{"name": "إعادة إرسال رمز التحقق", "components": [{"type": "textBlock", "props": {"text": "إعادة إرسال OTP"}}, {"type": "form", "props": {"inputs": [{"label": "رقم الهاتف", "type": "text", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال رقم الهاتف"}}], "submit_btn_message": "إعادة إرسال OTP", "success_message": "تم إرسال OTP جديدة إلى رقم هاتفك", "success_redirect": "إدخال رمز التحقق"}}]}, {"name": "إدخال رمز التحقق", "components": [{"type": "textBlock", "props": {"text": "إدخال OTP"}}, {"type": "form", "props": {"inputs": [{"label": "<PERSON><PERSON><PERSON> التحقق", "type": "text", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال OTP الجديدة"}}], "submit_btn_message": "تحقق", "success_message": "تم التحقق من OTP بنجاح", "success_redirect": "لوحة التحكم"}}]}]}]}, "5": {"5.1": [{"screens": [{"name": "لا يوجد اتصال بالإنترنت", "components": [{"type": "textBlock", "props": {"text": "لا يمكن إتمام العملية. يرجى الاتصال بالإنترنت"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إعادة المحاولة", "onClick": "retryConnection"}}]}]}], "5.2": [{"screens": [{"name": "شاشة إعادة تعيين كلمة المرور", "components": [{"type": "textBlock", "props": {"text": "Enter the OTP and temporary password sent to your phone"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التحقق", "id": "otp", "validation": "required", "error_message": "<PERSON><PERSON>ز التحقق مطلوب"}}, {"type": "password", "props": {"label": "كلمة مرور مؤقتة", "id": "temporary_password", "validation": "required", "error_message": "كلمة مرور مؤقتة مطلوبة"}}, {"type": "password", "props": {"label": "كلمة المرور الجديدة", "id": "new_password", "validation": "required|min:8", "error_message": "New Password is required and must be at least 8 characters long"}}, {"type": "password", "props": {"label": "تأكيد كلمة المرور الجديدة", "id": "confirm_new_password", "validation": "required|same:new_password", "error_message": "كلمات المرور غير متطابقة"}}], "submit_btn_message": "Reset Password", "sucess_message": "Password reset successfully", "sucess_redirect": "شاشة تسجيل الدخول"}}]}]}], "5.3": [{"screens": [{"name": "شاشة التحقق من رمز التحقق", "components": [{"type": "textBlock", "props": {"text": "Enter the OTP and temporary password sent to your phone"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التحقق", "id": "otp", "validation": "required", "error_message": "<PERSON><PERSON>ز التحقق مطلوب"}}, {"type": "password", "props": {"label": "كلمة مرور مؤقتة", "id": "temporary_password", "validation": "required", "error_message": "كلمة مرور مؤقتة مطلوبة"}}], "submit_btn_message": "Verify OTP", "sucess_message": "OTP verified successfully", "error_message": "رمز التحقق أو كلمة المرور المؤقتة غير صحيحة. يرجى المحاولة مرة أخرى"}}]}]}], "5.4": [{"screens": [{"name": "شاشة إرسال رمز التحقق", "components": [{"type": "textBlock", "props": {"text": "Enter your phone number to receive OTP"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "رقم الهاتف", "id": "phone_number", "validation": "required|phone", "error_message": "رقم الهاتف مطلوب ويجب أن يكون صالحاً"}}], "submit_btn_message": "Send OTP", "sucess_message": "تم إرسال رمز التحقق بنجاح", "error_message": "فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى"}}]}]}]}, "6": {"6.1": [{"screens": [{"name": "شاشة إضافة مركبة", "components": [{"type": "textBlock", "props": {"text": "Enter the vehicle details and upload the required documents"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "نوع المركبة", "id": "vehicle_type", "validation": "required", "error_message": "نوع المركبة مطلوب"}}, {"type": "text", "props": {"label": "لوحة الترخيص", "id": "license_plate", "validation": "required", "error_message": "لوحة الترخيص مطلوبة"}}, {"type": "text", "props": {"label": "السنة", "id": "year", "validation": "required", "error_message": "السنة مطلوبة"}}, {"type": "text", "props": {"label": "الطراز", "id": "model", "validation": "required", "error_message": "الطراز مطلوب"}}, {"type": "file", "props": {"label": "وثيقة الترخيص", "id": "license_document", "validation": "required", "error_message": "وثيقة الترخيص مطلوبة"}}, {"type": "file", "props": {"label": "وثيقة التأمين", "id": "insurance_document", "validation": "required", "error_message": "وثيقة التأمين مطلوبة"}}], "submit_btn_message": "Add Vehicle", "sucess_message": "Vehicle added successfully", "error_message": "فشل في إضافة المركبة. يرجى المحاولة مرة أخرى"}}]}]}], "6.2": [{"screens": [{"name": "إدارة المركبات", "components": [{"type": "form", "props": {"inputs": [{"label": "رقم اللوحة", "type": "text", "validation": {"required": true, "pattern": "^[A-Za-z0-9]+$"}, "error_message": "ير<PERSON>ى إدخال رقم لوحة صالح"}, {"label": "السنة", "type": "number", "validation": {"required": true, "min": 1900, "max": 2100}, "error_message": "ير<PERSON>ى إدخال سنة صالحة"}, {"label": "موديل السيارة", "type": "text", "validation": {"required": true}, "error_message": "ير<PERSON>ى إدخال موديل صالح"}, {"label": "وثائق السيارة", "type": "file", "validation": {"required": false, "accept": "image/*,application/pdf"}, "error_message": "يرجى تحميل وثيقة صالحة"}], "submit_btn_message": "حفظ التعديلات", "success_message": "تم تحديث معلومات السيارة بنجاح", "success_redirect": "VehicleDetails"}}]}]}], "6.3": [{"screens": [{"name": "إدارة المركبات", "components": [{"type": "List", "props": {"items": [{"title": "رخصة المركبة", "content-render-format": "link"}, {"title": "تأمين المركبة", "content-render-format": "link"}]}}, {"type": "form", "props": {"inputs": [{"label": "تحميل وثائق جديدة", "type": "file", "validation": {"required": true, "accept": "image/*,application/pdf"}, "error_message": "يرجى تحميل وثيقة صالحة"}], "submit_btn_message": "حف<PERSON> الوثائق", "success_message": "تم تحديث وثائق السيارة بنجاح", "success_redirect": "VehicleDetails"}}]}]}]}, "7": {"7.1": [{"screens": [{"name": "إدارة السائقين", "components": [{"type": "form", "props": {"fields": [{"label": "اسم السائق", "name": "الاسم", "type": "text", "validation": {"required": true, "minLength": 2}}, {"label": "رقم الهاتف", "name": "الهاتف", "type": "text", "validation": {"required": true, "pattern": "^\\d{10}$"}}, {"label": "العنوان", "name": "العنوان", "type": "text", "validation": {"required": true}}, {"label": "رقم الرخصة", "name": "رقم الترخيص", "type": "text", "validation": {"required": true}}, {"label": "صورة الرخصة", "name": "صورة الترخيص", "type": "file", "validation": {"required": true}}], "submit_btn_message": "إضافة سائق", "success_message": "تم إضافة السائق بنجاح", "success_redirect": "DriverList"}}, {"type": "List", "props": {"title": "قائمة السائقين", "items": []}}]}]}], "7.2": [{"screens": [{"name": "إدارة السائقين", "components": [{"type": "form", "props": {"fields": [{"label": "اسم السائق", "name": "الاسم", "type": "text", "validation": {"required": true, "minLength": 2}}, {"label": "رقم الهاتف", "name": "الهاتف", "type": "text", "validation": {"required": true, "pattern": "^\\d{10}$"}}, {"label": "العنوان", "name": "العنوان", "type": "text", "validation": {"required": true}}, {"label": "رقم الرخصة", "name": "رقم الترخيص", "type": "text", "validation": {"required": true}}, {"label": "صورة الرخصة", "name": "صورة الترخيص", "type": "file", "validation": {"required": true}}], "submit_btn_message": "تعديل معلومات السائق", "success_message": "تم تعديل معلومات السائق بنجاح", "success_redirect": "DriverList"}}, {"type": "List", "props": {"title": "قائمة السائقين", "items": []}}]}]}], "7.3": [{"screens": [{"name": "إدارة السائقين", "components": [{"type": "List", "props": {"title": "قائمة السائقين", "items": [{"name": "اسم السائق", "actions": [{"label": "فصل عن السيارة", "onClick": "unlinkDriver"}]}]}}]}]}]}, "8": {"8.1": [{"screens": [{"name": "شاشة إضافة محطة", "components": [{"type": "form", "props": {"inputs": [{"name": "الاسم", "type": "text", "label": "اسم الفرع", "validation": {"required": true, "maxLength": 100, "errorMessage": "الاسم مطلوب ويجب ألا يتجاوز 100 حرف"}}, {"name": "العنوان", "type": "text", "label": "العنوان", "validation": {"required": true, "maxLength": 200, "errorMessage": "العنوان مطلوب ويجب ألا يتجاوز 200 حرف"}}, {"name": "<PERSON><PERSON> العرض", "type": "number", "label": "<PERSON><PERSON> العرض", "validation": {"required": true, "errorMessage": "<PERSON>ط العرض مطلوب"}}, {"name": "<PERSON><PERSON> الطول", "type": "number", "label": "<PERSON><PERSON> الطول", "validation": {"required": true, "errorMessage": "<PERSON><PERSON> الطول مطلوب"}}], "submit_btn_message": "إضافة المحطة", "sucess_message": "تمت إضافة المحطة بنجاح", "sucess_redirect": "StationListScreen"}}]}]}], "8.2": [{"screens": [{"name": "شاشة تعديل المحطة", "components": [{"type": "form", "props": {"inputs": [{"name": "الاسم", "type": "text", "label": "اسم الفرع", "validation": {"required": true, "maxLength": 100, "errorMessage": "الاسم مطلوب ويجب ألا يتجاوز 100 حرف"}}, {"name": "العنوان", "type": "text", "label": "العنوان", "validation": {"required": true, "maxLength": 200, "errorMessage": "العنوان مطلوب ويجب ألا يتجاوز 200 حرف"}}, {"name": "<PERSON><PERSON> العرض", "type": "number", "label": "<PERSON><PERSON> العرض", "validation": {"required": true, "errorMessage": "<PERSON>ط العرض مطلوب"}}, {"name": "<PERSON><PERSON> الطول", "type": "number", "label": "<PERSON><PERSON> الطول", "validation": {"required": true, "errorMessage": "<PERSON><PERSON> الطول مطلوب"}}], "submit_btn_message": "تعديل المحطة", "sucess_message": "تم تعديل معلومات المحطة بنجاح", "sucess_redirect": "StationListScreen"}}]}]}], "8.3": [{"screens": [{"name": "شاشة تعديل موقع المحطة", "components": [{"type": "form", "props": {"inputs": [{"name": "<PERSON><PERSON> العرض", "type": "number", "label": "<PERSON><PERSON> العرض", "validation": {"required": true, "errorMessage": "<PERSON>ط العرض مطلوب"}}, {"name": "<PERSON><PERSON> الطول", "type": "number", "label": "<PERSON><PERSON> الطول", "validation": {"required": true, "errorMessage": "<PERSON><PERSON> الطول مطلوب"}}], "submit_btn_message": "تعديل الموقع", "sucess_message": "تم تعديل موقع المحطة بنجاح", "sucess_redirect": "StationListScreen"}}]}]}]}, "9": {"9.1": [{"screens": [{"name": "شاشة طلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please select your role in the transport process:"}}, {"type": "form", "props": {"inputs": [{"label": "نوع العميل", "type": "select", "options": ["Sender", "Receiver"], "validation": "Please select a customer type"}], "submit_btn_message": "Next", "success_message": "تم اختيار نوع العميل بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.2": [{"screens": [{"name": "شاشة فئة المركبة لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please select the category of the vehicle required for transport:"}}, {"type": "form", "props": {"inputs": [{"label": "فئة المركبة", "type": "select", "options": ["Light Transport", "Heavy Transport"], "validation": "Please select a vehicle category"}], "submit_btn_message": "Next", "success_message": "تم اختيار فئة المركبة بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.3": [{"screens": [{"name": "شاشة نوع المركبة لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please select the type of vehicle required for transport based on the selected category:"}}, {"type": "form", "props": {"inputs": [{"label": "نوع المركبة", "type": "select", "options": ["Type 1", "Type 2", "Type 3"], "validation": "Please select a vehicle type"}], "submit_btn_message": "Next", "success_message": "تم اختيار نوع المركبة بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.4": [{"screens": [{"name": "شاشة تفاصيل الحمولة لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please provide details of the cargo for transport:"}}, {"type": "form", "props": {"inputs": [{"label": "وصف الحمولة", "type": "text", "validation": "Please provide a description of the cargo"}, {"label": "وزن الحمولة", "type": "number", "validation": "Please provide the total weight of the cargo"}], "submit_btn_message": "Next", "success_message": "تم إدخال تفاصيل الحمولة بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.5": [{"screens": [{"name": "شاشة قيمة الحمولة لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please provide the value of the cargo for transport:"}}, {"type": "form", "props": {"inputs": [{"label": "قيمة الحمولة", "type": "number", "validation": "Please provide the value of the cargo"}], "submit_btn_message": "Next", "success_message": "تم إدخال قيمة الحمولة بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.6": [{"screens": [{"name": "شاشة مواقع التحميل والتفريغ لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please provide the loading and unloading locations for transport:"}}, {"type": "form", "props": {"inputs": [{"label": "موقع التحميل", "type": "text", "validation": "Please provide the loading location"}, {"label": "موقع التفريغ", "type": "text", "validation": "Please provide the unloading location"}], "submit_btn_message": "Next", "success_message": "تم إدخال المواقع بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.7": [{"screens": [{"name": "شاشة وقت التحميل لطلب النقل", "components": [{"type": "textBlock", "props": {"text": "Please provide the loading time for transport:"}}, {"type": "form", "props": {"inputs": [{"label": "وقت التحميل", "type": "datetime", "validation": "Please provide the loading time"}], "submit_btn_message": "Next", "success_message": "تم إدخال وقت التحميل بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "9.8": [{"screens": [{"name": "شاشة نطاق البحث", "components": [{"type": "textBlock", "props": {"text": "ح<PERSON><PERSON> نطاق البحث"}}, {"type": "List", "props": {"items": ["10 كم", "50 كم", "100 كم", "200 كم"]}}, {"type": "<PERSON><PERSON>", "props": {"label": "التالي", "onClick": "goToNextStep"}}]}, {"name": "شاشة نتائج البحث", "components": [{"type": "table", "props": {"columns": [{"title": "اسم المزود", "content-render-format": "text"}, {"title": "المسافة", "content-render-format": "text"}, {"title": "التقييم", "content-render-format": "text"}]}}]}]}], "9.9": [{"screens": [{"name": "شاشة آلية التسعير", "components": [{"type": "textBlock", "props": {"text": "اختر آلية التسعير"}}, {"type": "List", "props": {"items": ["مزايدة عامة", "مزايدة خاصة", "سعر ثابت"]}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الطلب", "onClick": "submitRequest"}}]}, {"name": "شاشة ملخص الطلب", "components": [{"type": "text", "props": {"text": "تم تقديم الطلب بنجاح"}}]}]}]}, "10": {"10.1": [{"screens": [{"name": "شاشة اختيار نوع التسعير", "components": [{"type": "textBlock", "props": {"text": "Select Pricing Type"}}, {"type": "List", "props": {"items": [{"type": "RadioButton", "props": {"label": "تسعير ثابت", "value": "fixed"}}, {"type": "RadioButton", "props": {"label": "تلقي العروض", "value": "quotes"}}]}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "submitPricingType"}}]}]}], "10.2": [{"screens": [{"name": "شاشة إظهار الأجرة الأساسية", "components": [{"type": "textBlock", "props": {"text": "Base Fare Calculation"}}, {"type": "text", "props": {"content": "Base fare: $amount"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmBaseFare"}}]}]}], "10.3": [{"screens": [{"name": "شاشة إظهار الأجرة الإجمالية", "components": [{"type": "textBlock", "props": {"text": "Total Fare Calculation"}}, {"type": "text", "props": {"content": "Total fare: $amount"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmTotalFare"}}]}]}], "10.4": [{"screens": [{"name": "شاشة تقديم الطلب", "components": [{"type": "textBlock", "props": {"text": "Submit Cargo Request"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تفاصيل الحمولة", "placeholder": "Enter cargo details", "id": "cargo-details-input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال", "onClick": "submitRequest"}}]}}]}]}], "10.5": [{"screens": [{"name": "شاشة عرض العروض", "components": [{"type": "textBlock", "props": {"text": "View All Bids"}}, {"type": "table", "props": {"columns": [{"title": "المزود", "content-render-format": "text"}, {"title": "م<PERSON><PERSON>غ العرض", "content-render-format": "currency"}, {"title": "التقييم", "content-render-format": "stars"}]}}]}]}], "10.6": [{"screens": [{"name": "شاشة ترتيب وتصفية العروض", "components": [{"type": "textBlock", "props": {"text": "Sort and Filter Offers"}}, {"type": "form", "props": {"inputs": [{"type": "select", "props": {"label": "ترتيب حسب", "options": [{"label": "السعر (من الأقل إلى الأعلى)", "value": "price_low_high"}, {"label": "السعر (من الأعلى إلى الأقل)", "value": "price_high_low"}, {"label": "التقييم (من الأعلى إلى الأقل)", "value": "rating_high_low"}], "id": "sort-by-select"}}, {"type": "CheckboxGroup", "props": {"label": "معايير التصفية", "options": [{"label": "نطاق السعر", "value": "price_range"}, {"label": "التقييم", "value": "rating"}, {"label": "وقت التسليم", "value": "delivery_time"}], "id": "filter-criteria-checkbox"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تطبيق", "onClick": "applySortFilter"}}]}}]}]}], "10.7": [{"screens": [{"name": "شاشة عرض العروض", "components": [{"type": "textBlock", "props": {"text": "Display Bids"}}, {"type": "table", "props": {"columns": [{"title": "المزود", "content-render-format": "text"}, {"title": "م<PERSON><PERSON>غ العرض", "content-render-format": "currency"}, {"title": "النوع", "content-render-format": "text"}]}}]}]}]}, "11": {"11.1": [{"screens": [{"name": "تحقق من الرصيد", "components": [{"type": "textBlock", "props": {"text": "Checking your balance.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "تحقق من الرصيد", "onClick": "checkBalance"}}, {"type": "textBlock", "props": {"id": "balanceResult", "text": ""}}]}]}], "11.2": [{"screens": [{"name": "إرسال رمز التأكيد", "components": [{"type": "textBlock", "props": {"text": "Sending confirmation code.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الرمز", "onClick": "sendConfirmationCode"}}, {"type": "textBlock", "props": {"id": "confirmationResult", "text": ""}}]}]}], "11.3": [{"screens": [{"name": "ت<PERSON><PERSON>ي<PERSON> الحجز", "components": [{"type": "textBlock", "props": {"text": "Please enter the confirmation code sent to your registered phone number"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation-code", "validation": {"required": true, "pattern": "^[0-9]{6}$", "error_message": "Please enter a valid 6-digit confirmation code"}}}], "submit_btn_message": "Confirm Booking", "success_message": "تم تأكيد حجزك بنجاح", "success_redirect": "BookingDetails"}}]}]}], "11.4": [{"screens": [{"name": "تأكيد إضافة الأموال", "components": [{"type": "textBlock", "props": {"text": "Your balance is insufficient. Please enter the confirmation code sent to your registered phone number to add funds"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation-code", "validation": {"required": true, "pattern": "^[0-9]{6}$", "error_message": "Please enter a valid 6-digit confirmation code"}}}], "submit_btn_message": "إضافة الأموال", "success_message": "تمت إضافة الأموال بنجاح", "success_redirect": "BookingDetails"}}]}]}], "11.5": [{"screens": [{"name": "إضافة الأموال", "components": [{"type": "textBlock", "props": {"text": "Please enter the confirmation code sent to your registered phone number to add funds to your wallet"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation-code", "validation": {"required": true, "pattern": "^[0-9]{6}$", "error_message": "Please enter a valid 6-digit confirmation code"}}}, {"type": "text", "props": {"label": "طريقة الدفع", "id": "payment-method", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال طريقة دفع صالحة"}}}, {"type": "text", "props": {"label": "تفاصيل الدفع", "id": "payment-details", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال تفاصيل دفع صالحة"}}}], "submit_btn_message": "إضافة الأموال", "success_message": "تمت إضافة الأموال بنجاح", "success_redirect": "BookingDetails"}}]}]}], "11.6": [{"screens": [{"name": "تأكيد تجميد المبلغ", "components": [{"type": "textBlock", "props": {"text": "The transaction amount has been successfully frozen until the transport service is completed"}}]}]}], "11.7": [{"screens": [{"name": "إلغاء النقل", "components": [{"type": "textBlock", "props": {"text": "Your transport request has been cancelled. The refund amount will be calculated based on how close the transporter was to your location and whether they had started the journey"}}]}]}]}, "12": {"12.1": [{"screens": [{"name": "شاشة إدخا<PERSON> عدد العناصر", "components": [{"type": "textBlock", "props": {"text": "Enter the number of items"}}, {"type": "text", "props": {"text": "Please enter the number of items in your cargo"}}, {"type": "form", "props": {"inputs": [{"type": "number", "props": {"label": "<PERSON><PERSON><PERSON> العناصر", "id": "item-count", "validation": {"required": true, "error_message": "ع<PERSON><PERSON> العناصر مطلوب"}}}], "submit_btn_message": "إرسال", "success_message": "تم إد<PERSON>ا<PERSON> عدد العناصر بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "12.2": [{"screens": [{"name": "شاشة إدخال تفاصيل العناصر", "components": [{"type": "textBlock", "props": {"text": "Enter the details of each item"}}, {"type": "text", "props": {"text": "Please enter the details of each item in your cargo"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "وصف العنصر", "id": "item-description", "validation": {"required": true, "error_message": "وصف العنصر مطلوب"}}}, {"type": "number", "props": {"label": "<PERSON><PERSON><PERSON> العناصر", "id": "item-count", "validation": {"required": true, "error_message": "ع<PERSON><PERSON> العناصر مطلوب"}}}, {"type": "number", "props": {"label": "وزن العنصر", "id": "item-weight", "validation": {"required": true, "error_message": "وزن العنصر مطلوب"}}}, {"type": "number", "props": {"label": "قيمة العنصر", "id": "item-value", "validation": {"required": true, "error_message": "قيمة العنصر مطلوبة"}}}], "submit_btn_message": "إرسال", "success_message": "تم إدخال تفاصيل العنصر بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "12.3": [{"screens": [{"name": "شاشة إدخال متطلبات النقل", "components": [{"type": "textBlock", "props": {"text": "Enter Transport Requirements"}}, {"type": "text", "props": {"text": "Please enter the storage and care requirements for each item in your cargo"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "شروط التخزين", "id": "storage-conditions", "validation": {"required": true, "error_message": "شروط التخزين مطلوبة"}}}, {"type": "text", "props": {"label": "متطلبات الرعاية", "id": "care-requirements", "validation": {"required": true, "error_message": "متطلبات الرعاية مطلوبة"}}}], "submit_btn_message": "إرسال", "success_message": "تم إدخال متطلبات النقل بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "12.4": [{"screens": [{"name": "شاشة إدخال مواقع التحميل والتفريغ", "components": [{"type": "textBlock", "props": {"text": "Enter Loading and Unloading Locations"}}, {"type": "text", "props": {"text": "Please enter the loading and unloading locations for each item in your cargo"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "موقع التحميل", "id": "loading-location", "validation": {"required": true, "error_message": "موقع التحميل مطلوب"}}}, {"type": "text", "props": {"label": "موقع التفريغ", "id": "unloading-location", "validation": {"required": true, "error_message": "موقع التفريغ مطلوب"}}}, {"type": "text", "props": {"label": "معلومات المستلم", "id": "recipient-info", "validation": {"required": true, "error_message": "معلومات المستلم مطلوبة"}}}], "submit_btn_message": "إرسال", "success_message": "تم إدخال مواقع التحميل والتفريغ بنجاح", "success_redirect": "NextStepScreen"}}]}]}], "12.5": [{"screens": [{"name": "شاشة إرسال رابط التأكيد", "components": [{"type": "textBlock", "props": {"text": "Send Confirmation Link"}}, {"type": "text", "props": {"text": "Please enter the recipient's phone number to send the confirmation link"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "رقم هاتف المستلم", "id": "recipient-phone", "validation": {"required": true, "error_message": "رقم هاتف المستلم مطلوب"}}}, {"type": "text", "props": {"label": "تفاصيل الحمولة", "id": "cargo-details", "validation": {"required": true, "error_message": "تفاصيل الحمولة مطلوبة"}}}], "submit_btn_message": "Send Link", "success_message": "تم إرسال رابط التأكيد بنجاح", "success_redirect": "NextStepScreen"}}]}]}]}, "13": {"13.1": [{"screens": [{"name": "شاشة تأكيد الحمولة", "components": [{"type": "form", "props": {"inputs": [{"label": "وصف الحمولة", "type": "text", "validation": {"required": true, "error_message": "وصف الحمولة مطلوب"}}, {"label": "وزن الحمولة", "type": "number", "validation": {"required": true, "error_message": "وزن الحمولة مطلوب"}}, {"label": "حجم الحمولة", "type": "number", "validation": {"required": true, "error_message": "حجم الحمولة مطلوب"}}], "submit_btn_message": "Confirm Cargo", "success_message": "تم تأكيد الحمولة بنجاح!", "success_redirect": "CargoDetailsScreen"}}]}]}], "13.2": [{"screens": [{"name": "شاشة توليد رمز QR", "components": [{"type": "Message", "props": {"text": "Cargo details have been confirmed. Your QR code is being generated"}}]}]}], "13.3": [{"screens": [{"name": "شاشة مسح رمز QR", "components": [{"type": "form", "props": {"inputs": [{"label": "رمز QR", "type": "text", "validation": {"required": true, "error_message": "<PERSON><PERSON><PERSON> QR مطلوب"}}], "submit_btn_message": "Scan QR", "success_message": "تم مسح رمز QR بنجاح!", "success_redirect": "CargoDetailsScreen"}}]}]}], "13.4": [{"screens": [{"name": "شاشة ربط الحمولة", "components": [{"type": "Message", "props": {"text": "Cargo has been successfully linked to the client and carrier"}}]}]}]}, "14": {"14.1": [{"screens": [{"name": "تفتيش الشحنة", "components": [{"type": "textBlock", "props": {"text": "تفاصيل الحمولة"}}, {"type": "List", "props": {"items": "shipmentItems"}}, {"type": "form", "props": {"inputs": [{"type": "Checkbox", "props": {"label": "استلام بدون ملاحظات", "id": "noRemarks"}}, {"type": "textarea", "props": {"label": "ملاحظات", "id": "remarks", "placeholder": "اكتب ملاحظاتك هنا"}}], "submit_btn_message": "تأكيد الاستلام", "success_message": "تم تأكيد الاستلام بنجاح", "success_redirect": "الصفحة الرئيسية"}}]}]}], "14.2": [{"screens": [{"name": "صور الشحنة", "components": [{"type": "textBlock", "props": {"text": "التقط صور الحمولة"}}, {"type": "ImageUpload", "props": {"id": "photoUpload", "label": "اختر الصور لرفعها"}}, {"type": "<PERSON><PERSON>", "props": {"label": "رفع الصور", "onClick": "uploadPhotos"}}]}]}], "14.3": [{"screens": [{"name": "وثائق الشحنة", "components": [{"type": "textBlock", "props": {"text": "إرفاق المستندات"}}, {"type": "file", "props": {"id": "documentUpload", "label": "اختر المستندات لرفعها"}}, {"type": "<PERSON><PERSON>", "props": {"label": "رفع المستندات", "onClick": "uploadDocuments"}}]}]}], "14.4": [{"screens": [{"name": "ملاحظات الشحنة", "components": [{"type": "textBlock", "props": {"text": "تسجيل ملاحظات الأصناف"}}, {"type": "textarea", "props": {"id": "notes", "label": "اكتب ملاحظاتك هنا"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تسجيل الملاحظات", "onClick": "submitNotes"}}]}]}]}, "15": {"15.1": [{"screens": [{"name": "تأكيد الاستلام", "components": [{"type": "textBlock", "props": {"text": "Please confirm the receipt of the shipment"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmReceipt"}}]}, {"name": "تتبع الشحنة", "components": [{"type": "Map", "props": {"apiKey": "نص", "location": {"latitude": "float", "longitude": "float"}}}, {"type": "textBlock", "props": {"text": "Estimated Arrival Time: {{estimated_arrival}}"}}]}]}], "15.2": [{"screens": [{"name": "تتبع الشحنة", "components": [{"type": "Map", "props": {"apiKey": "نص", "location": {"latitude": "float", "longitude": "float"}}}, {"type": "textBlock", "props": {"text": "Estimated Arrival Time: {{estimated_arrival}}"}}]}]}], "15.3": [{"screens": [{"name": "تتبع الشاحنة", "components": [{"type": "Map", "props": {"apiKey": "نص", "location": {"latitude": "float", "longitude": "float"}}}]}]}], "15.4": [{"screens": [{"name": "بيانات المستشعر", "components": [{"type": "table", "props": {"columns": [{"title": "معامل", "content-render-format": "text"}, {"title": "القيمة", "content-render-format": "text"}], "data": [{"Parameter": "Temperature", "Value": "{{temperature}}"}, {"Parameter": "Vibration", "Value": "{{vibration}}"}, {"Parameter": "<PERSON><PERSON><PERSON><PERSON>", "Value": "{{humidity}}"}]}}]}]}], "15.5": [{"screens": [{"name": "حالة المرور", "components": [{"type": "table", "props": {"columns": [{"title": "الموقع", "content-render-format": "text"}, {"title": "الحالة", "content-render-format": "text"}, {"title": "الشدة", "content-render-format": "text"}], "data": [{"Location": "{{location}}", "Condition": "{{condition}}", "Severity": "{{severity}}"}]}}]}]}]}, "16": {"16.1": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "Please scan the QR code to confirm delivery"}}, {"type": "QRScanner", "props": {"onScan": "handleQRScan"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "submitConfirmation"}}]}]}], "16.2": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "Please click the link in the SMS to confirm delivery"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "openConfirmationLink"}}]}]}], "16.3": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "Please review and confirm the items received"}}, {"type": "ItemList", "props": {"items": "deliveryItems", "onConfirm": "handleItemConfirmation"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "submitConfirmation"}}]}]}], "16.4": [{"screens": [{"name": "تأكيد تحويل التكلفة", "components": [{"type": "textBlock", "props": {"text": "The costs will be transferred to the carrier after one hour"}}]}]}]}, "17": {"17.1": [{"screens": [{"name": "تأكيد الدفع", "components": [{"type": "textBlock", "props": {"text": "تم تحويل أجرة النقل إلى محفظة الناقل بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "عودة", "onClick": "goBack"}}]}]}], "17.2": [{"screens": [{"name": "تأكيد رسوم الخدمة", "components": [{"type": "textBlock", "props": {"text": "تم تحويل رسوم الخدمة إلى حساب التطبيق بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "عودة", "onClick": "goBack"}}]}]}], "17.3": [{"screens": [{"name": "تأكيد ضريبة القيمة المضافة", "components": [{"type": "textBlock", "props": {"text": "تم تحويل ضريبة القيمة المضافة إلى حساب الضريبة بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "عودة", "onClick": "goBack"}}]}]}]}, "18": {"18.1": [{"screens": [{"name": "شاشة التقييم", "components": [{"type": "textBlock", "props": {"text": "Please rate your recent transportation experience"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تعليقات", "placeholder": "Write your comments here", "required": false}}, {"type": "radio", "props": {"label": "التقييم", "options": ["1", "2", "3", "4", "5"], "required": true}}], "submit_btn_message": "إرسال", "sucess_message": "Thank you for your feedback!", "sucess_redirect": "HomePage"}}]}]}], "18.2": [{"screens": [{"name": "مل<PERSON> مقدم الخدمة", "components": [{"type": "textBlock", "props": {"text": "Customer Reviews"}}, {"type": "List", "props": {"items": [{"type": "textBlock", "props": {"text": "Review 1"}}, {"type": "textBlock", "props": {"text": "Review 2"}}]}}]}]}], "18.3": [{"screens": [{"name": "شاشة التذكير", "components": [{"type": "textBlock", "props": {"text": "You haven't rated your recent transportation experience. Please take a moment to provide your feedback"}}, {"type": "<PERSON><PERSON>", "props": {"label": "قيم الآن", "onClick": "openEvaluationform"}}]}]}], "18.4": [{"screens": [{"name": "شاشة التقييم", "components": [{"type": "textBlock", "props": {"text": "Please rate your recent transportation experience"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تعليقات", "placeholder": "Write your comments here", "required": false}}, {"type": "radio", "props": {"label": "التقييم", "options": ["1", "2", "3", "4", "5"], "required": true}}], "submit_btn_message": "إرسال", "sucess_message": "Thank you for your feedback!", "sucess_redirect": "HomePage"}}]}]}], "18.5": [{"screens": [{"name": "شاشة التقييمات الإدارية", "components": [{"type": "table", "props": {"columns": [{"title": "معرف المستخدم", "content-render-format": "نص"}, {"title": "معرف المعاملة", "content-render-format": "نص"}, {"title": "التقييم", "content-render-format": "number"}, {"title": "تعليقات", "content-render-format": "نص"}]}}]}]}]}, "19": {"19.1": [{"screens": [{"name": "حالة الخدمة", "components": [{"type": "textBlock", "props": {"text": "تحديد حالة الخدمة"}}, {"type": "RadioGroup", "props": {"options": [{"label": "طارئة", "value": "emergency"}, {"label": "عادية", "value": "regular"}], "name": "حالة الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceStatus"}}]}]}], "19.2": [{"screens": [{"name": "نوع الخدمة", "components": [{"type": "textBlock", "props": {"text": "تحديد نوع الخدمة"}}, {"type": "select", "props": {"options": [{"label": "صيانة", "value": "maintenance"}, {"label": "إصلاح", "value": "repair"}], "name": "نوع الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceType"}}]}]}], "19.3": [{"screens": [{"name": "وصف الخدمة", "components": [{"type": "textBlock", "props": {"text": "إدخال وصف الخدمة"}}, {"type": "textarea", "props": {"name": "وصف الخدمة", "placeholder": "أدخل وصف الخدمة هنا.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceDescription"}}]}]}], "19.4": [{"screens": [{"name": "الموقع", "components": [{"type": "textBlock", "props": {"text": "تحديد الموقع الجغرافي"}}, {"type": "text", "props": {"text": "النظام يتصل بنظام GPS لتحديد موقعك"}}, {"type": "<PERSON><PERSON>", "props": {"label": "ح<PERSON><PERSON> الموقع", "onClick": "submitLocation"}}]}]}], "19.5": [{"screens": [{"name": "الموقع اليدوي", "components": [{"type": "textBlock", "props": {"text": "إدخال الموقع الجغرافي يدويًا"}}, {"type": "text", "props": {"text": "ير<PERSON>ى إدخال معلومات الموقع الجغرافي يدويًا"}}, {"type": "form", "props": {"inputs": [{"type": "number", "name": "<PERSON><PERSON> العرض", "label": "<PERSON><PERSON> العرض", "validation": {"required": true, "min": -90, "max": 90, "errorMessage": "<PERSON>ط العرض يجب أن يكون بين -90 و 90"}}, {"type": "number", "name": "<PERSON><PERSON> الطول", "label": "<PERSON><PERSON> الطول", "validation": {"required": true, "min": -180, "max": 180, "errorMessage": "<PERSON><PERSON> الطول يجب أن يكون بين -180 و 180"}}], "submit_btn_message": "ح<PERSON><PERSON> الموقع", "success_message": "تم حفظ الموقع الجغرافي بنجاح", "success_redirect": "LocationConfirmation"}}]}]}], "19.6": [{"screens": [{"name": "حالة خدمة العميل", "components": [{"type": "textBlock", "props": {"text": "تحديد حالة الخدمة"}}, {"type": "RadioGroup", "props": {"options": [{"label": "طارئة", "value": "emergency"}, {"label": "عادية", "value": "regular"}], "name": "حالة الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceStatus"}}]}]}], "19.7": [{"screens": [{"name": "نوع خدمة العميل", "components": [{"type": "textBlock", "props": {"text": "تحديد نوع الخدمة"}}, {"type": "select", "props": {"options": [{"label": "صيانة", "value": "maintenance"}, {"label": "إصلاح", "value": "repair"}], "name": "نوع الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceType"}}]}]}], "19.8": [{"screens": [{"name": "وصف خدمة العميل", "components": [{"type": "textBlock", "props": {"text": "إدخال وصف الخدمة"}}, {"type": "textarea", "props": {"name": "وصف الخدمة", "placeholder": "أدخل وصف الخدمة هنا.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "submitServiceDescription"}}]}]}], "19.9": [{"screens": [{"name": "موقع العميل", "components": [{"type": "textBlock", "props": {"text": "تحديد الموقع الجغرافي"}}, {"type": "text", "props": {"text": "النظام يتصل بنظام GPS لتحديد موقعك"}}, {"type": "<PERSON><PERSON>", "props": {"label": "ح<PERSON><PERSON> الموقع", "onClick": "submitLocation"}}]}]}], "19.10": [{"screens": [{"name": "تفاصيل الخدمة", "components": [{"type": "textBlock", "props": {"text": "تفاصيل الطلب"}}, {"type": "text", "props": {"text": "حالة الخدمة: {{service_status}}"}}, {"type": "text", "props": {"text": "نوع الخدمة: {{service_type}}"}}, {"type": "text", "props": {"text": "وصف الخدمة: {{service_description}}"}}, {"type": "Map", "props": {"latitude": "{{latitude}}", "longitude": "{{longitude}}"}}]}]}]}, "20": {"20.1": [{"screens": [{"name": "تصنيف طلب الخدمة", "components": [{"type": "form", "props": {"inputs": [{"name": "نوع الخدمة", "type": "text", "label": "نوع الخدمة", "required": true, "validation": "نص", "error_message": "ير<PERSON>ى إدخال نوع الخدمة"}, {"name": "بيانات المركبة", "type": "text", "label": "بيانات المركبة", "required": true, "validation": "object", "error_message": "ير<PERSON>ى إدخال بيانات المركبة"}, {"name": "بيانات الموقع", "type": "text", "label": "بيانات الموقع", "required": true, "validation": "object", "error_message": "ير<PERSON>ى إدخال بيانات الموقع"}], "submit_btn_message": "إرسال الطلب", "sucess_message": "تم تصنيف الطلب بنجاح", "sucess_redirect": "ServiceRequestsList"}}]}]}], "20.2": [{"screens": [{"name": "إرسال طلب الخدمة", "components": [{"type": "form", "props": {"inputs": [{"name": "معر<PERSON> الطلب", "type": "text", "label": "معر<PERSON> الطلب", "required": true, "validation": "نص", "error_message": "ير<PERSON>ى إدخال معرف الطلب"}, {"name": "معرف الورشة", "type": "text", "label": "معرف الورشة", "required": true, "validation": "نص", "error_message": "ير<PERSON>ى إدخال معرف الورشة"}], "submit_btn_message": "إرسال الطلب", "sucess_message": "تم إرسال الطلب بنجاح", "sucess_redirect": "ServiceRequestsList"}}]}]}], "20.3": [{"screens": [{"name": "الرد على طلب الخدمة", "components": [{"type": "form", "props": {"inputs": [{"name": "الرد", "type": "text", "label": "رد الورشة", "required": true, "validation": "نص", "error_message": "<PERSON><PERSON><PERSON><PERSON> إدخال الرد"}, {"name": "تقدير التكلفة", "type": "number", "label": "التكلفة التقديرية", "required": false, "validation": "number", "error_message": "ير<PERSON>ى إدخال التكلفة التقديرية"}, {"name": "معلومات إضافية", "type": "text", "label": "معلومات إضافية", "required": false, "validation": "نص", "error_message": "ير<PERSON>ى إدخال معلومات إضافية إن وجدت"}], "submit_btn_message": "إرسال الرد", "sucess_message": "تم إرسال الرد بنجاح", "sucess_redirect": "ServiceRequestsList"}}]}]}]}, "21": {"21.1": [{"screens": [{"name": "التقرير الأسبوعي", "components": [{"type": "textBlock", "props": {"text": "التقرير الأسبوعي للشحنات"}}, {"type": "table", "props": {"columns": [{"title": "نوع الشحنة", "content-render-format": "نص"}, {"title": "<PERSON><PERSON><PERSON> الشحنات", "content-render-format": "number"}, {"title": "التاريخ", "content-render-format": "date"}]}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال التقرير", "onClick": "submitReport"}}]}]}], "21.2": [{"screens": [{"name": "تقدير الإصلاح", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "التكلفة المقدرة", "id": "estimated-cost", "validation": {"required": true, "pattern": "^[0-9]*\\.?[0-9]+$", "errorMessage": "Please enter a valid cost"}}}], "submit_btn_message": "Submit Estimate", "success_message": "تم تقديم التكلفة المقدرة بنجاح", "success_redirect": "ServiceRequestDetails"}}]}]}], "21.3": [{"screens": [{"name": "رسوم الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تكلفة المواد", "id": "material-cost", "validation": {"required": true, "pattern": "^[0-9]*\\.?[0-9]+$", "errorMessage": "Please enter a valid cost"}}}, {"type": "text", "props": {"label": "تكلفة العمل", "id": "labor-cost", "validation": {"required": true, "pattern": "^[0-9]*\\.?[0-9]+$", "errorMessage": "Please enter a valid cost"}}}], "submit_btn_message": "Calculate Fee", "success_message": "تم حساب رسوم الخدمة بنجاح", "success_redirect": "ServiceFeeDetails"}}]}]}], "21.4": [{"screens": [{"name": "شاشة تأكيد الشحنة", "components": [{"type": "textBlock", "props": {"text": "تم تأكيد استلام الحمولة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حسناً", "onClick": "closeNotification"}}]}]}], "21.5": [{"screens": [{"name": "شاشة المبلغ الصافي", "components": [{"type": "textBlock", "props": {"text": "صافي المبلغ المستحق"}}, {"type": "text", "props": {"label": "المبلغ", "value": "{net_amount}"}}]}]}], "21.6": [{"screens": [{"name": "شاشة تأكيد العرض", "components": [{"type": "textBlock", "props": {"text": "تم إرسال عرضك بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حسناً", "onClick": "closeNotification"}}]}]}], "21.7": [{"screens": [{"name": "شاشة إشعار الرفض", "components": [{"type": "textBlock", "props": {"text": "تم رفض طلبك"}}, {"type": "textBlock", "props": {"text": "{rejection_reason}"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حسناً", "onClick": "closeNotification"}}]}]}]}, "22": {"22.1": [{"screens": [{"name": "شاشة العروض المستلمة", "components": [{"type": "textBlock", "props": {"text": "عروض الإصلاح المستلمة"}}, {"type": "List", "props": {"items": [{"repair_cost": "number", "distance_to_workshop": "number", "expected_repair_time": "نص"}]}}]}]}], "22.2": [{"screens": [{"name": "شاشة مقارنة العروض", "components": [{"type": "textBlock", "props": {"text": "مقارنة العروض"}}, {"type": "table", "props": {"columns": [{"title": "أجرة الإصلاح", "content-render-format": "number"}, {"title": "المسافة إلى الورشة", "content-render-format": "number"}, {"title": "الزمن المتوقع للإصلاح", "content-render-format": "نص"}]}}, {"type": "<PERSON><PERSON>", "props": {"label": "اختيار العرض الأنسب", "onClick": "selectBestOffer"}}]}]}], "22.3": [{"screens": [{"name": "شاشة قبول العرض", "components": [{"type": "<PERSON><PERSON>", "props": {"label": "قبول العرض", "onClick": "acceptOffer"}}]}]}], "22.4": [{"screens": [{"name": "عرض التكلفة الإجمالية", "components": [{"type": "textBlock", "props": {"text": "التكلفة الإجمالية للخدمة"}}, {"type": "List", "props": {"items": [{"id": "repair-cost", "content": "تكلفة الإصلاح"}, {"id": "service-fee", "content": "رسوم الخدمة"}, {"id": "tax-amount", "content": "قيمة الضريبة"}, {"id": "total-cost", "content": "التكلفة الإجمالية"}]}}]}]}]}, "23": {"23.1": [{"screens": [{"name": "شاشة قبول الصفقة", "components": [{"type": "textBlock", "props": {"text": "ملخص الصفقة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "قبول الصفقة", "onClick": "handleAcceptDeal"}}]}]}], "23.2": [{"screens": [{"name": "شاشة تفاصيل الصفقة", "components": [{"type": "textBlock", "props": {"text": "تفاصيل الرسوم والخدمات"}}, {"type": "<PERSON><PERSON>", "props": {"label": "عرض التكاليف النهائية", "onClick": "showFinalCosts"}}]}]}], "23.3": [{"screens": [{"name": "شاشة التحقق من الدفع", "components": [{"type": "textBlock", "props": {"text": "تحقق من رصيد المحفظة"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "مب<PERSON>غ الصفقة", "id": "transaction_amount", "validation": {"required": true, "minValue": 1}, "error_message": "ير<PERSON>ى إدخال مبلغ صحيح"}}], "submit_btn_message": "تحقق", "success_message": "تم التحقق من الرصيد بنجاح", "success_redirect": "شاشة رمز التأكيد"}}]}]}], "23.4": [{"screens": [{"name": "شاشة إضافة الأموال", "components": [{"type": "textBlock", "props": {"text": "تعبئة المحفظة"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "المبلغ المطلوب", "id": "amount", "validation": {"required": true, "minValue": 1}, "error_message": "ير<PERSON>ى إدخال مبلغ صحيح"}}, {"type": "select", "props": {"label": "طريقة الدفع", "id": "payment_method", "options": ["بطاقة ائتمان", "تحويل بنكي"], "validation": {"required": true}, "error_message": "يرجى اختيار طريقة دفع"}}], "submit_btn_message": "تأكيد", "success_message": "تمت تعبئة المحفظة بنجاح", "success_redirect": "شاشة رمز التأكيد"}}]}]}], "23.5": [{"screens": [{"name": "شاشة رمز التأكيد", "components": [{"type": "textBlock", "props": {"text": "أد<PERSON><PERSON> كود التأكيد لإتمام الحجز"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "<PERSON>و<PERSON> التأكيد", "id": "confirmation_code", "validation": {"required": true, "pattern": "^[0-9]{6}$"}, "error_message": "ير<PERSON>ى إدخال كود تأكيد صحيح مكون من 6 أرقام"}}], "submit_btn_message": "تأكيد", "success_message": "تم تأكيد الحجز بنجاح", "success_redirect": "Booking Confirmation Screen"}}]}]}]}, "24": {"24.1": [{"screens": [{"name": "تعديل تفاصيل النقل", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "معر<PERSON> النقل", "id": "haulage_id", "validation": {"required": true, "error_message": "معرف النقل مطلوب"}}, {"type": "textarea", "label": "تفاصيل جديدة", "id": "new_details", "validation": {"required": true, "error_message": "التفاصيل الجديدة مطلوبة"}}], "submit_btn_message": "Save Changes", "success_message": "تم تحديث التفاصيل بنجاح", "success_redirect": "HaulageDetails"}}]}]}], "24.2": [{"screens": [{"name": "تأكيد الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation_code", "validation": {"required": true, "error_message": "رمز التأ<PERSON>يد مطلوب"}}], "submit_btn_message": "تأكيد", "success_message": "تم تأكيد الخدمة بنجاح", "success_redirect": "تفاصيل الخدمة"}}]}]}], "24.3": [{"screens": [{"name": "تفعيل الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation_code", "validation": {"required": true, "error_message": "رمز التأ<PERSON>يد مطلوب"}}], "submit_btn_message": "Activate Service", "success_message": "تم تفعيل الخدمة بنجاح", "success_redirect": "تفاصيل الخدمة"}}]}]}], "24.4": [{"screens": [{"name": "دعم العملاء", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "تفاصيل المعاملة", "id": "transaction_details", "validation": {"required": true, "error_message": "تفاصيل المعاملة مطلوبة"}}, {"type": "text", "label": "<PERSON><PERSON><PERSON> الفشل", "id": "failed_code", "validation": {"required": true, "error_message": "<PERSON><PERSON>ز الفشل مطلوب"}}], "submit_btn_message": "إرسال", "success_message": "تم تقديم طلب الدعم بنجاح", "success_redirect": "SupportConfirmation"}}]}]}], "24.5": [{"screens": [{"name": "طل<PERSON> ر<PERSON>ز جديد", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "تفاصيل الطلب", "id": "request_details", "validation": {"required": true, "error_message": "تفاصيل الطلب مطلوبة"}}], "submit_btn_message": "إرسال", "success_message": "تم توليد رمز جديد بنجاح", "success_redirect": "ConfirmationCode"}}]}]}]}, "25": {"25.1": [{"screens": [{"name": "شاشة ملخص الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "معرف الخدمة", "id": "service_id", "required": true}}, {"type": "textarea", "props": {"label": "الملخص", "id": "summary", "required": true}}, {"type": "text", "props": {"label": "التكلفة", "id": "cost", "required": true}}], "submit_btn_message": "Submit Summary", "sucess_message": "تم تقديم ملخص الخدمة بنجاح", "sucess_redirect": "HomeScreen"}}]}]}], "25.2": [{"screens": [{"name": "شاشة رسوم الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "معرف الخدمة", "id": "service_id", "required": true}}, {"type": "text", "props": {"label": "مب<PERSON><PERSON> الرسوم", "id": "charge_amount", "required": true}}], "submit_btn_message": "Submit Charge", "sucess_message": "تم تقديم رسوم الخدمة بنجاح", "sucess_redirect": "HomeScreen"}}]}]}], "25.3": [{"screens": [{"name": "شاشة تأكيد الخدمة", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "معرف الخدمة", "id": "service_id", "required": true}}, {"type": "text", "props": {"label": "<PERSON><PERSON><PERSON> التأكيد", "id": "confirmation_code", "required": true}}], "submit_btn_message": "Confirm Charge", "sucess_message": "تم تأكيد رسوم الخدمة بنجاح", "sucess_redirect": "HomeScreen"}}]}]}], "25.4": [{"screens": [{"name": "شاشة دعم العملاء", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "معرف الخدمة", "id": "service_id", "required": true}}, {"type": "textarea", "props": {"label": "تفاصيل النزاع", "id": "dispute_details", "required": true}}], "submit_btn_message": "Submit Dispute", "sucess_message": "تم تقديم طلب دعم العملاء بنجاح", "sucess_redirect": "HomeScreen"}}]}]}]}, "26": {"26.1": [{"screens": [{"name": "شاشة إغلاق الخدمة", "components": [{"type": "textBlock", "props": {"text": "يرجى التأكد من أن كافة المعلومات صحيحة قبل إغلاق الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إغلاق الخدمة", "onClick": "closeService"}}]}]}], "26.2": [{"screens": [{"name": "شاشة تحويل رسوم الإصلاح", "components": [{"type": "textBlock", "props": {"text": "يتم الآن تحويل أجرة الإصلاح إلى محفظة الورشة الفنية"}}]}]}], "26.3": [{"screens": [{"name": "شاشة تحويل رسوم الخدمة", "components": [{"type": "textBlock", "props": {"text": "يتم الآن تحويل رسوم الخدمة إلى حساب التطبيق"}}]}]}], "26.4": [{"screens": [{"name": "شاشة تحويل ضريبة القيمة المضافة", "components": [{"type": "textBlock", "props": {"text": "يتم الآن تحويل ضريبة القيمة المضافة إلى حساب الضريبة"}}]}]}]}, "27": {"27.1": [{"screens": [{"name": "إدارة السائقين", "components": [{"type": "form", "props": {"inputs": [{"label": "اسم السائق", "type": "text", "id": "driver-name", "required": true}, {"label": "رقم الهاتف", "type": "text", "id": "driver-phone", "required": true}, {"label": "العنوان", "type": "text", "id": "driver-address", "required": true}, {"label": "رقم الرخصة", "type": "text", "id": "driver-license-number", "required": true}, {"label": "صورة الرخصة", "type": "file", "id": "driver-license-image", "required": true}], "submit_btn_message": "إضافة سائق", "success_message": "تمت إضافة السائق بنجاح", "success_redirect": "DriverList"}}, {"type": "Select", "props": {"label": "اختر سيارة", "id": "vehicle-select", "options": [{"value": "vehicle1", "label": "سيارة 1"}, {"value": "vehicle2", "label": "سيارة 2"}], "required": true}}, {"type": "<PERSON><PERSON>", "props": {"label": "ربط السائق بالسيارة", "onClick": "assignDriverToVehicle"}}]}]}], "27.2": [{"screens": []}], "27.3": [{"screens": []}]}, "28": {"28.1": [{"screens": [{"name": "طلب قطع الغيار", "components": [{"type": "form", "props": {"inputs": [{"type": "dropdown", "props": {"label": "نوع القطعة", "id": "part-type", "options": ["جديدة", "مستعملة"], "required": true, "validation": {"type": "نص", "enum": ["جديدة", "مستعملة"], "errorMessage": "يرجى اختيار نوع القطعة (جديدة أو مستعملة)"}}}], "submit_btn_message": "تأكيد", "success_message": "تم تحديد نوع القطعة بنجاح", "success_redirect": "SparePartsDetails"}}]}]}], "28.2": [{"screens": [{"name": "شاشة تفاصيل المركبة", "components": [{"type": "textBlock", "props": {"text": "تفاصيل المركبة"}}, {"type": "table", "props": {"columns": [{"title": "النوع", "content-render-format": "text"}, {"title": "الطراز", "content-render-format": "text"}, {"title": "السنة", "content-render-format": "text"}]}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmVehicleDetails"}}]}]}], "28.3": [{"screens": [{"name": "شاشة إدخال تفاصيل القطع", "components": [{"type": "form", "props": {"inputs": [{"name": "وصف القطعة", "type": "text", "label": "وصف القطع", "validation": "ير<PERSON>ى إدخال وصف صحيح"}, {"name": "كمية القطعة", "type": "number", "label": "الكمية", "validation": "ير<PERSON>ى إدخال كمية صحيحة"}, {"name": "صوت القطعة", "type": "file", "label": "تسجيل صوتي", "validation": "يرجى تحميل تسجيل صوتي صحيح"}, {"name": "صورة القطعة", "type": "file", "label": "صورة", "validation": "يرجى تحميل صورة صحيحة"}], "submit_btn_message": "إدخال", "success_message": "تم إدخال بيانات القطع بنجاح", "success_redirect": "PartDetailsConfirmationScreen"}}]}]}], "28.4": [{"screens": [{"name": "شاشة موقع GPS", "components": [{"type": "textBlock", "props": {"text": "تحديد الموقع"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تفعيل GPS", "onClick": "enableGPS"}}, {"type": "Map", "props": {"latitude": "<PERSON><PERSON> العرض", "longitude": "<PERSON><PERSON> الطول", "label": "موقعك الحالي"}}]}]}], "28.5": [{"screens": [{"name": "شاشة إدخال تفاصيل القطع", "components": [{"type": "form", "props": {"inputs": [{"name": "وصف القطعة", "type": "text", "label": "وصف القطع", "validation": "ير<PERSON>ى إدخال وصف صحيح"}, {"name": "كمية القطعة", "type": "number", "label": "الكمية", "validation": "ير<PERSON>ى إدخال كمية صحيحة"}, {"name": "صوت القطعة", "type": "file", "label": "تسجيل صوتي", "validation": "يرجى تحميل تسجيل صوتي صحيح"}, {"name": "صورة القطعة", "type": "file", "label": "صورة", "validation": "يرجى تحميل صورة صحيحة"}], "submit_btn_message": "إدخال", "success_message": "تم إدخال بيانات القطع بنجاح", "success_redirect": "PartDetailsConfirmationScreen"}}]}]}]}, "29": {"29.1": [{"screens": [{"name": "طل<PERSON> خدمة النقل", "components": [{"type": "form", "props": {"inputs": [{"label": "نوع العميل", "type": "radio", "options": ["Sender", "Receiver"], "validation": "Please select a client type"}], "submit_btn_message": "Next", "sucess_message": "Client type selected successfully", "sucess_redirect": "NextStepScreen"}}]}]}], "29.2": [{"screens": [{"name": "طلب قطع الغيار", "components": [{"type": "form", "props": {"inputs": [{"label": "معرف القطعة", "type": "text", "validation": "Please enter a valid part ID"}, {"label": "الموقع", "type": "text", "validation": "Please enter your location"}], "submit_btn_message": "Search", "sucess_message": "Searching for nearest stores..", "sucess_redirect": "شاشة نتائج البحث"}}]}]}], "29.3": [{"screens": [{"name": "تقديم عرض قطع الغيار", "components": [{"type": "form", "props": {"inputs": [{"label": "تفاصيل العرض", "type": "text", "validation": "Please enter offer details"}, {"label": "السعر", "type": "number", "validation": "Please enter a valid price"}], "submit_btn_message": "Submit Offer", "sucess_message": "Offer submitted successfully", "sucess_redirect": "OfferSummaryScreen"}}]}]}], "29.4": [{"screens": [{"name": "عرض العروض", "components": [{"type": "list", "props": {"items": [{"title": "العرض", "content-render-format": "text"}]}}, {"type": "form", "props": {"inputs": [{"label": "نوع الطلب", "type": "dropdown", "options": ["More Details", "New Offers"], "validation": "Please select a request type"}], "submit_btn_message": "إرسال", "sucess_message": "تم تقديم الطلب بنجاح", "sucess_redirect": "OffersSummaryScreen"}}]}]}], "29.5": [{"screens": [{"name": "إعادة بحث قطع الغيار", "components": [{"type": "form", "props": {"inputs": [{"label": "معر<PERSON> الطلب", "type": "text", "validation": "Please enter a valid request ID"}], "submit_btn_message": "Re-search", "sucess_message": "Re-search initiated successfully", "sucess_redirect": "شاشة نتائج البحث"}}]}]}], "29.6": [{"screens": [{"name": "تقديم العروض الترويجية", "components": [{"type": "form", "props": {"inputs": [{"label": "تفاصيل العروض الترويجية", "type": "text", "validation": "Please enter promotion details"}], "submit_btn_message": "Submit Promotions", "sucess_message": "Promotions submitted successfully", "sucess_redirect": "PromotionsSummaryScreen"}}]}]}]}, "30": {"30.1": [{"screens": [{"name": "محادثة مؤقتة", "components": [{"type": "textBlock", "props": {"text": "بدء محادثة مؤقتة مع مقدم الخدمة لطلب بيانات إضافية"}}, {"type": "form", "props": {"inputs": [{"type": "textarea", "props": {"label": "رسالة", "id": "message", "placeholder": "أدخل رسالتك هنا..", "validation": "الرسالة مطلوبة"}}], "submit_btn_message": "إرسال", "success_message": "تم إرسال الرسالة بنجاح", "success_redirect": "RequestDetails"}}]}, {"name": "استجابة المحادثة المؤقتة", "components": [{"type": "textBlock", "props": {"text": "تم إرسال رسالتك بنجاح، وسيقوم مقدم الخدمة بالرد قريباً"}}]}]}], "30.2": [{"screens": [{"name": "تقديم بيانات إضافية", "components": [{"type": "textBlock", "props": {"text": "طلب مقدم الخدمة للحصول على بيانات إضافية"}}, {"type": "form", "props": {"inputs": [{"type": "textarea", "props": {"label": "البيانات المطلوبة", "id": "additional_data", "placeholder": "أد<PERSON>ل البيانات هنا..", "validation": "البيانات مطلوبة"}}], "submit_btn_message": "إرسال", "success_message": "تم إرسال البيانات بنجاح", "success_redirect": "RequestDetails"}}]}, {"name": "تم تقديم البيانات الإضافية بنجاح", "components": [{"type": "textBlock", "props": {"text": "تم إرسال بياناتك بنجاح، وسيقوم مقدم الخدمة بمراجعتها قريباً"}}]}]}], "30.3": [{"screens": [{"name": "محادثة مؤقتة", "components": [{"type": "textBlock", "props": {"text": "محادثة مؤقتة مع مقدم الخدمة. يرجى عدم مشاركة بيانات الاتصال الشخصية"}}, {"type": "form", "props": {"inputs": [{"type": "textarea", "props": {"label": "رسالتك", "id": "message", "placeholder": "أدخل رسالتك هنا..", "validation": "ير<PERSON>ى عدم مشاركة بيانات الاتصال الشخصية"}}], "submit_btn_message": "إرسال", "success_message": "تم إرسال الرسالة بنجاح", "success_redirect": "ConversationDetails"}}]}]}], "30.4": [{"screens": [{"name": "قرار المتابعة", "components": [{"type": "textBlock", "props": {"text": "لم يتم استلام رد من العميل في الوقت المحدد. يرجى اتخاذ قرار بناءً على المعلومات المتاحة"}}, {"type": "form", "props": {"inputs": [{"type": "textarea", "props": {"label": "القرار", "id": "decision", "placeholder": "أد<PERSON>ل القرار هنا..", "validation": "القرار مطلوب"}}], "submit_btn_message": "إرسال", "success_message": "تم اتخاذ القرار بنجاح", "success_redirect": "RequestDetails"}}]}]}]}, "31": {"31.1": [{"screens": [{"name": "شاشة إدخال القطع", "components": [{"type": "textBlock", "props": {"text": "Please enter the part specifications below:"}}, {"type": "form", "props": {"inputs": [{"label": "الحجم", "type": "text", "id": "size-input", "validation": {"required": true, "error_message": "الحجم مطلوب"}}, {"label": "الوزن", "type": "number", "id": "weight-input", "validation": {"required": true, "error_message": "الوزن مطلوب"}}, {"label": "الكمية", "type": "number", "id": "quantity-input", "validation": {"required": true, "error_message": "الكمية مطلوبة"}}], "submit_btn_message": "Save", "success_message": "تم حفظ مواصفات القطعة بنجاح", "success_redirect": "PartsOverviewScreen"}}]}]}], "31.2": [{"screens": [{"name": "إدخال تكلفة القطعة", "components": [{"type": "form", "props": {"inputs": [{"type": "number", "label": "تكلفة القطعة", "id": "part-cost", "validation": "required|numeric|min:0.01", "error_message": "برجاء إدخال تكلفة صحيحة"}], "submit_btn_message": "إدخال التكلفة", "success_message": "تم إدخال تكلفة القطعة بنجاح", "success_redirect": "تكلفة القطعة"}}]}]}], "31.3": [{"screens": [{"name": "عرض رسوم الخدمة", "components": [{"type": "table", "props": {"columns": [{"title": "مواصفات القطعة", "content-render-format": "text"}, {"title": "تكلفة القطعة", "content-render-format": "currency"}, {"title": "رسوم الخدمة", "content-render-format": "currency"}, {"title": "التكلفة الإجمالية", "content-render-format": "currency"}]}}]}]}], "31.4": [{"screens": [{"name": "عرض الضريبة المقررة", "components": [{"type": "table", "props": {"columns": [{"title": "رسوم الخدمة", "content-render-format": "currency"}, {"title": "الضريبة المقررة", "content-render-format": "currency"}, {"title": "التكلفة الإجمالية", "content-render-format": "currency"}]}}]}]}], "31.5": [{"screens": [{"name": "عرض صافي المبلغ المستحق", "components": [{"type": "table", "props": {"columns": [{"title": "تكلفة القطعة", "content-render-format": "currency"}, {"title": "رسوم الخدمة", "content-render-format": "currency"}, {"title": "الضريبة", "content-render-format": "currency"}, {"title": "صافي المبلغ المستحق", "content-render-format": "currency"}]}}]}]}], "31.6": [{"screens": [{"name": "إرسال العرض", "components": [{"type": "form", "props": {"inputs": [{"type": "number", "label": "صافي المبلغ المستحق", "id": "net-amount", "validation": "required|numeric|min:0.01", "error_message": "برجاء إدخال مبلغ صحيح"}], "submit_btn_message": "إرسال العرض", "success_message": "تم إرسال العرض بنجاح", "success_redirect": "حالة الطلب"}}]}]}], "31.7": [{"screens": [{"name": "إرسال رسالة نصية", "components": [{"type": "form", "props": {"inputs": [{"type": "text", "label": "رقم الهاتف", "id": "phone-number", "validation": "required|numeric", "error_message": "برجاء إدخال رقم هاتف صحيح"}, {"type": "textarea", "label": "الرسالة", "id": "message", "validation": "required|max:160", "error_message": "برجاء كتابة رسالة لا تتجاوز 160 حرفاً"}], "submit_btn_message": "إرسال الرسالة", "success_message": "تم إرسال الرسالة بنجاح", "success_redirect": "حالة الطلب"}}]}]}]}, "32": {"32.1": [{"screens": [{"name": "صفحة العروض المستلمة", "components": [{"type": "List", "props": {"items": [{"type": "OfferCard", "props": {"offer_id": "integer", "part_name": "نص", "total_cost": "currency", "net_amount": "currency", "details_link": "link"}}]}}]}]}], "32.2": [{"screens": [{"name": "صفحة تفاصيل العرض", "components": [{"type": "DetailCard", "props": {"part_name": "نص", "total_cost": "currency", "service_fee": "currency", "tax": "currency", "net_amount": "currency", "distance_to_shop": "نص", "estimated_response_time": "نص", "details": "نص"}}]}]}], "32.3": [{"screens": [{"name": "صفحة العروض المستلمة", "components": [{"type": "List", "props": {"items": [{"type": "OfferCard", "props": {"offer_id": "integer", "part_name": "نص", "total_cost": "currency", "net_amount": "currency", "details_link": "link", "accept_button": {"label": "موافقة", "onClick": "submitAcceptance"}}}]}}]}]}], "32.4": [{"screens": [{"name": "صفحة التكلفة الإجمالية", "components": [{"type": "DetailCard", "props": {"part_cost": "currency", "service_fee": "currency", "tax": "currency", "total_cost": "currency"}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافقة", "onClick": "submitFinalApproval"}}]}]}], "32.5": [{"screens": [{"name": "صفحة التكلفة الإجمالية", "components": [{"type": "DetailCard", "props": {"part_cost": "currency", "service_fee": "currency", "tax": "currency", "total_cost": "currency"}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافقة", "onClick": "submitFinalApproval"}}]}]}], "32.6": [{"screens": [{"name": "صفحة العروض المتاحة", "components": [{"type": "List", "props": {"items": [{"type": "OfferCard", "props": {"offer_id": "integer", "part_name": "نص", "total_cost": "currency", "net_amount": "currency", "details_link": "link", "select_button": {"label": "اختر هذا العرض", "onClick": "selectOffer"}}}]}}]}]}]}, "33": {"33.1": [{"screens": [{"name": "صفحة المحفظة", "components": [{"type": "text", "props": {"text": "رصيد المحفظة الحالي"}}, {"type": "Number", "props": {"id": "wallet-balance", "value": "decimal"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إعادة شحن المحفظة", "onClick": "rechargeWallet"}}]}]}], "33.2": [{"screens": [{"name": "صفحة إضافة الأموال", "components": [{"type": "form", "props": {"inputs": [{"type": "number", "label": "المبلغ", "id": "amount", "validation": "required|numeric|min:1", "error_message": "برجاء إدخال مبلغ صحيح"}, {"type": "select", "label": "طريقة الدفع", "id": "payment-method", "options": ["بطاقة ائتمان", "باي بال", "حوالة بنكية"], "validation": "required", "error_message": "برجاء اختيار طريقة دفع"}], "submit_btn_message": "إضافة الأموال", "success_message": "تم إضافة الأموال بنجاح", "success_redirect": "صفحة المحفظة"}}]}]}], "33.3": [{"screens": [{"name": "صفحة تأكيد الصفقة", "components": [{"type": "DetailCard", "props": {"transaction_details": "object"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد الصفقة", "onClick": "confirmTransaction"}}]}]}], "33.4": [{"screens": [{"name": "صفحة إلغاء الصفقة", "components": [{"type": "DetailCard", "props": {"transaction_details": "object"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إلغاء الصفقة", "onClick": "cancelTransaction"}}]}]}]}, "34": {"34.1": [{"screens": [{"name": "شاشة إنشاء الطلب", "components": [{"type": "textBlock", "props": {"text": "إنشاء طلب جديد لتوصيل قطع غيار"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تفاصيل القطع المطلوبة", "placeholder": "أدخل تفاصيل القطع", "validation": {"required": true, "message": "تفاصيل القطع مطلوبة"}}}, {"type": "text", "props": {"label": "موقع الاستلام", "placeholder": "أدخل موقع الاستلام", "validation": {"required": true, "message": "موقع الاستلام مطلوب"}}}, {"type": "text", "props": {"label": "موقع التسليم", "placeholder": "أدخل موقع التسليم", "validation": {"required": true, "message": "موقع التسليم مطلوب"}}}], "submit_btn_message": "نشر الطلب", "success_message": "تم نشر الطلب بنجاح", "success_redirect": "OrdersListScreen"}}]}]}], "34.2": [{"screens": [{"name": "شاشة إدارة الطلبات", "components": [{"type": "textBlock", "props": {"text": "إدارة الطلبات"}}, {"type": "List", "props": {"items": [{"title": "<PERSON><PERSON><PERSON> جديد", "content-render-format": "orderDetails"}]}}]}]}], "34.3": [{"screens": [{"name": "شاشة قائمة العروض", "components": [{"type": "textBlock", "props": {"text": "العروض المقدمة"}}, {"type": "List", "props": {"items": [{"title": "عرض من مركبة", "content-render-format": "offerDetails"}]}}]}]}], "34.4": [{"screens": [{"name": "تفاصيل العرض", "components": [{"type": "textBlock", "props": {"text": "تم إرسال العرض الأفضل لطالب الخدمة بناءً على معايير محددة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "استعراض العرض", "onClick": "viewOfferDetails"}}, {"type": "<PERSON><PERSON>", "props": {"label": "قبول العرض", "onClick": "acceptOffer"}}, {"type": "<PERSON><PERSON>", "props": {"label": "رف<PERSON> العرض", "onClick": "rejectOffer"}}]}]}], "34.5": [{"screens": [{"name": "قبول العرض", "components": [{"type": "textBlock", "props": {"text": "ير<PERSON>ى إدخا<PERSON> كود التحقق المرسل لإتمام عملية القبول"}}, {"type": "text", "props": {"label": "<PERSON>و<PERSON> التحقق", "id": "verification_code"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmVerificationCode"}}]}]}]}, "35": {"35.1": [{"screens": [{"name": "تحضير القطع", "components": [{"type": "textBlock", "props": {"text": "إعداد قطع الغيار للتسليم"}}, {"type": "List", "props": {"items": ["طلب التوصيل: {{order_id}}", "قائمة قطع الغيار: {{parts_list}}"]}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد الإعداد", "onClick": "confirmPreparation"}}]}]}], "35.2": [{"screens": [{"name": "استلام القطع", "components": [{"type": "textBlock", "props": {"text": "يرجى تقديم رمز QR أو رمز التسليم لاستلام القطع"}}, {"type": "text", "props": {"label": "ر<PERSON>ز QR أو رمز التسليم", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "<PERSON><PERSON><PERSON><PERSON>"}}]}]}], "35.3": [{"screens": [{"name": "التحقق من التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى تقديم رمز QR أو رمز التسليم للتحقق"}}, {"type": "text", "props": {"label": "ر<PERSON>ز QR أو رمز التسليم", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تحقق", "onClick": "verifyQRCode"}}]}]}]}, "36": {"36.1": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى مسح رمز QR لتأكيد استلام القطع"}}, {"type": "text", "props": {"label": "رمز QR", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmQRCode"}}]}]}], "36.2": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى مسح رمز QR لتأكيد استلام القطع"}}, {"type": "text", "props": {"label": "رمز QR", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmQRCode"}}]}]}], "36.3": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى مسح رمز QR لتأكيد استلام القطع"}}, {"type": "text", "props": {"label": "رمز QR", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmQRCode"}}]}]}], "36.4": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى مسح رمز QR لتأكيد استلام القطع"}}, {"type": "text", "props": {"label": "رمز QR", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmQRCode"}}]}]}], "36.5": [{"screens": [{"name": "تأكيد التسليم", "components": [{"type": "textBlock", "props": {"text": "يرجى مسح رمز QR لتأكيد استلام القطع"}}, {"type": "text", "props": {"label": "رمز QR", "id": "qr_code_input"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد", "onClick": "confirmQRCode"}}]}]}], "36.6": [{"screens": [{"name": "التأكيد اليدوي", "components": [{"type": "textBlock", "props": {"text": "لم يتمكن من مسح رمز QR؟ يرجى التواصل مع خدمة العملاء لتأكيد الاستلام يدوياً"}}, {"type": "<PERSON><PERSON>", "props": {"label": "اتصل بخدمة العملاء", "onClick": "contactCustomerService"}}]}]}]}, "37": {"37.1": [{"screens": [{"name": "شاشة قبول قطع الغيار", "components": [{"type": "textBlock", "props": {"text": "تأكيد استلام قطع الغيار"}}, {"type": "<PERSON><PERSON>", "props": {"label": "استلام", "onClick": "handleAcceptSpareParts"}}, {"type": "Notification", "props": {"message": "تم استلام القطع بنجاح"}}]}]}], "37.2": [{"screens": [{"name": "شاشة رفض قطع الغيار", "components": [{"type": "textBlock", "props": {"text": "رفض استلام قطع الغيار"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "handleRejectSpareParts"}}, {"type": "Notification", "props": {"message": "تم رفض القطع بنجاح"}}]}]}], "37.3": [{"screens": [{"name": "شاشة تحويل رسوم الخدمة", "components": [{"type": "textBlock", "props": {"text": "تحويل رسوم الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تحويل", "onClick": "handleServiceFeeTransfer"}}, {"type": "Notification", "props": {"message": "تم تحويل رسوم الخدمة بنجاح"}}]}]}]}, "38": {"38.1": [{"screens": [{"name": "شاشة تقديم المراجعة", "components": [{"type": "textBlock", "props": {"text": "تقييم الطلب"}}, {"type": "التقييم", "props": {"label": "تقييم الخدمة", "max_rating": 5}}, {"type": "textarea", "props": {"label": "تعليق (اختياري)", "placeholder": "اكتب تعليقك هنا.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال التقييم", "onClick": "handleSubmitReview"}}, {"type": "Notification", "props": {"message": "تم إرسال تقييمك بنجاح"}}]}]}], "38.2": [{"screens": [{"name": "شاشة تقديم مراجعة المتجر", "components": [{"type": "textBlock", "props": {"text": "تقييم الطلب"}}, {"type": "التقييم", "props": {"label": "تقييم العميل", "max_rating": 5}}, {"type": "textarea", "props": {"label": "تعليق (اختياري)", "placeholder": "اكتب تعليقك هنا.."}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال التقييم", "onClick": "handleSubmitStoreReview"}}, {"type": "Notification", "props": {"message": "تم إرسال تقييمك بنجاح"}}]}]}], "38.3": [{"screens": [{"name": "شاشة تاريخ الطلب", "components": [{"type": "textBlock", "props": {"text": "سجل الطلبات السابقة"}}, {"type": "List", "props": {"items": [{"order_id": "نص", "date": "نص", "details": "نص", "rating": "integer", "comment": "نص"}], "onItemClick": "handleOrderClick"}}, {"type": "OrderDetailsModal", "props": {"order_id": "نص", "date": "نص", "details": "نص", "rating": "integer", "comment": "نص"}}]}]}], "38.4": [{"screens": [{"name": "شاشة أرشفة بيانات الخدمة", "components": [{"type": "textBlock", "props": {"text": "أرشفة بيانات الخدمة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "أرشفة", "onClick": "handleArchiveServiceData"}}, {"type": "Notification", "props": {"message": "تمت أرشفة بيانات الخدمة بنجاح"}}]}]}]}, "39": {"39.1": [{"screens": [{"name": "شاشة طلب التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "طلب التخليص الجمركي"}}, {"type": "file", "props": {"label": "إرفاق الوثائق المطلوبة", "accepted_file_types": ["PDF", "JPG", "PNG"], "onUpload": "handleDocumentUpload"}}, {"type": "select", "props": {"label": "تحديد المن<PERSON>ذ الجمركي", "options": ["منفذ 1", "منفذ 2", "منفذ 3"], "onSelect": "handleCustomsPortSelect"}}, {"type": "RadioGroup", "props": {"label": "اختيار آلية إسناد الطلب", "options": ["تخصيص مخلص جمركي", "النشر العام للطلب على المنصة"], "onSelect": "handleAssignmentMethodSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الطلب", "onClick": "handleSubmitRequest"}}, {"type": "Notification", "props": {"message": "تم إرسال الطلب بنجاح"}}]}]}], "39.2": [{"screens": [{"name": "شاشة تحديد ميناء الجمارك", "components": [{"type": "textBlock", "props": {"text": "تحديد المن<PERSON>ذ الجمركي"}}, {"type": "file", "props": {"label": "إرفاق الوثائق المطلوبة", "accepted_file_types": ["PDF", "JPG", "PNG"], "onUpload": "handleDocumentUpload"}}, {"type": "select", "props": {"label": "تحديد المن<PERSON>ذ الجمركي", "options": ["منفذ 1", "منفذ 2", "منفذ 3"], "onSelect": "handleCustomsPortSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال", "onClick": "handleSubmitRequest"}}, {"type": "Notification", "props": {"message": "تم تحديد المن<PERSON>ذ الجمركي بنجاح"}}]}]}], "39.3": [{"screens": [{"name": "شاشة تعيين التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "طلب التخليص الجمركي"}}, {"type": "file", "props": {"label": "إرفاق الوثائق المطلوبة", "accepted_file_types": ["PDF", "JPG", "PNG"], "onUpload": "handleDocumentUpload"}}, {"type": "select", "props": {"label": "تحديد المن<PERSON>ذ الجمركي", "options": ["منفذ 1", "منفذ 2", "منفذ 3"], "onSelect": "handleCustomsPortSelect"}}, {"type": "RadioGroup", "props": {"label": "اختيار آلية إسناد الطلب", "options": ["تخصيص مخلص جمركي", "النشر العام للطلب على المنصة"], "onSelect": "handleAssignmentMethodSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال", "onClick": "handleSubmitRequest"}}, {"type": "Notification", "props": {"message": "تم تحديد آلية إسناد الطلب بنجاح"}}]}]}], "39.4": [{"screens": [{"name": "شاشة تقديم طلب التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "طلب التخليص الجمركي"}}, {"type": "file", "props": {"label": "إرفاق الوثائق المطلوبة", "accepted_file_types": ["PDF", "JPG", "PNG"], "onUpload": "handleDocumentUpload"}}, {"type": "select", "props": {"label": "تحديد المن<PERSON>ذ الجمركي", "options": ["منفذ 1", "منفذ 2", "منفذ 3"], "onSelect": "handleCustomsPortSelect"}}, {"type": "RadioGroup", "props": {"label": "اختيار آلية إسناد الطلب", "options": ["تخصيص مخلص جمركي", "النشر العام للطلب على المنصة"], "onSelect": "handleAssignmentMethodSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال", "onClick": "handleSubmitRequest"}}, {"type": "Notification", "props": {"message": "تم إرسال الطلب بنجاح"}}]}]}], "39.5": [{"screens": [{"name": "شاشة تعديل طلب التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "تعديل طلب التخليص الجمركي"}}, {"type": "List", "props": {"items": [{"order_id": "نص", "date": "نص", "status": "نص"}], "onItemClick": "handleOrderSelect"}}, {"type": "file", "props": {"label": "إرفاق الوثائق الجديدة", "accepted_file_types": ["PDF", "JPG", "PNG"], "onUpload": "handleDocumentUpload"}}, {"type": "select", "props": {"label": "تحديد المن<PERSON>ذ الجمركي الجديد", "options": ["منفذ 1", "منفذ 2", "منفذ 3"], "onSelect": "handleCustomsPortSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حفظ التعديلات", "onClick": "handleSaveChanges"}}, {"type": "Notification", "props": {"message": "تم حفظ التعديلات بنجاح"}}]}]}]}, "40": {"40.1": [{"screens": [{"name": "شاشة إرسال طلب التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "إرسال طلب التخليص الجمركي"}}, {"type": "List", "props": {"items": [{"request_id": "نص", "status": "نص", "details": "نص"}], "onItemClick": "handleRequestSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الطلب", "onClick": "handleSendRequest"}}, {"type": "Notification", "props": {"message": "تم إرسال الطلب بنجاح"}}]}]}], "40.2": [{"screens": [{"name": "شاشة مراجعة طلب التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "مراجعة طلب التخليص الجمركي"}}, {"type": "List", "props": {"items": [{"request_id": "نص", "status": "نص", "details": "نص"}], "onItemClick": "handleRequestSelect"}}, {"type": "DocumentViewer", "props": {"label": "عرض الوثائق المرفقة", "document_urls": ["url_to_document_1", "url_to_document_2"]}}, {"type": "FeeCalculator", "props": {"label": "تحديد الرسوم المستحقة", "onCalculate": "handleFeeCalculation"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد المراجعة", "onClick": "handleReviewConfirmation"}}, {"type": "Notification", "props": {"message": "تمت مراجعة الطلب وتحديد الرسوم بنجاح"}}]}]}], "40.3": [{"screens": [{"name": "شاشة إصدار فاتورة التخليص الجمركي", "components": [{"type": "textBlock", "props": {"text": "إصدار فاتورة طلب التخليص الجمركي"}}, {"type": "FeeDetails", "props": {"label": "تفاصيل الأجرة ورسوم الخدمة والضريبة", "details": {"service_fee": "number", "tax_fee": "number", "net_amount": "number"}}}, {"type": "<PERSON><PERSON>", "props": {"label": "إصدار الفاتورة", "onClick": "handleIssueInvoice"}}, {"type": "Notification", "props": {"message": "تم إصدار الفاتورة النهائية بنجاح"}}]}]}]}, "41": {"41.1": [{"screens": [{"name": "شاشة عرض عروض الخدمة", "components": [{"type": "textBlock", "props": {"text": "العروض المستلمة من مقدمي الخدمات"}}, {"type": "List", "props": {"items": [{"provider_id": "نص", "offer_details": "نص", "price": "number", "validity": "date"}]}}]}]}], "41.2": [{"screens": [{"name": "شاشة عرض رسوم الخدمة", "components": [{"type": "textBlock", "props": {"text": "رسوم الخدمة المستحقة"}}, {"type": "FeeDetails", "props": {"label": "تفاصيل الرسوم", "details": {"service_fees": "number"}}}]}]}], "41.3": [{"screens": [{"name": "شاشة عرض تفاصيل الضريبة", "components": [{"type": "textBlock", "props": {"text": "الضريبة المستحقة على الرسوم"}}, {"type": "TaxDetails", "props": {"label": "تفاصيل الضريبة", "details": {"tax_amount": "number"}}}]}]}], "41.4": [{"screens": [{"name": "شاشة عرض المبلغ الإجمالي", "components": [{"type": "textBlock", "props": {"text": "المبلغ الإجمالي المستحق"}}, {"type": "TotalAmountDetails", "props": {"label": "تفاصيل المبلغ الإجمالي", "details": {"service_fees": "number", "tax_amount": "number", "clearance_fees": "number", "total_amount": "number"}}}]}]}], "41.5": [{"screens": [{"name": "شاشة الموافقة على العرض", "components": [{"type": "textBlock", "props": {"text": "عرض الخدمة والمبلغ الإجمالي"}}, {"type": "تفاصيل العرض", "props": {"label": "تفاصيل العرض", "details": {"offer_id": "نص", "provider_name": "نص", "service_details": "نص", "price": "number"}}}, {"type": "TotalAmountDetails", "props": {"label": "المبلغ الإجمالي", "details": {"total_amount": "number"}}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافقة", "onClick": "handleApproveOffer"}}, {"type": "Notification", "props": {"message": "تمت الموافقة على العرض والمبلغ الإجمالي بنجاح"}}]}]}]}, "42": {"42.1": [{"screens": [{"name": "شاشة قبول الصفقة النهائية", "components": [{"type": "textBlock", "props": {"text": "الصفقة النهائية"}}, {"type": "DealDetails", "props": {"label": "تفاصيل الصفقة النهائية", "details": {"deal_id": "نص", "provider_name": "نص", "service_details": "نص", "total_amount": "number"}}}, {"type": "<PERSON><PERSON>", "props": {"label": "قبول", "onClick": "handleAcceptDeal"}}, {"type": "Notification", "props": {"message": "تم قبول الصفقة النهائية بنجاح"}}]}]}], "42.2": [{"screens": [{"name": "شاشة التحقق من رصيد المحفظة", "components": [{"type": "textBlock", "props": {"text": "التحقق من رصيد المحفظة"}}, {"type": "WalletBalance", "props": {"label": "رصيد المحفظة الحالي", "balance": "number", "required_amount": "number"}}, {"type": "Notification", "props": {"message": "تم التحقق من توفر المبلغ في المحفظة"}}]}]}], "42.3": [{"screens": [{"name": "شاشة تأكيد تجميد المبلغ", "components": [{"type": "textBlock", "props": {"text": "تأ<PERSON>يد حجز المبلغ"}}, {"type": "AmountDetails", "props": {"label": "تفاصيل المبلغ الإجمالي", "details": {"total_amount": "number"}}}, {"type": "Notification", "props": {"message": "تم حجز المبلغ وتجميده بنجاح ض<PERSON>ن حسابك"}}]}]}], "42.4": [{"screens": [{"name": "شاشة إضافة الأموال وتجميدها", "components": [{"type": "textBlock", "props": {"text": "إضافة المبلغ للمحفظة"}}, {"type": "PaymentMethod", "props": {"label": "اختر وسيلة الدفع", "options": ["بطاقة ائتمان", "بطاقة خصم", "محفظة إلكترونية"], "onSelect": "handlePaymentMethodSelect"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إضافة المبلغ", "onClick": "handleAddFunds"}}, {"type": "Notification", "props": {"message": "تم إضافة المبلغ للمحفظة وحجزه بنجاح"}}]}]}], "42.5": [{"screens": [{"name": "شاشة إلغاء الطلب", "components": [{"type": "textBlock", "props": {"text": "هل أنت متأكد من أنك تريد إلغاء الطلب؟"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إلغاء الطلب", "onClick": "submitCancellationRequest"}}, {"type": "<PERSON><PERSON>", "props": {"label": "رجوع", "onClick": "navigateBack"}}]}]}], "42.6": [{"screens": [{"name": "شاشة إلغاء الطلب الجمركي", "components": [{"type": "textBlock", "props": {"text": "هل أنت متأكد من أنك تريد إلغاء الطلب؟"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إلغاء الطلب", "onClick": "submitCustomsCancellationRequest"}}, {"type": "<PERSON><PERSON>", "props": {"label": "رجوع", "onClick": "navigateBack"}}]}]}]}, "43": {"43.1": [{"screens": [{"name": "شاشة توليد رمز QR", "components": [{"type": "textBlock", "props": {"text": "الرجاء الانتظار بينما يتم توليد رمز QR أو الكود النصي"}}, {"type": "LoadingSpinner", "props": {"size": "large"}}, {"type": "textBlock", "props": {"text": "تم توليد رمز QR أو الكود النصي بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}], "43.2": [{"screens": [{"name": "شاشة إرسال رمز QR", "components": [{"type": "textBlock", "props": {"text": "جارٍ إرسال رمز QR أو الكود النصي إلى طالب الخدمة"}}, {"type": "LoadingSpinner", "props": {"size": "large"}}, {"type": "textBlock", "props": {"text": "تم إرسال رمز QR أو الكود النصي بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}], "43.3": [{"screens": [{"name": "شاشة مشاركة رمز QR", "components": [{"type": "textBlock", "props": {"text": "حدد طريقة المشاركة:"}}, {"type": "select", "props": {"options": ["الب<PERSON>يد الإلكتروني", "الرسائل النصية", "وسائل التواصل"], "onChange": "selectShareMethod"}}, {"type": "<PERSON><PERSON>", "props": {"label": "مشاركة الرمز", "onClick": "submitShareRequest"}}, {"type": "textBlock", "props": {"text": "تم إرسال رمز QR أو الكود النصي بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}], "43.4": [{"screens": [{"name": "شاشة تفعيل الاتصال", "components": [{"type": "textBlock", "props": {"text": "الرجاء إدخال الرمز أو الكود لتفعيل الربط مع طالب الخدمة"}}, {"type": "Input", "props": {"placeholder": "أد<PERSON><PERSON> الرمز أو الكود هنا", "onChange": "handleInputChange"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تفعيل الربط", "onClick": "submitCode"}}, {"type": "textBlock", "props": {"text": "تم تفعيل الربط بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}], "43.5": [{"screens": [{"name": "شاشة إرسال رمز بديل", "components": [{"type": "textBlock", "props": {"text": "الرجاء اختيار طريقة بديلة لتبادل الرمز أو الكود"}}, {"type": "select", "props": {"options": ["الب<PERSON>يد الإلكتروني", "الرسائل النصية"], "onChange": "selectAlternativeMethod"}}, {"type": "Input", "props": {"placeholder": "أد<PERSON><PERSON> البريد الإلكتروني أو رقم الهاتف هنا", "onChange": "handleInputChange"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الرمز", "onClick": "submitAlternativeMethod"}}, {"type": "textBlock", "props": {"text": "تم إرسال رمز QR أو الكود النصي بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}]}, "44": {"44.1": [{"screens": [{"name": "تحميل الوثيقة", "components": [{"type": "form", "props": {"inputs": [{"type": "file", "label": "تحميل وثيقة التخليص", "id": "upload-clearance-doc", "validation": {"required": true, "error_message": "الوثيقة مطلوبة"}}], "submit_btn_message": "Upload Document", "success_message": "تم تحميل الوثيقة بنجاح", "success_redirect": "مراجعة الوثيقة"}}]}, {"name": "مراجعة الوثيقة", "components": [{"type": "textBlock", "props": {"text": "Please review the uploaded clearance document"}}, {"type": "<PERSON><PERSON>", "props": {"label": "إرسال الوثيقة", "onClick": "sendDocument"}}]}]}], "44.2": [{"screens": [{"name": "مراجعة الوثيقة", "components": [{"type": "textBlock", "props": {"text": "Review the customs clearance document below"}}, {"type": "DocumentViewer", "props": {"document_id": "document_id"}}, {"type": "<PERSON><PERSON>", "props": {"label": "الموافقة", "onClick": "approveDocument"}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "rejectDocument"}}, {"type": "form", "props": {"inputs": [{"type": "textarea", "label": "سبب الرفض", "id": "rejection-reason", "validation": {"required": false, "error_message": "يرجى تقديم سبب للرفض"}}], "submit_btn_message": "Submit <PERSON>", "success_message": "تم تقديم الملاحظات بنجاح", "success_redirect": "DocumentList"}}]}]}], "44.3": [{"screens": [{"name": "تقديم الوثيقة", "components": [{"type": "form", "props": {"inputs": [{"type": "file", "label": "تحميل وثيقة التخليص", "id": "upload-clearance-doc", "validation": {"required": true, "error_message": "الوثيقة مطلوبة"}}], "submit_btn_message": "Submit Document", "success_message": "تم تقديم الوثيقة بنجاح", "success_redirect": "شاشة المؤقت"}}]}, {"name": "شاشة المؤقت", "components": [{"type": "textBlock", "props": {"text": "Waiting for service requester response.."}}, {"type": "CountdownTimer", "props": {"end_time": "timestamp"}}]}]}]}, "45": {"45.1": [{"screens": [{"name": "تقديم رمز الموافقة", "components": [{"type": "textBlock", "props": {"text": "أدخل رمز الموافقة لتأكيد إكمال الخدمة:"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "رمز الموافقة", "id": "approval-code", "validation": "رمز الموافقة مطلوب"}}], "submit_btn_message": "تأكيد", "success_message": "تم تأكيد إكمال الخدمة وجمع المبلغ بنجاح", "success_redirect": "تأكيد الدفع"}}]}]}], "45.2": [{"screens": [{"name": "تأكيد إتمام الخدمة", "components": [{"type": "textBlock", "props": {"text": "تأكيد إكمال الخدمة:"}}, {"type": "form", "props": {"inputs": [{"type": "Checkbox", "props": {"label": "لقد أكملت الخدمة", "id": "service-completion", "validation": "يجب تأكيد إكمال الخدمة"}}], "submit_btn_message": "تأكيد", "success_message": "تم تأكيد إكمال الخدمة بنجاح", "success_redirect": "FeeTransferConfirmation"}}]}]}], "45.3": [{"screens": [{"name": "تأكيد توزيع الرسوم", "components": [{"type": "textBlock", "props": {"text": "تأكيد توزيع الرسوم وضريبة القيمة المضافة:"}}, {"type": "form", "props": {"inputs": [{"type": "Checkbox", "props": {"label": "تم جمع المبلغ الإجمالي من حساب طالب الخدمة", "id": "total-amount-collected", "validation": "يجب تأكيد جمع المبلغ الإجمالي"}}], "submit_btn_message": "تأكيد", "success_message": "تم توزيع الرسوم وضريبة القيمة المضافة بنجاح", "success_redirect": "DistributionConfirmation"}}]}]}]}, "46": {"46.1": [{"screens": [{"name": "شاشة اختيار المركبة", "components": [{"type": "textBlock", "props": {"text": "اختر نوع المركبات المطلوبة:"}}, {"type": "select", "props": {"label": "نوع المركبات", "id": "vehicle-dropdown", "options": ["نوع المركبة 1", "نوع المركبة 2", "نوع المركبة 3"]}}, {"type": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "onClick": "saveVehicleSelection"}}]}]}], "46.2": [{"screens": [{"name": "شاشة تحديد عدد النقل", "components": [{"type": "form", "props": {"inputs": [{"type": "NumberInput", "props": {"label": "عد<PERSON> النقلات المطلوبة", "id": "transport-count-input", "validation": {"required": true, "min": 1, "max": 100, "error_message": "ير<PERSON>ى إدخا<PERSON> عدد نقلات صحيح بين 1 و 100"}}}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم حفظ عدد النقلات بنجاح", "success_redirect": "ConfirmationScreen"}}]}]}], "46.3": [{"screens": [{"name": "شاشة تحديد وقت التنفيذ", "components": [{"type": "textBlock", "props": {"text": "حد<PERSON> مدة التنفيذ"}}, {"type": "form", "props": {"inputs": [{"type": "number", "props": {"label": "مدة التنفيذ بالأيام", "id": "execution-time", "validation": {"required": true, "min": 1, "max": 365, "error_message": "ير<PERSON>ى إدخال مدة صالحة بين 1 و365 يومًا"}}}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم حفظ مدة التنفيذ بنجاح", "success_redirect": "ConfirmationScreen"}}]}]}], "46.4": [{"screens": [{"name": "شاشة تحديد مواقع التحميل والتفريغ", "components": [{"type": "textBlock", "props": {"text": "حد<PERSON> مواقع التحميل والتنزيل"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "موقع التحميل", "id": "load-location", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال موقع التحميل"}}}, {"type": "text", "props": {"label": "موقع التنزيل", "id": "unload-location", "validation": {"required": true, "error_message": "ير<PERSON>ى إدخال موقع التنزيل"}}}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم حفظ مواقع التحميل والتنزيل بنجاح", "success_redirect": "ConfirmationScreen"}}]}]}], "46.5": [{"screens": [{"name": "شاشة تحميل العقد", "components": [{"type": "textBlock", "props": {"text": "إرفاق نموذج العقد"}}, {"type": "textBlock", "props": {"text": "يرجى إرفاق نموذج العقد المطلوب أو اختيار نموذج من القائمة"}}, {"type": "form", "props": {"inputs": [{"type": "FileInput", "props": {"label": "إرفاق نموذج العقد", "id": "contractFile", "validationError": "يرجى إرفاق ملف بصيغة PDF أو DOCX"}}], "submit_btn_message": "إرفاق", "success_message": "تم إرفاق نموذج العقد بنجاح", "success_redirect": "ContractDetailsScreen"}}]}]}], "46.6": [{"screens": [{"name": "شاشة مدة النشر", "components": [{"type": "textBlock", "props": {"text": "تحديد مدة النشر"}}, {"type": "textBlock", "props": {"text": "يرجى تحديد مدة النشر المطلوبة"}}, {"type": "form", "props": {"inputs": [{"type": "NumberInput", "props": {"label": "مدة النشر (بالأيام)", "id": "publicationDuration", "validationError": "ير<PERSON>ى إدخال مدة نشر صحيحة"}}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم حفظ مدة النشر بنجاح", "success_redirect": "PublicationDetailsScreen"}}]}]}], "46.7": [{"screens": [{"name": "شاشة تعيين المنافس", "components": [{"type": "textBlock", "props": {"text": "تحديد المتنافسين"}}, {"type": "textBlock", "props": {"text": "يرجى تحديد المتنافسين المطلوبين (إن وجد)"}}, {"type": "form", "props": {"inputs": [{"type": "MultiSelect", "props": {"label": "المتنافسين", "id": "competitors", "options": [{"value": "competitor1", "label": "المنافس 1"}, {"value": "competitor2", "label": "المنافس 2"}], "validationError": "يرجى اختيار المتنافسين بشكل صحيح"}}], "submit_btn_message": "<PERSON><PERSON><PERSON>", "success_message": "تم حفظ المتنافسين بنجاح", "success_redirect": "CompetitorDetailsScreen"}}]}]}], "46.8": [{"screens": [{"name": "اختيار فئة المنافس", "components": [{"type": "textBlock", "props": {"text": "اختر الفئات المتنافسة:"}}, {"type": "form", "props": {"inputs": [{"type": "CheckboxGroup", "props": {"label": "الفئات المتنافسة", "options": [{"label": "الفئة 1", "value": "category1"}, {"label": "الفئة 2", "value": "category2"}], "id": "competitor-categories"}}], "submit_btn_message": "حفظ الفئات المتنافسة", "success_message": "تم حفظ الفئات المتنافسة بنجاح", "success_redirect": "ConfirmationPage"}}]}]}]}, "47": {"47.1": [{"screens": [{"name": "نشر المسابقة", "components": [{"type": "textBlock", "props": {"text": "نشر منافسة نقل"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تفاصيل المنافسة", "id": "competitionDetails", "required": true, "validationErrorMessage": "الرجاء إدخال تفاصيل المنافسة"}}], "submit_btn_message": "نشر المنافسة", "success_message": "تم نشر المنافسة بنجاح", "success_redirect": "قائمة المسابقة"}}]}]}], "47.2": [{"screens": [{"name": "عرض خيارات المسابقة", "components": [{"type": "textBlock", "props": {"text": "خيارات النشر المتاحة"}}, {"type": "List", "props": {"items": [{"type": "textBlock", "props": {"text": "الخيار 1"}}, {"type": "textBlock", "props": {"text": "الخيار 2"}}]}}, {"type": "<PERSON><PERSON>", "props": {"label": "تأكيد النشر", "onClick": "confirmPublication"}}]}]}], "47.3": [{"screens": [{"name": "إرسال الإشعارات", "components": [{"type": "textBlock", "props": {"text": "إرسال الإشعارات"}}, {"type": "textBlock", "props": {"text": "جاري إرسال الإشعارات للشركات المتنافسة المستهدفة"}}]}]}], "47.4": [{"screens": [{"name": "شاشة الشركات المستهدفة", "components": [{"type": "textBlock", "props": {"text": "حدد الشركات المستهدفة لتلقي الطلبات:"}}, {"type": "SearchBox", "props": {"placeholder": "أدخل اسم الشركة أو معرفها", "onChange": "handleSearchChange"}}, {"type": "List", "props": {"items": [], "itemRenderer": "renderCompanyItem"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حفظ الشركات المحددة", "onClick": "submitTargetCompanies"}}, {"type": "textBlock", "props": {"text": "تم حفظ الشركات المحددة بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}], "47.5": [{"screens": [{"name": "شاشة تبديل ميزة الرسوم", "components": [{"type": "textBlock", "props": {"text": "حدد تفعيل أو تعطيل خاصية الرسوم:"}}, {"type": "Switch", "props": {"label": "تفعيل الرسوم", "onChange": "handleToggleChange"}}, {"type": "<PERSON><PERSON>", "props": {"label": "حفظ التحديثات", "onClick": "submitToggleFeature"}}, {"type": "textBlock", "props": {"text": "تم تحديث حالة الرسوم بنجاح", "visibility": "hidden", "id": "successMessage"}}]}]}]}, "48": {"48.1": [{"screens": [{"name": "قائمة المسابقة", "components": [{"type": "textBlock", "props": {"text": "المنافسات المتاحة"}}, {"type": "table", "props": {"columns": [{"title": "عنوان المنافسة", "content-render-format": "text"}, {"title": "الوصف", "content-render-format": "text"}, {"title": "الحالة", "content-render-format": "text"}]}}]}]}], "48.2": [{"screens": [{"name": "تفاصيل المسابقة", "components": [{"type": "textBlock", "props": {"text": "تفاصيل المنافسة"}}, {"type": "textBlock", "props": {"text": "تحميل كافة المرفقات والبيانات الخاصة بالمنافسة"}}, {"type": "<PERSON><PERSON>", "props": {"label": "تحميل المرفقات", "onClick": "downloadAttachments"}}]}]}], "48.3": [{"screens": [{"name": "تحضير العرض", "components": [{"type": "textBlock", "props": {"text": "إعداد العرض"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تفاصيل العرض", "id": "offerDetails", "required": true, "validationErrorMessage": "الرجاء إدخال تفاصيل العرض"}}], "submit_btn_message": "إرسال العرض", "success_message": "تم إرسال العرض بنجاح", "success_redirect": "قائمة المسابقة"}}]}]}], "48.4": [{"screens": [{"name": "إرسال بريد العرض", "components": [{"type": "textBlock", "props": {"text": "إرسال العرض عبر البريد الإلكتروني"}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "تفاصيل العرض", "id": "offerDetails", "required": true, "validationErrorMessage": "الرجاء إدخال تفاصيل العرض"}}, {"type": "text", "props": {"label": "البريد الإلكتروني للعميل", "id": "clientEmail", "required": true, "validationErrorMessage": "الرجاء إدخال البريد الإلكتروني للعميل"}}], "submit_btn_message": "إرسال العرض", "success_message": "تم إرسال العرض بنجاح", "success_redirect": "قائمة المسابقة"}}]}]}]}, "49": {"49.1": [{"screens": [{"name": "شاشة تأكيد الأرشفة", "components": [{"type": "textBlock", "props": {"text": "تم أرشفة بيانات المنافسة بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافق", "onClick": "redirectToDashboard"}}]}]}], "49.2": [{"screens": [{"name": "شاشة تأكيد التخزين", "components": [{"type": "textBlock", "props": {"text": "تم تخزين بيانات المنافسة بنجاح"}}, {"type": "<PERSON><PERSON>", "props": {"label": "موافق", "onClick": "redirectToDashboard"}}]}]}], "49.3": [{"screens": [{"name": "شاشة الأرشيف", "components": [{"type": "textBlock", "props": {"text": "قائمة المنافسات المؤرشفة"}}, {"type": "table", "props": {"columns": [{"title": "عنوان المنافسة", "content-render-format": "نص"}, {"title": "الوصف", "content-render-format": "نص"}, {"title": "تاريخ الأرشفة", "content-render-format": "نص"}]}}]}]}], "49.4": [{"screens": [{"name": "أرشفة بيانات المسابقة", "components": [{"type": "form", "props": {"inputs": [{"label": "معرف المسابقة", "type": "text", "name": "معرف المسابقة", "validation": "Competition ID is required"}, {"label": "تاريخ الأرشفة", "type": "date", "name": "تاريخ الأرشفة", "validation": "Archive date is required"}], "submit_btn_message": "Archive Data", "success_message": "تم أرشفة البيانات بنجاح", "success_redirect": "ArchiveSuccessScreen"}}]}]}]}, "50": {"50.1": [{"screens": [{"name": "العمليات المالية", "components": [{"type": "textBlock", "props": {"text": "إدارة العمليات المالية"}}, {"type": "table", "props": {"columns": [{"title": "رقم العملية", "content-render-format": "نص"}, {"title": "التاريخ", "content-render-format": "date"}, {"title": "المبلغ", "content-render-format": "currency"}, {"title": "الحالة", "content-render-format": "نص"}]}}, {"type": "form", "props": {"inputs": [{"type": "text", "props": {"label": "رقم العملية", "validation": "يج<PERSON> إدخال رقم العملية"}}, {"type": "textarea", "props": {"label": "ملاحظات", "validation": "يمكن ترك الملاحظات فارغة"}}], "submit_btn_message": "تأكيد العملية", "sucess_message": "تم تأكيد العملية بنجاح", "sucess_redirect": "FinancialOperationsSuccessScreen"}}]}]}]}, "51": {"51.1": [{"screens": [{"name": "لوحة التحكم الإدارية", "components": [{"type": "textBlock", "props": {"text": "لوحة البيانات الإدارية"}}, {"type": "table", "props": {"columns": [{"title": "العنوان", "content-render-format": "نص"}, {"title": "القيمة", "content-render-format": "number"}]}}, {"type": "form", "props": {"inputs": [{"type": "select", "props": {"label": "تنسيق التصدير", "options": ["CSV", "PDF"], "validation": "يج<PERSON> اختيار تنسيق التصدير"}}], "submit_btn_message": "تصدير البيانات", "sucess_message": "تم التصدير بنجاح", "sucess_redirect": "ExportSuccessScreen"}}]}]}]}}