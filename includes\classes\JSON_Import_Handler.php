<?php 


class JSON_Import_Handler {

    private $json_field_key;
    private $postType;

    public function __construct() {
        $this->json_field_key = 'json_file'; // ACF field key for the JSON file upload
        $this->postType = 'requirement';
        $this->acf_add_options_page();
        // Hook into ACF update_value action
        add_filter('acf/options_page/save', array($this, 'process_uploaded_json'), 10, 2);
        // add_action('acf/save_post', array($this, 'process_uploaded_json'), 10);
        // Allow JSON uploads
        add_filter('upload_mimes', array($this, 'allow_json_uploads'), 10, 1);
        // Add the filter globally for all fields
        add_filter('acf/load_value', array($this, 'acf_load_textarea_value_global'), 20, 3);
    }

    private function acf_add_options_page(){
        if( function_exists('acf_add_options_page') ) {
            acf_add_options_page(array(
                'page_title'    => 'Import JSON',
                'menu_title'    => 'Import JSON',
                'menu_slug'     => 'import-json',
                'capability'    => 'edit_posts',
                'redirect'      => false
            ));
        }
    }

    function acf_load_textarea_value_global($value, $post_id, $field) {
        // Check if the field type is textarea and the value is an array
        if ($field['type'] === 'textarea' && is_array($value)) {
            // Convert the array to a JSON string with unescaped Unicode characters
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        return $value;
    }
    
    

    public function allow_json_uploads($mimes) {
        error_log('allow_json_uploads hook fired'); // Debugging line
        $mimes['json'] = 'application/json';
        return $mimes;
    }

    /**
     * Summary of process_uploaded_json
     * @param mixed $post_id
     * @param mixed $menu_slug
     * @return void
     */
    public function process_uploaded_json($post_id, $menu_slug) {
        // Check if it's the options page
        if ( 'import-json' !== $menu_slug ) {
            return;     
        }
        // Get the uploaded file
        $file = get_field($this->json_field_key, 'option');
        $file = isset($_POST['acf']['field_665d8b00491c9']) ? $_POST['acf']['field_665d8b00491c9'] : '';

        if (! empty( $file ) ) {
            // Get the file content
            $file_path = get_attached_file($file);
            $json_content = file_get_contents($file_path);
            $json_data = json_decode($json_content, true);
            if ( $json_data ) {
                // Process JSON data
                foreach ($json_data as $id => $data) {
                    if( $id === 27 ) {
                        // Create or update a custom post type for each status
                        $post_id = $this->create_or_update_custom_post($id, $data);
                        // Update ACF fields
                        $this->updateAcfFields($id, $data, $post_id);
                    }
                }
            }
        }   
    }

    private function create_or_update_custom_post($id, $data) {
        $option_key = 'analyst_' . sanitize_key($id);

        // Check if the post ID is already stored in the options
        $post_id = get_option($option_key);

        if ($post_id) {
            // Update the existing post
            wp_update_post(array(
                'ID' => $post_id,
                'post_title' => $data['title'],
                'post_type' => $this->postType,
                'post_status' => 'publish'
            ));
        } else {
            // Create a new post of this type
            $post_id = wp_insert_post(array(
                'post_title' => $data['title'],
                'post_type' => $this->postType,
                'post_status' => 'publish'
            ));
            // Store the post ID in the options
            update_option($option_key, $post_id);
        }

        return $post_id;
    }

    private function updateAcfFields($id, $data, $post_id) {
        
        update_post_meta($post_id, 'analyst_json' , $data);

        update_field('id', $id, $post_id);
        
        // remove old data
        update_field('use_cases', [], $post_id);

        // Update repeater fields
        if (isset($data['business_goals'])) {
            $business_goals = array();
            foreach ($data['business_goals'] as $goal) {
                $business_goals[] = array('goal' => $goal);
            }
            update_field('business_goals', $business_goals, $post_id);
        }

        // Update stakeholders repeater field
        if (isset($data['stakeholders'])) {
            $stakeholders = array();
            foreach ($data['stakeholders'] as $stakeholder) {
                $stakeholders[] = array('stakeholder' => $stakeholder);
            }
            update_field('stakeholders', $stakeholders, $post_id);
        }

        // Update main_steps repeater field
        if (isset($data['main_steps'])) {
            $main_steps = array();
            foreach ($data['main_steps'] as $step) {
                $main_steps[] = array('step' => $step);
            }
            update_field('main_steps', $main_steps, $post_id);
        }

        // Update alternative_steps repeater field
        if (isset($data['alternative_steps'])) {
            $alternative_steps = array();
            foreach ($data['alternative_steps'] as $step) {
                $alternative_steps[] = array('step' => $step);
            }
            update_field('alternative_steps', $alternative_steps, $post_id);
        }

        // Update user_stories repeater field
        if (isset($data['user_stories'])) {
            $user_stories = array();
            foreach ($data['user_stories'] as $story) {
                $user_stories[] = array('story' => $story);
            }
            update_field('user_stories', $user_stories, $post_id);
        }

        // Update performance_indicators repeater field
        if (isset($data['performance_indicators'])) {
            $performance_indicators = array();
            foreach ($data['performance_indicators'] as $indicator) {
                $performance_indicators[] = array('indicators' => $indicator);
            }
            update_field('performance_indicators', $performance_indicators, $post_id);
        }

        // Update use_cases repeater field
        if (isset($data['use_cases'])) {
            $use_cases = array();
            foreach ($data['use_cases'] as $case_key => $case) {
                $use_case = array(
                    'is_new' => '',
                    'case_id' => $case_key,
                    'description_simple' => $case['description-simple'] ?? '',
                    'actor_simple' => $case['actor-simple'] ?? '',
                    'preconditions_simple' => array(),
                    'postconditions_simple' => array(),
                    'main_flow_steps_simple' => array(),
                    'description' => $case['description'] ?? '',
                    'actor' => $case['actor'] ?? '',
                    'preconditions' => array(),
                    'postconditions' => array(),
                    'main_flow_steps' => array(),
                    'alternative_flow_steps' => array(),
                    'exception_flow_steps' => array(),
                    'trigger' => $case['trigger'] ?? '',
                    'priority' => $case['priority'] ?? '',
                    'business_rules' => array(),
                    'assumptions' => array(),
                    'frequency_of_use' => $case['frequency_of_use'] ?? '',
                    'special_requirements' => array(),
                    'notes_and_issues' => array(),
                    'events_sequence' => array(),
                    'inputs' => array(),
                    'outputs' => array(),
                    'user_interactions' => array(),
                    'special_conditions' => array(),
                    'usage_scenarios' => array(),
                    'security_requirements' => array(),
                    'integration_with_other_systems' => array(),
                    'constraints_and_assumptions' => array(),
                    'testing_requirements' => array(),
                    'backend_details' => array(),
                    'frontend_details' => array(),
                    'notifications' => array(),
                    'flowchart_mermaid' => $case['flowchart_mermaid'] ?? ''
                );
                // update_sub_field( array("use_cases", 1, "case_key"), $case_key, $post_id);

                if (isset($case['preconditions-simple'])) {
                    foreach ($case['preconditions-simple'] as $condition) {
                        $use_case['preconditions_simple'][] = array('condition' => $condition);
                    }
                }

                if (isset($case['postconditions-simple'])) {
                    foreach ($case['postconditions-simple'] as $condition) {
                        $use_case['postconditions_simple'][] = array('condition' => $condition);
                    }

                }

                if (isset($case['main_flow_steps-simple'])) {
                    foreach ($case['main_flow_steps-simple'] as $step) {
                        $use_case['main_flow_steps_simple'][] = array('step' => $step);
                    }
                }

                if (isset($case['preconditions'])) {
                    foreach ($case['preconditions'] as $condition) {
                        $use_case['preconditions'][] = array('condition' => $condition);
                    }
                }

                if (isset($case['postconditions'])) {
                    foreach ($case['postconditions'] as $condition) {
                        $use_case['postconditions'][] = array('condition' => $condition);
                    }
                }

                if (isset($case['main_flow_steps'])) {
                    foreach ($case['main_flow_steps'] as $step) {
                        $use_case['main_flow_steps'][] = array('step' => $step);
                    }
                }

                if (isset($case['alternative_flow_steps'])) {
                    foreach ($case['alternative_flow_steps'] as $step) {
                        $step_array = array('condition' => $step['condition'], 'steps' => array());
                        foreach ($step['steps'] as $step_detail) {
                            $step_array['steps'][] = array('step' => $step_detail);
                        }
                        $use_case['alternative_flow_steps'][] = $step_array;
                    }
                }

                if (isset($case['exception_flow_steps'])) {
                    foreach ($case['exception_flow_steps'] as $step) {
                        $step_array = array('condition' => $step['condition'], 'steps' => array());
                        foreach ($step['steps'] as $step_detail) {
                            $step_array['steps'][] = array('step' => $step_detail);
                        }
                        $use_case['exception_flow_steps'][] = $step_array;
                    }
                }

                if (isset($case['business_rules'])) {
                    foreach ($case['business_rules'] as $rule) {
                        $use_case['business_rules'][] = array('rule' => $rule);
                    }
                    
                }

                if (isset($case['assumptions'])) {
                    foreach ($case['assumptions'] as $assumption) {
                        $use_case['assumptions'][] = array('assumption' => $assumption);
                    }
                }

                if (isset($case['special_requirements'])) {
                    foreach ($case['special_requirements'] as $requirement) {
                        $use_case['special_requirements'][] = array('requirement' => $requirement);
                    }
                }

                if (isset($case['notes_and_issues'])) {
                    foreach ($case['notes_and_issues'] as $note) {
                        $use_case['notes_and_issues'][] = array('note' => $note);
                    }
                }

                if (isset($case['events_sequence'])) {
                    foreach ($case['events_sequence'] as $event) {
                        $use_case['events_sequence'][] = array('event' => $event);
                    }
                }

                if (isset($case['inputs'])) {
                    foreach ($case['inputs'] as $input) {
                        $use_case['inputs'][] = array('input' => $input);
                    }
                }

                if (isset($case['outputs'])) {
                    foreach ($case['outputs'] as $output) {
                        $use_case['outputs'][] = array('output' => $output);
                    }
                }

                if (isset($case['user_interactions'])) {
                    foreach ($case['user_interactions'] as $interaction) {
                        $use_case['user_interactions'][] = array('interaction' => $interaction);
                    }
                }

                if (isset($case['special_conditions'])) {
                    foreach ($case['special_conditions'] as $condition) {
                        $use_case['special_conditions'][] = array('condition' => $condition);
                    }
                }

                if (isset($case['usage_scenarios'])) {
                    foreach ($case['usage_scenarios'] as $scenario) {
                        $use_case['usage_scenarios'][] = array('scenario' => $scenario);
                    }
                }

                if (isset($case['security_requirements'])) {
                    foreach ($case['security_requirements'] as $requirement) {
                        $use_case['security_requirements'][] = array('requirement' => $requirement);
                    }
                }

                if (isset($case['integration_with_other_systems'])) {
                    foreach ($case['integration_with_other_systems'] as $system) {
                        $use_case['integration_with_other_systems'][] = array('system' => $system);
                    }
                }

                if (isset($case['constraints_and_assumptions'])) {
                    foreach ($case['constraints_and_assumptions'] as $constraint) {
                        $use_case['constraints_and_assumptions'][] = array('constraint' => $constraint);
                    }
                }

                if (isset($case['testing_requirements'])) {
                    foreach ($case['testing_requirements'] as $requirement) {
                        $use_case['testing_requirements'][] = array('requirement' => $requirement);
                    }
                }

                // Fix backend_details
                if (isset($case['backend_details'])) {
                    $backend_details = array();
                    foreach ($case['backend_details'] as $detail) {
                        $backend_detail = array('api_endpoints' => array());
                        if (isset($detail)) {
                            foreach ($detail as $endpoint) {
                                $endpoint_array = array(
                                    'method' => $endpoint['method'] ?? '',
                                    'endpoint' => $endpoint['endpoint'] ?? '',
                                    'description' => $endpoint['description'] ?? '',
                                    'request' => array(),
                                    'response' => array()
                                );
            
                                if (isset($endpoint['request'])) {
                                    $request = $endpoint['request'];
                                    $endpoint_array['request'] = array();
                                    
                                    $endpoint_array['request'][] = [ 
                                        'body' => isset($request['body']) ? $this->array_to_multiline_string($request['body']) : '',
                                        'headers' => isset($request['headers']) ? $this->array_to_multiline_string($request['headers']): ''
                                    ];
                                                                
                                }
            
                                if (isset($endpoint['response'])) {
                                    foreach ($endpoint['response'] as $code => $response) {
                                        $endpoint_array['response'][] = array(
                                            'status_code' => $code ?? '',
                                            'description' => $response['description'] ?? '',
                                            'body' => isset($response['body']) ? $this->array_to_multiline_string($response['body']) : ''
                                        );
                                    }
                                }
                                $backend_detail['api_endpoints'][] = $endpoint_array;
                            }
                        }
                        $backend_details[] = $backend_detail;
                    }
                    $use_case['backend_details'] = $backend_details;
                }
                
                
                // Process frontend_details inside use_cases
                if (isset($case['frontend_details'])) {
                    $frontend_details = array();
                    foreach ($case['frontend_details'] as $detail) {
                        $frontend_detail = array('screens' => array());
                        
                        foreach ($detail as $screen) {
                            $screen_detail = array(
                                'name' => $screen['name'] ?? '',
                                'components' => array()
                            );
                            if (isset($screen['components']) && is_array($screen['components'])) {
                                foreach ($screen['components'] as $component) {
                                    $component_array = array(
                                        'acf_fc_layout' => strtolower($component['type']),
                                    );
                                    switch (strtolower($component['type'])) {
                                        case 'form':
                                            if (isset($component['props']['inputs']) && is_array($component['props']['inputs'])) {
                                                foreach ($component['props']['inputs'] as $input) {
                                                    $component_array['acf_fc_layout'] = 'form';
                                                    if( isset($input['props']) ) {
                                                        $component_array['inputs'][] = array(
                                                            'label' => $input['props']['label'] ?? '',
                                                            'type' => $input['type'] ?? '',
                                                            'options' => $input['props']['options'] ?? '',
                                                            'validation' => isset($input['props']['validation']) ? $this->array_to_multiline_string($input['props']['validation']) : '',
                                                            );
                                                    } else {
                                                        $component_array['inputs'][] = array(
                                                            'label' => $input['label'] ?? '',
                                                            'type' => $input['type'] ?? '',
                                                            'options' => $input['options'] ?? '',
                                                            'validation' => $input['validation'] ?? '',
                                                        ); 
                                                    }
                                                            // prr($component_array);
                                                }
                                                $component_array['submit_btn_message'] = $component['props']['submit_btn_message'] ?? ''; 
                                                $component_array['success_message'] = $component['props']['success_message'] ?? '';
                                                $component_array['success_redirect'] = $component['props']['success_redirect'] ?? '';
                                            }
                                            break;
                                        case 'textblock':
                                                $component_array['text'] = $component['props']['text'] ?? '';
                                                break;       
                                        case 'button':
                                                $component_array['acf_fc_layout'] = 'form';
                                                $component_array['inputs'][] = array(
                                                    'type' => $component['type'] ?? '',
                                                    'label' => $component['props']['label'] ?? '',
                                                    'options' => $component['props']['onClick'] ?? '',
                                                    'validation' => isset($component['props']['validation'])  ? $this->array_to_multiline_string($component['props']['validation']) : '',
                                                );
                                                break;
                                        case 'text':
                                        case 'checkbox':
                                        case 'email':
                                        case 'password':
                                        case 'file':
                                        case 'number':
                                        case 'message':
                                        case 'textarea':
                                        case 'select':
                                        case 'dropdown':
                                        case 'input':
                                        case 'checkboxgroup':
                                        case 'switch':
                                        case 'radio':
                                        case 'date':
                                                $component_array['acf_fc_layout'] = 'form';
                                                if( isset($component['props']) ) {
                                                    $component_array['inputs'][] = array(
                                                        'type' => $component['type'] ?? '',
                                                        'label' => $component['props']['label'] ?? '',
                                                        'options' => $component['props']['options'] ?? '',
                                                        'validation' => isset($component['props']['validation'])  ? $this->array_to_multiline_string($component['props']['validation']) : '',
                                                    );
                                                
                                                } else {
                                                    $component_array['inputs'][] = array(
                                                        'type' => $component['type'] ?? '',
                                                        'label' => $component['label'] ?? '',
                                                        'options' => $component['options'] ?? '',
                                                        'validation' => isset($component['validation'])  ? $this->array_to_multiline_string($component['validation']) : '',
                                                    );
                                                }
                                                $component_array['submit_btn_message'] = $component['props']['submit_btn_message'] ?? ''; 
                                                $component_array['success_message'] = $component['props']['success_message'] ?? '';
                                                $component_array['success_redirect'] = $component['props']['success_redirect'] ?? '';
                                            break;   
                                        case 'table':
                                                if (isset($component['props']['columns']) && is_array($component['props']['columns'])) {
                                                    foreach ($component['props']['columns'] as $column) {
                                                        $component_array['columns'][] = array(
                                                            'title' => $column['title'] ?? '',
                                                            'content-render-format' => $column['content-render-format'] ?? '',
                                                        );
                                                    }
                                                }
                                            break;
                                        case 'list':
                                                
                                                if (isset($component['props']['items']) && is_array($component['props']['items']) && !empty($component['props']['items'])) {
                                                    foreach ($component['props']['items'] as $item) {
                                                        $content = '';
                                                        if( isset($item['props']['text']) ){
                                                            $content = $item['props']['text'];
                                                        } else if( isset($item['content']) ){
                                                            $content = $item['content'];
                                                        } else if( isset($item['props']['id']) ){
                                                            $content = $item['props']['id'];
                                                        } else {
                                                            $content = $this->array_to_multiline_string($item);
                                                        }
                                                        $component_array['items'][] = array(
                                                            'content' => $content,
                                                        );
                                                    }
                                                } else if (isset($component['props']) ) {
                                                    // prr($component['props']);
                                                    $component_array['items'][] = array(
                                                        'content' => $this->array_to_multiline_string($component['props']),
                                                    );
                                                }
                                            break;
                                            // Handle other component types if needed
                                        
                                        }
                                    $screen_detail['components'][] = $component_array;
                                }
                            }
                            $frontend_detail['screens'][] = $screen_detail;
                        }
                        $frontend_details[] = $frontend_detail;
                    }

                    $use_case['frontend_details'] = $frontend_details;
                }
                
                if (isset($case['notifications'])) {
                    foreach ($case['notifications'] as $notification) {
                        $notification_array = array(
                            'methods' => $this->render_methods($notification['methods']) ?? '',
                            'recipient' => $notification['recipient'] ?? '',
                            'title' => $notification['title'] ?? '',
                            'message' => $notification['message'] ?? ''
                        );
                        
                        $use_case['notifications'][] = $notification_array;
                    }
                }
                // wp_die(prr($use_case));
                $use_cases[] = $use_case;
            }
            
            update_field('use_cases', $use_cases, $post_id);
            // prr($use_cases);
            // wp_die();
        }
    }

    // Assuming $cases is an array containing all use cases
    function array_to_multiline_string($array) {
        if( is_array($array) ) {
            return implode("\n", array_map(function($key, $value) {
                return is_array($value) ? ucfirst($key) . " : " . implode(", ", $value) : "$key: $value";
            }, array_keys($array), $array));
        } else {
            return $array;
        }
    }

    private function render_methods($data) {
        if( is_array($data) ) {
            return implode(', ' , $data);
        }
    }
}

// Initialize the class
new JSON_Import_Handler();