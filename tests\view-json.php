<?php
/**
 * View JSON file content
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if folder parameter is provided
if (!isset($_GET['folder']) || empty($_GET['folder'])) {
    die('No folder specified.');
}

$folder_name = sanitize_file_name($_GET['folder']);
$folder_path = ANALYST_PLUGIN_DIR . 'requirments/' . $folder_name;
$file_path = $folder_path . '/data.json';

// Check if folder exists
if (!is_dir($folder_path)) {
    die('Folder not found: ' . esc_html($folder_name));
}

// Check if data.json file exists
if (!file_exists($file_path)) {
    die('data.json file not found in folder: ' . esc_html($folder_name));
}

// Read and display the JSON content
$json_content = file_get_contents($file_path);

if ($json_content === false) {
    die('Failed to read file content.');
}

// Try to decode JSON to validate it
$json_data = json_decode($json_content, true);
$json_error = json_last_error();

?>
<!DOCTYPE html>
<html>
<head>
    <title>View JSON: <?php echo esc_html($folder_name); ?>/data.json</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .json-container {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 15px;
            overflow-x: auto;
        }
        .json-content {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #1976d2;
        }
        .btn-secondary {
            background: #757575;
        }
        .btn-secondary:hover {
            background: #424242;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Requirement Data Viewer</h1>
            <p><strong>Folder:</strong> <?php echo esc_html($folder_name); ?></p>
            <p><strong>File:</strong> data.json</p>
            <p><strong>Path:</strong> <code><?php echo esc_html($file_path); ?></code></p>
            <p><strong>Size:</strong> <?php echo number_format(filesize($file_path)); ?> bytes</p>
            <p><strong>Modified:</strong> <?php echo date('Y-m-d H:i:s', filemtime($file_path)); ?></p>
        </div>

        <?php if ($json_error !== JSON_ERROR_NONE): ?>
            <div class="error">
                <strong>JSON Error:</strong> <?php echo json_last_error_msg(); ?>
            </div>
        <?php else: ?>
            <div class="success">
                <strong>Valid JSON:</strong> File contains valid JSON data.
            </div>
            
            <?php if (isset($json_data['title']) || isset($json_data['id'])): ?>
                <div class="info">
                    <strong>Requirement Info:</strong><br>
                    <?php if (isset($json_data['id'])): ?>
                        Requirement ID: <?php echo esc_html($json_data['id']); ?><br>
                    <?php endif; ?>
                    <?php if (isset($json_data['title'])): ?>
                        Title: <?php echo esc_html($json_data['title']); ?><br>
                    <?php endif; ?>
                    <?php
                    // Count ACF fields
                    $acf_count = 0;
                    foreach ($json_data as $key => $value) {
                        if ($key !== 'title') {
                            $acf_count++;
                        }
                    }
                    ?>
                    Total ACF Fields: <?php echo $acf_count; ?><br>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <div class="json-container">
            <div class="json-content"><?php echo esc_html($json_content); ?></div>
        </div>

        <div class="actions">
            <a href="test-json-export.php" class="btn btn-secondary">← Back to Test Page</a>
            <a href="?folder=<?php echo urlencode($folder_name); ?>&download=1" class="btn">Download JSON</a>
        </div>
    </div>
</body>
</html>

<?php
// Handle download
if (isset($_GET['download']) && $_GET['download'] == '1') {
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $folder_name . '_data.json"');
    header('Content-Length: ' . filesize($file_path));
    readfile($file_path);
    exit;
}
?>
