<?php
/**
 * Debug repeater field issue
 * This file specifically diagnoses why repeater fields show empty values
 */

// Set up basic constants
define('ANALYST_PLUGIN_DIR', dirname(__DIR__) . '/');

echo '<h1>🔍 Debug Repeater Field Issue</h1>';

// Step 1: Read JSON directly
echo '<h2>Step 1: Direct JSON Analysis</h2>';

$requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
$folders = glob($requirements_dir . '*', GLOB_ONLYDIR);

if (empty($folders)) {
    echo '<p style="color: red;">❌ No requirement folders found!</p>';
    exit;
}

$folder = $folders[0];
$folder_name = basename($folder);
$data_json = $folder . '/data.json';

echo '<p>📁 Testing with: <strong>' . htmlspecialchars($folder_name) . '</strong></p>';

if (!file_exists($data_json)) {
    echo '<p style="color: red;">❌ data.json not found!</p>';
    exit;
}

$json_content = file_get_contents($data_json);
$data = json_decode($json_content, true);

if (!$data) {
    echo '<p style="color: red;">❌ Invalid JSON!</p>';
    exit;
}

echo '<p style="color: green;">✅ JSON loaded successfully</p>';

// Focus on business_goals as example
if (isset($data['business_goals'])) {
    echo '<h3>🎯 business_goals Analysis:</h3>';
    echo '<div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    
    $bg = $data['business_goals'];
    echo '<p><strong>Type:</strong> ' . gettype($bg) . '</p>';
    echo '<p><strong>Count:</strong> ' . (is_array($bg) ? count($bg) : 'Not array') . '</p>';
    
    if (is_array($bg) && !empty($bg)) {
        echo '<p><strong>Raw Structure:</strong></p>';
        echo '<pre style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 3px; overflow-x: auto;">';
        echo htmlspecialchars(json_encode($bg, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo '</pre>';
        
        echo '<p><strong>Item Analysis:</strong></p>';
        foreach ($bg as $index => $item) {
            echo '<div style="margin: 5px 0; padding: 8px; background: white; border-radius: 3px;">';
            echo '<strong>Item ' . ($index + 1) . ':</strong><br>';
            echo 'Type: ' . gettype($item) . '<br>';
            
            if (is_array($item)) {
                echo 'Keys: ' . implode(', ', array_keys($item)) . '<br>';
                foreach ($item as $key => $value) {
                    echo '&nbsp;&nbsp;' . $key . ': ' . htmlspecialchars(substr((string)$value, 0, 100)) . '<br>';
                }
            } else {
                echo 'Value: ' . htmlspecialchars((string)$item) . '<br>';
            }
            echo '</div>';
        }
    }
    echo '</div>';
} else {
    echo '<p style="color: orange;">⚠️ business_goals not found in JSON</p>';
}

// Step 2: Test with Requirements_JSON_Reader
echo '<h2>Step 2: Requirements_JSON_Reader Test</h2>';

// Mock WordPress functions
if (!function_exists('get_post_type')) {
    function get_post_type($post_id) { return 'requirement'; }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $meta_key, $single = false) {
        if ($meta_key === '_requirement_folder') {
            global $folders;
            if (!empty($folders)) {
                return basename($folders[0]);
            }
        }
        return false;
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $args = 1) {
        // Mock function for testing
        return true;
    }
}

try {
    require_once ANALYST_PLUGIN_DIR . 'includes/classes/requirements-json-reader.php';
    
    $reader = new Requirements_JSON_Reader();
    echo '<p style="color: green;">✅ Requirements_JSON_Reader loaded</p>';
    
    // Test get_requirement_data_by_post_id
    $test_post_id = 123;
    $requirement_data = $reader->get_requirement_data_by_post_id($test_post_id);
    
    if ($requirement_data) {
        echo '<p style="color: green;">✅ Requirement data retrieved</p>';
        
        if (isset($requirement_data['business_goals'])) {
            echo '<h3>🎯 business_goals via Reader:</h3>';
            echo '<div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;">';
            
            $bg_reader = $requirement_data['business_goals'];
            echo '<p><strong>Type:</strong> ' . gettype($bg_reader) . '</p>';
            echo '<p><strong>Count:</strong> ' . (is_array($bg_reader) ? count($bg_reader) : 'Not array') . '</p>';
            
            if (is_array($bg_reader) && !empty($bg_reader)) {
                echo '<p><strong>First Item:</strong></p>';
                echo '<pre style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">';
                echo htmlspecialchars(json_encode($bg_reader[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                echo '</pre>';
            }
            echo '</div>';
        }
    } else {
        echo '<p style="color: red;">❌ Could not retrieve requirement data</p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Error: ' . $e->getMessage() . '</p>';
}

// Step 3: Test template functions
echo '<h2>Step 3: Template Functions Test</h2>';

if (!function_exists('get_field')) {
    function get_field($field_name, $post_id) { return false; }
}

try {
    require_once ANALYST_PLUGIN_DIR . 'includes/template-functions.php';
    echo '<p style="color: green;">✅ Template functions loaded</p>';
    
    $test_post_id = 123;
    
    // Test get_requirement_field
    $bg_template = get_requirement_field('business_goals', $test_post_id);
    
    echo '<h3>🎯 business_goals via get_requirement_field:</h3>';
    echo '<div style="background: #fff3e0; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    echo '<p><strong>Type:</strong> ' . gettype($bg_template) . '</p>';
    echo '<p><strong>Count:</strong> ' . (is_array($bg_template) ? count($bg_template) : 'Not array') . '</p>';
    
    if (is_array($bg_template) && !empty($bg_template)) {
        echo '<p><strong>Values Extraction Test:</strong></p>';
        foreach ($bg_template as $index => $item) {
            echo '<div style="margin: 5px 0; padding: 8px; background: white; border-radius: 3px;">';
            echo '<strong>Item ' . ($index + 1) . ':</strong><br>';
            
            if (is_array($item)) {
                echo 'Array keys: ' . implode(', ', array_keys($item)) . '<br>';
                
                // Test different ways to extract value
                if (isset($item['goal'])) {
                    echo '✅ item["goal"]: ' . htmlspecialchars($item['goal']) . '<br>';
                } else {
                    echo '❌ item["goal"]: not found<br>';
                }
                
                // Try other possible keys
                $possible_keys = array('value', 'text', 'content', 'name');
                foreach ($possible_keys as $key) {
                    if (isset($item[$key])) {
                        echo '✅ item["' . $key . '"]: ' . htmlspecialchars($item[$key]) . '<br>';
                    }
                }
            } else {
                echo 'Direct value: ' . htmlspecialchars((string)$item) . '<br>';
            }
            echo '</div>';
        }
        
        // Test the_requirement_repeater function
        echo '<p><strong>the_requirement_repeater test:</strong></p>';
        echo '<div style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">';
        echo 'Output: ';
        the_requirement_repeater('business_goals', $test_post_id, 'goal', ' | ');
        echo '</div>';
    }
    echo '</div>';
    
} catch (Exception $e) {
    echo '<p style="color: red;">❌ Template functions error: ' . $e->getMessage() . '</p>';
}

// Step 4: Diagnosis and Solution
echo '<h2>Step 4: 🩺 Diagnosis & Solution</h2>';

echo '<div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">';

echo '<h3>🔍 What We Found:</h3>';
echo '<ol>';
echo '<li><strong>JSON Structure:</strong> Check if the data is properly nested</li>';
echo '<li><strong>Reader Access:</strong> Verify the reader can access the data</li>';
echo '<li><strong>Template Functions:</strong> Test if functions can extract values</li>';
echo '<li><strong>Sub-field Keys:</strong> Confirm the correct key names (goal, stakeholder, etc.)</li>';
echo '</ol>';

echo '<h3>💡 Common Issues & Solutions:</h3>';
echo '<ul>';
echo '<li><strong>Empty Values:</strong> Sub-field key mismatch (e.g., "goal" vs "goals")</li>';
echo '<li><strong>Wrong Type:</strong> Data saved as string instead of array</li>';
echo '<li><strong>ACF Hooks:</strong> Hooks not working properly in WordPress context</li>';
echo '<li><strong>Encoding Issues:</strong> UTF-8 problems with Arabic text</li>';
echo '</ul>';

echo '<h3>🔧 Next Steps:</h3>';
echo '<ol>';
echo '<li>Run this test to see the exact data structure</li>';
echo '<li>Compare with what ACF expects</li>';
echo '<li>Adjust the template functions accordingly</li>';
echo '<li>Test in WordPress environment</li>';
echo '</ol>';

echo '</div>';

echo '<h2>📋 Test Results Summary</h2>';
echo '<p>This test shows the exact structure of your repeater data and how it\'s being processed.</p>';
echo '<p><strong>Look for:</strong></p>';
echo '<ul>';
echo '<li>✅ Green checkmarks indicate working parts</li>';
echo '<li>❌ Red X marks indicate problems</li>';
echo '<li>⚠️ Orange warnings indicate potential issues</li>';
echo '</ul>';

?>
