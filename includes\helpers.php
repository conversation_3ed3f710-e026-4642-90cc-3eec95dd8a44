<?php
if ( ! function_exists('view') ) {
    function view($view, $data = array(), $base_dir='', $full_path = false) {

        $view = $full_path ? $view :  $base_dir . 'views/'. $view . '.php';

        if (!is_file($view)) {
            die( __( 'No File with this name', 'dgenny') . ' </b>' . $view .'</b></br>' );
        }

        if (!empty($data)) {
            extract($data, EXTR_SKIP);
        }

        require $view;
    }
}
if ( ! function_exists('echos') ) {
    function echos( $var1, $var2='', $var3='', $var4='', $var5='', $var6='', $var7='', $var8='', $var9='', $var10='', ) {
        $text = trim( $var1.' '.$var2.' '.$var3.' '.$var4.' '.$var5.' '.$var6.' '.$var7.' '.$var8.' '.$var9.' '.$var10);
        echo $text;
    }
}
//

function render_req( $key , $req ) {
    /////////////////////// start of template ///////////////////////
    echos('<h3>', $key , ':' , $req['title'] ,'</h3>');
    echos('<ul>');
    render_list( 'اهداف العمل', $req['business_goals'] );
    render_list( 'المستخدمين', $req['stakeholders'] );
    render_list( 'المستخدمين', $req['main_steps'] );
    render_list( 'المستخدمين', $req['alternative_steps'] );
    render_list( 'المستخدمين', $req['alternative_steps'] );
    render_list( 'المستخدمين', $req['user_stories'] );

    
    
    echos('</ul>');
}

function render_list( $key , $data ) {
    if( empty( $data ) ){
        return;
    }

    echos('<li><h4>',$key,'</h4>');
    echos('<ul>');
    foreach ( $data as $key => $value) {
        echos('<li><h4>',$value,'</h4></li>');
    }
    echos('</ul></li>');

}
function full_data(){
    $jsonFilePath = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
    $jsonData = file_get_contents($jsonFilePath);
    $data = json_decode($jsonData, true);
    return $data;
}
