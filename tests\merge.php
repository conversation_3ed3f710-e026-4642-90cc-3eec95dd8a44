<?php

function loadJsonFile($filePath) {
    if (!file_exists($filePath)) {
        wp_die("File not found: $filePath");
    }
    $jsonData = file_get_contents($filePath);
    return json_decode($jsonData, true);
}

function compareRequirements($req1, $req2) {
    // Remove use_cases and advanced_use_cases for comparison
    $temp1 = $req1;
    $temp2 = $req2;
    unset($temp1['use_cases'], $temp1['advanced_use_cases']);
    unset($temp2['use_cases'], $temp2['advanced_use_cases']);
    
    return $temp1 === $temp2;
}

function mergeRequirements($req1, $req2) {
    $merged = $req1;
    if (isset($req1['use_cases']) && isset($req2['use_cases'])) {
        $merged['use_cases'] = array_merge($req1['use_cases'], $req2['use_cases']);
    } elseif (isset($req2['use_cases'])) {
        $merged['use_cases'] = $req2['use_cases'];
    }
    
    if (isset($req1['advanced_use_cases']) && isset($req2['advanced_use_cases'])) {
        $merged['advanced_use_cases'] = array_merge($req1['advanced_use_cases'], $req2['advanced_use_cases']);
    } elseif (isset($req2['advanced_use_cases'])) {
        $merged['advanced_use_cases'] = $req2['advanced_use_cases'];
    }
    
    return $merged;
}

$filePath1 = ANALYST_PLUGIN_DIR . 'data/merge/brd_reqs_with-advanced.json';
$filePath2 = ANALYST_PLUGIN_DIR . 'data/merge/brd_reqs_ar.json';

$data1 = loadJsonFile($filePath1);
$data2 = loadJsonFile($filePath2);


$mergedData = [];
$nonMatchingRequirements = [];

foreach ($data1 as $key => $requirement1) {
    if (isset($data2[$key])) {
        $requirement2 = $data2[$key];
        
        if (compareRequirements($requirement1, $requirement2)) {
            $mergedData[$key] = mergeRequirements($requirement1, $requirement2);
        } else {
            $nonMatchingRequirements[] = $key;
        }
    }
}

file_put_contents(ANALYST_PLUGIN_DIR . 'data/merge/merged.json', json_encode($mergedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "Non-matching requirements:\n";
print_r($nonMatchingRequirements);

?>