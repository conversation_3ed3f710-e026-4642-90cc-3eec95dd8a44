<?php
/**
 * Direct test for repeater fields specifically
 * This file tests how repeater fields are handled from JSON
 */

// Set up basic constants
define('ANALYST_PLUGIN_DIR', dirname(__DIR__) . '/');

// Include the classes and functions
require_once ANALYST_PLUGIN_DIR . 'includes/classes/requirements-json-reader.php';

// Mock WordPress functions
if (!function_exists('get_post_type')) {
    function get_post_type($post_id) { return 'requirement'; }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $meta_key, $single = false) {
        if ($meta_key === '_requirement_folder') {
            $requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
            $folders = glob($requirements_dir . '*', GLOB_ONLYDIR);
            if (!empty($folders)) {
                return basename($folders[0]);
            }
        }
        return false;
    }
}

if (!function_exists('get_field')) {
    function get_field($field_name, $post_id) { return false; }
}

// Include template functions
require_once ANALYST_PLUGIN_DIR . 'includes/template-functions.php';

echo '<h1>Direct Test - Repeater Fields</h1>';

// Test 1: Read raw JSON data
echo '<h2>Test 1: Raw JSON Data Analysis</h2>';

$requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';
$folders = glob($requirements_dir . '*', GLOB_ONLYDIR);

if (!empty($folders)) {
    $folder = $folders[0];
    $folder_name = basename($folder);
    $data_json = $folder . '/data.json';
    
    echo '<h3>Testing with folder: ' . htmlspecialchars($folder_name) . '</h3>';
    
    if (file_exists($data_json)) {
        $json_content = file_get_contents($data_json);
        $data = json_decode($json_content, true);
        
        if ($data && is_array($data)) {
            // Test specific repeater fields
            $repeater_fields = array(
                'business_goals',
                'stakeholders', 
                'main_steps',
                'alternative_steps',
                'user_stories',
                'performance_indicators'
            );
            
            foreach ($repeater_fields as $field_name) {
                if (isset($data[$field_name]) && is_array($data[$field_name])) {
                    echo '<div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">';
                    echo '<h4>' . $field_name . '</h4>';
                    echo '<p><strong>Type:</strong> ' . gettype($data[$field_name]) . '</p>';
                    echo '<p><strong>Count:</strong> ' . count($data[$field_name]) . '</p>';
                    
                    if (!empty($data[$field_name])) {
                        echo '<p><strong>First Item Structure:</strong></p>';
                        $first_item = $data[$field_name][0];
                        echo '<pre style="background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;">';
                        echo htmlspecialchars(json_encode($first_item, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                        echo '</pre>';
                        
                        echo '<p><strong>All Items Preview:</strong></p>';
                        echo '<ul>';
                        foreach (array_slice($data[$field_name], 0, 3) as $index => $item) {
                            if (is_array($item)) {
                                $keys = array_keys($item);
                                $first_key = $keys[0] ?? 'unknown';
                                $value = $item[$first_key] ?? 'empty';
                                echo '<li>Item ' . ($index + 1) . ': ' . htmlspecialchars(substr($value, 0, 100)) . '...</li>';
                            } else {
                                echo '<li>Item ' . ($index + 1) . ': ' . htmlspecialchars($item) . '</li>';
                            }
                        }
                        if (count($data[$field_name]) > 3) {
                            echo '<li>... and ' . (count($data[$field_name]) - 3) . ' more items</li>';
                        }
                        echo '</ul>';
                    }
                    echo '</div>';
                }
            }
        }
    }
}

// Test 2: Test Requirements_JSON_Reader with repeater fields
echo '<h2>Test 2: Requirements_JSON_Reader with Repeater Fields</h2>';

try {
    $reader = new Requirements_JSON_Reader();
    $all_requirements = $reader->get_all_requirements();
    
    if (!empty($all_requirements)) {
        $requirement = $all_requirements[0];
        
        echo '<h3>Testing Repeater Fields Access:</h3>';
        
        $repeater_fields = array(
            'business_goals' => 'goal',
            'stakeholders' => 'stakeholder',
            'main_steps' => 'step',
            'user_stories' => 'story',
            'performance_indicators' => 'indicators'
        );
        
        foreach ($repeater_fields as $field_name => $sub_field) {
            echo '<div style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px;">';
            echo '<h4>Field: ' . $field_name . '</h4>';
            
            if (isset($requirement[$field_name])) {
                $field_data = $requirement[$field_name];
                echo '<p><strong>Direct Access:</strong> ✓ Available</p>';
                echo '<p><strong>Type:</strong> ' . gettype($field_data) . '</p>';
                echo '<p><strong>Count:</strong> ' . (is_array($field_data) ? count($field_data) : 'Not array') . '</p>';
                
                if (is_array($field_data) && !empty($field_data)) {
                    echo '<p><strong>Sample Values:</strong></p>';
                    echo '<ul>';
                    foreach (array_slice($field_data, 0, 3) as $index => $item) {
                        if (is_array($item) && isset($item[$sub_field])) {
                            echo '<li>' . htmlspecialchars(substr($item[$sub_field], 0, 80)) . '...</li>';
                        } elseif (is_string($item)) {
                            echo '<li>' . htmlspecialchars(substr($item, 0, 80)) . '...</li>';
                        } else {
                            echo '<li>Item ' . ($index + 1) . ': ' . gettype($item) . '</li>';
                        }
                    }
                    echo '</ul>';
                }
            } else {
                echo '<p><strong>Direct Access:</strong> ✗ Not available</p>';
            }
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">✗ Error: ' . $e->getMessage() . '</p>';
}

// Test 3: Test template functions with repeater fields
echo '<h2>Test 3: Template Functions with Repeater Fields</h2>';

try {
    $test_post_id = 123; // Mock post ID
    
    $repeater_fields = array(
        'business_goals' => 'goal',
        'stakeholders' => 'stakeholder',
        'main_steps' => 'step',
        'user_stories' => 'story'
    );
    
    foreach ($repeater_fields as $field_name => $sub_field) {
        echo '<div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 5px;">';
        echo '<h4>Testing: ' . $field_name . '</h4>';
        
        // Test get_requirement_field
        $field_value = get_requirement_field($field_name, $test_post_id);
        echo '<p><strong>get_requirement_field result:</strong></p>';
        echo '<ul>';
        echo '<li>Type: ' . gettype($field_value) . '</li>';
        echo '<li>Count: ' . (is_array($field_value) ? count($field_value) : 'Not array') . '</li>';
        echo '</ul>';
        
        if (is_array($field_value) && !empty($field_value)) {
            echo '<p><strong>First 3 items:</strong></p>';
            echo '<ol>';
            foreach (array_slice($field_value, 0, 3) as $item) {
                if (is_array($item) && isset($item[$sub_field])) {
                    echo '<li>' . htmlspecialchars(substr($item[$sub_field], 0, 100)) . '...</li>';
                } else {
                    echo '<li>' . htmlspecialchars(json_encode($item)) . '</li>';
                }
            }
            echo '</ol>';
        }
        
        // Test the_requirement_repeater
        echo '<p><strong>the_requirement_repeater result:</strong></p>';
        echo '<div style="background: white; padding: 10px; border-radius: 3px; border: 1px solid #ddd;">';
        the_requirement_repeater($field_name, $test_post_id, $sub_field, ' | ');
        echo '</div>';
        
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">✗ Error in template functions test: ' . $e->getMessage() . '</p>';
}

// Test 4: Test use_cases (complex repeater)
echo '<h2>Test 4: Complex Repeater (use_cases)</h2>';

try {
    $test_post_id = 123;
    $use_cases = get_requirement_field('use_cases', $test_post_id);
    
    echo '<div style="margin: 20px 0; padding: 15px; background: #fff3e0; border-radius: 5px;">';
    echo '<h4>Use Cases Analysis:</h4>';
    echo '<p><strong>Type:</strong> ' . gettype($use_cases) . '</p>';
    echo '<p><strong>Count:</strong> ' . (is_array($use_cases) ? count($use_cases) : 'Not array') . '</p>';
    
    if (is_array($use_cases) && !empty($use_cases)) {
        $first_use_case = $use_cases[0];
        echo '<p><strong>First Use Case Structure:</strong></p>';
        echo '<ul>';
        if (is_array($first_use_case)) {
            foreach ($first_use_case as $key => $value) {
                $type = gettype($value);
                $preview = '';
                if (is_string($value)) {
                    $preview = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                } elseif (is_array($value)) {
                    $preview = 'Array with ' . count($value) . ' items';
                } else {
                    $preview = (string)$value;
                }
                echo '<li><strong>' . htmlspecialchars($key) . ':</strong> ' . $type . ' - ' . htmlspecialchars($preview) . '</li>';
            }
        }
        echo '</ul>';
        
        // Test nested repeater fields
        $nested_repeaters = array('preconditions', 'postconditions', 'main_flow_steps');
        foreach ($nested_repeaters as $nested_field) {
            if (isset($first_use_case[$nested_field]) && is_array($first_use_case[$nested_field])) {
                echo '<p><strong>' . $nested_field . ':</strong></p>';
                echo '<ul>';
                foreach (array_slice($first_use_case[$nested_field], 0, 3) as $item) {
                    if (is_array($item)) {
                        $keys = array_keys($item);
                        $first_key = $keys[0] ?? 'unknown';
                        $value = $item[$first_key] ?? 'empty';
                        echo '<li>' . htmlspecialchars(substr($value, 0, 80)) . '...</li>';
                    } else {
                        echo '<li>' . htmlspecialchars($item) . '</li>';
                    }
                }
                echo '</ul>';
            }
        }
    }
    echo '</div>';
    
} catch (Exception $e) {
    echo '<p style="color: red;">✗ Error in use_cases test: ' . $e->getMessage() . '</p>';
}

echo '<h2>Test Summary</h2>';
echo '<div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h3>🔍 Diagnosis:</h3>';
echo '<p>This test helps identify why repeater fields might show empty values:</p>';
echo '<ul>';
echo '<li><strong>JSON Structure:</strong> Check if data is properly nested in arrays</li>';
echo '<li><strong>Field Access:</strong> Verify that template functions can access the data</li>';
echo '<li><strong>ACF Compatibility:</strong> Ensure data format matches ACF expectations</li>';
echo '<li><strong>Sub-field Access:</strong> Test if sub-field values are accessible</li>';
echo '</ul>';

echo '<h3>💡 Expected Results:</h3>';
echo '<ul>';
echo '<li>✓ JSON data should show proper array structure</li>';
echo '<li>✓ Template functions should return non-empty arrays</li>';
echo '<li>✓ Sub-field values should be accessible and display correctly</li>';
echo '<li>✓ Complex repeaters (use_cases) should maintain nested structure</li>';
echo '</ul>';
echo '</div>';

?>
