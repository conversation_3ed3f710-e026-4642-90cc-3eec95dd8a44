<?php





function translate_keys($data, $translations) {
    $translated_data = [];
    
    foreach ($data as $key => $value) {
        $translated_key = $translations[$key] ?? $key; // استخدم الترجمة إذا كانت موجودة، وإلا استخدم المفتاح الأصلي
        
        if (is_array($value)) {
            $translated_data[$translated_key] = translate_keys($value, $translations);
        } else {
            $translated_data[$translated_key] = $value;
        }
    }
    
    return $translated_data;
}

$translations = [
    "المحفز" => "trigger",
    "الأولوية" => "priority",
    "القواعد التجارية" => "business_rules",
    "الافتراضات" => "assumptions",
    "تكرار الاستخدام" => "frequency_of_use",
    "المتطلبات الخاصة" => "special_requirements",
    "ملاحظات ومشكلات" => "notes_and_issues",
    "تسلسل الأحداث" => "sequence_of_events",
    "المدخلات" => "inputs",
    "المخرجات" => "outputs",
    "التفاعلات مع المستخدمين" => "user_interactions",
    "الحالات الخاصة" => "special_cases",
    "سيناريوهات الاستخدام" => "usage_scenarios",
    "متطلبات الأمان" => "security_requirements",
    "التكامل مع الأنظمة الأخرى" => "integration_with_other_systems",
    "القيود والافتراضات" => "constraints_and_assumptions",
    "متطلبات الاختبار" => "testing_requirements",
    "التفاصل backend" => "backend_details",
    "التفاصل frontend" => "frontend_details",
    "الاشعارات" => "notifications"
];

// تحقق من وجود الملف وقراءته
$file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';

if (!file_exists($file_path)) {
    die("Error: File not found.");
}

$file_contents = file_get_contents($file_path);

if ($file_contents === false) {
    die("Error: Unable to read the file.");
}

$json_data = json_decode($file_contents, true);

if ($json_data === null) {
    die("Error: Invalid JSON data.");
}

// ترجمة المفاتيح في advanced_use_cases
foreach ($json_data as $content_key => &$content) {
    $new_simple_cases = [];
    if (is_array($content) && isset($content['use_cases']) && is_array($content['use_cases'])) {
        foreach ($content['use_cases'] as $case_key => &$use_case) {
            $id = str_replace('-', '.', $use_case['id'] );
            $current_case = $use_case;
            unset( $current_case['id'] );
            unset( $json_data[$content_key]['use_cases'][$case_key] );
            $new_simple_cases[ $id ] = $current_case;

            // $use_case = translate_keys($use_case, $translations);
        }
        $content['use_cases'] = $new_simple_cases;
    }

    $new_cases = [];
    if (is_array($content) && isset($content['advanced_use_cases']) && is_array($content['advanced_use_cases'])) {
        foreach ($content['advanced_use_cases'] as $case_key => &$use_case) {
            $id = str_replace('-', '.', $use_case['id'] );
            $current_case = $use_case;
            unset( $current_case['id'] );
            unset( $json_data[$content_key]['advanced_use_cases'][$case_key] );
            $new_cases[ $id ] = $current_case;

            // $use_case = translate_keys($use_case, $translations);
        }
        $content['advanced_use_cases'] = $new_cases;
    }

}

// حفظ البيانات المترجمة إلى ملف جديد
$new_file_path = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced-en-keys.json';
file_put_contents($new_file_path, json_encode($json_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

echo "Translation completed. Translated file saved as brd_reqs_with-advanced-en-keys.json";
?>
