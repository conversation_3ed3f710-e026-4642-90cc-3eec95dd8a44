{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"1": {"type": "object", "properties": {"title": {"type": "string"}, "business_goals": {"type": "array", "items": {"type": "string"}}, "stakeholders": {"type": "array", "items": {"type": "string"}}, "main_steps": {"type": "array", "items": {"type": "string"}}, "alternative_steps": {"type": "array", "items": {"type": "string"}}, "user_stories": {"type": "array", "items": {"type": "string"}}, "performance_indicators": {"type": "array", "items": {"type": "string"}}, "use_cases": {"type": "object", "properties": {"1.1": {"$ref": "#/definitions/use_case"}, "1.2": {"$ref": "#/definitions/use_case"}, "1.3": {"$ref": "#/definitions/use_case"}, "1.4": {"$ref": "#/definitions/use_case"}, "1.5": {"$ref": "#/definitions/use_case"}, "1.6": {"$ref": "#/definitions/use_case"}}, "additionalProperties": false}}, "required": ["title", "business_goals", "stakeholders", "main_steps", "user_stories", "performance_indicators", "use_cases"], "additionalProperties": false}, "2": {"type": "object", "properties": {"title": {"type": "string"}, "business_goals": {"type": "array", "items": {"type": "string"}}, "stakeholders": {"type": "array", "items": {"type": "string"}}, "main_steps": {"type": "array", "items": {"type": "string"}}, "alternative_steps": {"type": "array", "items": {"type": "string"}}, "user_stories": {"type": "array", "items": {"type": "string"}}, "performance_indicators": {"type": "array", "items": {"type": "string"}}, "use_cases": {"type": "object", "properties": {"2.1": {"$ref": "#/definitions/use_case"}, "2.2": {"$ref": "#/definitions/use_case"}, "2.3": {"$ref": "#/definitions/use_case"}, "2.4": {"$ref": "#/definitions/use_case"}, "2.5": {"$ref": "#/definitions/use_case"}}, "additionalProperties": false}}, "required": ["title", "business_goals", "stakeholders", "main_steps", "user_stories", "performance_indicators", "use_cases"], "additionalProperties": false}}, "required": ["1", "2"], "additionalProperties": false, "definitions": {"use_case": {"type": "object", "properties": {"description-simple": {"type": "string"}, "actor-simple": {"type": "string"}, "preconditions-simple": {"type": "array", "items": {"type": "string"}}, "postconditions-simple": {"type": "array", "items": {"type": "string"}}, "main_flow_steps-simple": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "actor": {"type": "string"}, "preconditions": {"type": "array", "items": {"type": "string"}}, "postconditions": {"type": "array", "items": {"type": "string"}}, "main_flow_steps": {"type": "array", "items": {"type": "string"}}, "alternative_flow_steps": {"type": "array", "items": {"type": "object", "properties": {"condition": {"type": "string"}, "steps": {"type": "array", "items": {"type": "string"}}}, "required": ["condition", "steps"], "additionalProperties": false}}, "exception_flow_steps": {"type": "array", "items": {"type": "object", "properties": {"condition": {"type": "string"}, "steps": {"type": "array", "items": {"type": "string"}}}, "required": ["condition", "steps"], "additionalProperties": false}}, "trigger": {"type": "string"}, "priority": {"type": "string"}, "business_rules": {"type": "array", "items": {"type": "string"}}, "assumptions": {"type": "array", "items": {"type": "string"}}, "frequency_of_use": {"type": "string"}, "special_requirements": {"type": "array", "items": {"type": "string"}}, "notes_and_issues": {"type": "array", "items": {"type": "string"}}, "events_sequence": {"type": "array", "items": {"type": "string"}}, "inputs": {"type": "array", "items": {"type": "string"}}, "outputs": {"type": "array", "items": {"type": "string"}}, "user_interactions": {"type": "array", "items": {"type": "string"}}, "special_conditions": {"type": "array", "items": {"type": "string"}}, "usage_scenarios": {"type": "array", "items": {"type": "string"}}, "security_requirements": {"type": "array", "items": {"type": "string"}}, "integration_with_other_systems": {"type": "array", "items": {"type": "string"}}, "constraints_and_assumptions": {"type": "array", "items": {"type": "string"}}, "testing_requirements": {"type": "array", "items": {"type": "string"}}, "backend_details": {"type": "object", "properties": {"api_endpoints": {"type": "array", "items": {"type": "object", "properties": {"method": {"type": "string"}, "endpoint": {"type": "string"}, "description": {"type": "string"}, "request": {"type": "object", "properties": {"body": {"type": "object", "additionalProperties": {"type": "string"}}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "response": {"type": "object", "properties": {"200": {"type": "object", "properties": {"description": {"type": "string"}, "body": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "400": {"type": "object", "properties": {"description": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}}, "required": ["method", "endpoint", "description", "request", "response"], "additionalProperties": false}}}, "additionalProperties": false}, "frontend_details": {"type": "object", "properties": {"screens": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "components": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "props": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["type", "props"], "additionalProperties": false}}}, "required": ["name", "components"], "additionalProperties": false}}}, "additionalProperties": false}, "notifications": {"type": "array", "items": {"type": "object", "properties": {"methods": {"type": "array", "items": {"type": "string"}}, "recipient": {"type": "string"}, "title": {"type": "string"}, "message": {"type": "string"}}, "required": ["methods", "recipient", "title", "message"], "additionalProperties": false}}, "flowchart_mermaid": {"type": "string"}}, "required": ["description-simple", "actor-simple", "preconditions-simple", "postconditions-simple", "main_flow_steps-simple", "description", "actor", "preconditions", "postconditions", "main_flow_steps", "trigger", "priority"], "additionalProperties": false}}}