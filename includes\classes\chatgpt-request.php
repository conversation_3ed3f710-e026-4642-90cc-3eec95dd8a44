<?php

class ChatGPT_Request {
    private $url;
    private $api_key;
    private $headers;
    private $options;
    private $context;
    private $response;
    private $new_message;
    private $new_messages;
    private $file_id;
    private $messages = [];

    // المتغيرات الافتراضية لمكونات الـ body
    private $default_model = 'gpt-4o';
    private $default_temperature = 0.7;
    private $default_max_tokens = 150;

    public function __construct() {
        $this->set_settings();
    }

    private function set_settings() {
        $this->url = 'https://api.openai.com/v1/chat/completions';
        $this->api_key = 'sk-chatgpt-request-class-8o6T4jOkvViqxSzwLZ47T3BlbkFJpv6jVDHZKv4ZzOY5bgJk';
        $this->headers = [
            'Authorization: Bearer ' . $this->api_key,
            'Content-Type: application/json'
        ];
    }

    private function request($method = 'POST') {
        $url = $this->url;

        // إعداد الرسائل الجديدة
        if (!empty($this->new_message)) {
            $this->messages[] = ['role' => 'user', 'content' => $this->new_message];
        }
        if (!empty($this->new_messages)) {
            foreach ($this->new_messages as $message) {
                $this->messages[] = ['role' => 'user', 'content' => $message];
            }
        }

        // إعداد الـ body
        $body = [
            'model' => $this->default_model,
            'temperature' => $this->default_temperature,
            'max_tokens' => $this->default_max_tokens,
            'messages' => $this->messages
        ];

        // إضافة الملف إذا تم تعريف رقم الملف
        if (!empty($this->file_id)) {
            $body['file'] = $this->file_id;
        }

        $this->options = [
            'http' => [
                'header' => implode("\r\n", $this->headers),
                'method' => $method,
                'content' => json_encode($body),
                'ignore_errors' => true
            ]
        ];

        $this->context = stream_context_create($this->options);

        try {
            $this->response = file_get_contents($url, false, $this->context);

            if ($this->response === false) {
                throw new Exception('Connection to ChatGPT API was not completed');
            } else {
                $this->response = ['success' => 1, 'gpt_resp' => json_decode($this->response)];
            }
        } catch (Exception $e) {
            $this->response = ['success' => 0, 'msg' => $e->getMessage()];
        }

        return $this->response;
    }

    public function ask_new_question($question) {
        $this->new_message = $question;
        return $this->request('POST');
    }

    public function start_conversation($initial_message) {
        $this->new_message = $initial_message;
        return $this->request('POST');
    }

    public function continue_conversation($conversation_id, $message) {
        $this->new_message = $message;
        return $this->request('POST');
    }

    public function assistant_with_file($initial_message, $file_id) {
        $this->new_message = $initial_message;
        $this->file_id = $file_id;
        return $this->request('POST');
    }

    public function continue_assistant_conversation($conversation_id, $message) {
        $this->new_message = $message;
        return $this->request('POST');
    }

    // دالة لعرض المعلومات بشكل مناسب
    public function display_response() {
        view( 'analyst-chat-page-template.php' , [] , ANYALYST_PLUGIN_DIR );;
        if (!$this->response['success']) {
            echo "Error: " . $this->response['msg'];
            return;
        }
    
        // استعراض الاستخدام (اختياري)
        $usage = $this->response['gpt_resp']->usage;
    
        echo "<div style='width: 100%; font-family: Arial, sans-serif;'>";
        
        // عرض الرسائل السابقة
        foreach ($this->messages as $msg) {
            $alignment = $msg['role'] === 'user' ? 'right' : 'left';
            $bgColor = $msg['role'] === 'user' ? '#007BFF' : '#28A745';
            $msgClass = $msg['role'] === 'user' ? 'user-message' : 'assistant-message';
            echo "<div class='{$msgClass}' style='text-align: {$alignment};'>";
            echo "<div style='display: inline-block; max-width: 60%; background-color: {$bgColor}; color: white; padding: 10px; border-radius: 10px; margin: 5px 0;'>";
            echo nl2br(htmlspecialchars($msg['content']));
            echo "</div>";
            echo "</div>";
        }
    
        // عرض الردود الجديدة
        foreach ($this->response['gpt_resp']->choices as $choice) {
            $message = $choice->message;
            $role = $message->role;
            $content = $message->content;
            $alignment = $role === 'user' ? 'right' : 'left';
            $bgColor = $role === 'user' ? '#007BFF' : '#28A745';
            $msgClass = $role === 'user' ? 'user-message' : 'assistant-message';
            echo "<div class='{$msgClass}' style='text-align: {$alignment};'>";
            echo "<div style='display: inline-block; max-width: 60%; background-color: {$bgColor}; color: white; padding: 10px; border-radius: 10px; margin: 5px 0;'>";
            echo nl2br(htmlspecialchars($content));
            echo "</div>";
            echo "</div>";
        }
    
        echo "</div>";
    
        // تنسيق الرسائل باستخدام CSS داخلي
        echo "
        <style>
            .user-message {
                text-align: right;
                margin-right: auto;
            }
            .assistant-message {
                text-align: left;
                margin-left: auto;
            }
            .user-message div, .assistant-message div {
                display: inline-block;
                max-width: 60%;
                padding: 10px;
                border-radius: 10px;
                margin: 5px 0;
            }
        </style>
        ";
    }
    
}
?>
