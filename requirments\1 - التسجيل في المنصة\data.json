{"title": "التسجيل في المنصة", "id": "1", "business_goals": [{"goal": "التأكد من أن كل مستخدم للمنصة قادر على تقديم الخدمة أو طلب الخدمة بطريقة آمنة وفعالة .."}, {"goal": "<PERSON><PERSON><PERSON> أن المعلومات المقدمة من المستخدمين صحيحة وحديثة"}], "stakeholders": [{"stakeholder": "المستخدمون (العملاء، مقدمو الخدمات)"}, {"stakeholder": "إداريي النظام"}], "main_steps": [{"step": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته"}, {"step": "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية"}, {"step": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لاداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)"}, {"step": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة"}, {"step": "المستخدم يتلقى إشعارات النظام (أو البريد في حالة استخدامه) تحتوي على تحديث حالة التسجيل (مقبول، مرفوض وسبب الرفض)"}], "alternative_steps": [{"step": "إذا تم رفض حساب المستخدم، المستخدم يمكنه المراجعة والتعديل وإعادة تقديم الطلب"}], "user_stories": [{"story": "كمستخدم عميل، أريد أن أقوم بالتسجيل في التطبيق بسرعة وسهولة حتى أتمكن من البدء في استخدام المنصة ، من أجل استخدام الخدمات المتوفرة"}, {"story": "كمقدم خدمة أريد أن أقوم بالتسجيل في التطبيق، من أجل عرض خدماتي و الحصول على فرص عمل جديدة وبناء شراكات"}, {"story": "كمستخدم، أنا بحاجة لتوثيق رقم الهاتف الخاص بي لضمان أمان حسابي"}, {"story": "كمستخدم، أنا بحاجة لتقديم وثائق تحقيق الشخصية الخاصة بي للتأكد من اعتمادي من النظام"}, {"story": "موظف إداري في المنصة، أرغب في تلقي إشعارات عند تسجيل مستخدم جديد أو مقدم خدمة جديد حتى أتمكن من فحص وثائقهم والتحقق منها للتأكد من التزام المنصة بمعايير الجودة والسلامة"}, {"story": "موظف إداري في المنصة، يحتاج إلى مراجعة طلبات الالتحاق, مراجعتها إما القبول أو وضع سبب الرفض"}, {"story": "كمستخدم، أريد تلقي تحديثات حول حالة طلب التسجيل الخاص بي، من أجل معرفة ما إذا كان مقبولًا أم مرفوضًا أم في انتظار المراجعة"}], "performance_indicators": [{"indicators": "عدد المسجلين مع تصنيف الفئات (تاجر، ناقل، وسيط....../ تسجيل مقبول، تسجيل مرفوض، إعادة تسجيل....)"}, {"indicators": "الفترة الزمنية للتسجيل"}], "use_cases": [{"is_new": false, "case_id": "1.1", "description_simple": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته", "actor_simple": "المستخدم (العميل أو مقدم الخدمة أو الناقل)", "preconditions_simple": [{"condition": "يجب أن يكون المستخدم غير مسجل سابقًا في النظام"}], "postconditions_simple": [{"condition": "المستخدم يكون لديه حساب جديد في النظام"}], "main_flow_steps_simple": [{"step": "فتح صفحة التسجيل"}, {"step": "إدخال البيانات الشخصية المطلوبة "}, {"step": "قراءة سياسة الخصوصية والموافقة عليها"}, {"step": "النقر على زر 'تسجيل'"}], "description": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق إدخال بياناته الشخصية وإنشاء حساب جديد", "actor": "المستخدم (العميل أو مقدم الخدمة)", "preconditions": [{"condition": "المستخدم غير مسجل سابقًا في النظام"}], "postconditions": [{"condition": "المستخدم يكون لديه حساب جديد في النظام"}], "main_flow_steps": [{"step": "فتح صفحة التسجيل"}, {"step": "إدخال البيانات الشخصية المطلوبة (الاسم، البريد الإلكتروني، رقم الهاتف، كلمة المرور)"}, {"step": "قراءة سياسة الخصوصية والموافقة عليها"}, {"step": "النقر على زر 'تسجيل'"}, {"step": "النظام يتحقق من صحة البيانات المدخلة"}, {"step": "النظام ينشئ حسابًا جديدًا للمستخدم"}], "events_sequence": [{"event": "المستخدم يفتح صفحة التسجيل"}, {"event": "المستخدم يدخل البيانات الشخصية المطلوبة"}, {"event": "المستخدم يقرأ  سياسة الخصوصية ويوافق عليها"}, {"event": "المستخدم ينقر على زر 'تسجيل'"}, {"event": "النظام يتحقق من صحة البيانات"}, {"event": "النظام ينشئ حسابًا جديدًا للمستخدم"}], "alternative_flow_steps": [{"condition": "إذا كانت البيانات المدخلة غير صحيحة", "steps": [{"step": "النظام يعرض رسالة خطأ توضح البيانات غير الصحيحة"}, {"step": "المستخدم يعدل البيانات المدخلة"}, {"step": "النقر على زر 'تسجيل' مرة أخرى"}]}], "exception_flow_steps": [{"condition": "إذا كان المستخدم مسجل مسبقًا", "steps": [{"step": "النظام يعرض رسالة تفيد بأن البريد الإلكتروني أو رقم الهاتف مستخدم مسبقًا"}, {"step": "المستخدم يمكنه استخدام خيار 'نسيت كلمة المرور' لاستعادة الوصول إلى الحساب"}]}], "trigger": "المستخدم يرغب في إنشاء حساب جديد", "priority": "عالي", "business_rules": [{"rule": "يجب أن تكون كلمة المرور قوية (تحتوي على حروف وأرقام ورموز)"}], "assumptions": [{"assumption": "المستخدم (الناق<PERSON> ) يمتلك رقم هاتف صالح"}, {"assumption": "باقي المستخدمين يمتلكون رقم هاتف وبريد الكتروني صالحين"}], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": false, "notes_and_issues": false, "inputs": [{"input": "الاسم"}, {"input": "الب<PERSON>يد الإلكتروني"}, {"input": "رقم الهاتف"}, {"input": "كلمة المرور"}, {"input": "الموافقة على سياسة الخصوصية"}], "outputs": false, "user_interactions": false, "special_conditions": false, "usage_scenarios": [{"scenario": "كمستخدم جديد، أرغب في إنشاء حساب على النظام حتى أتمكن من استخدام الخدمات المتاحة"}], "security_requirements": [{"requirement": "يجب التحقق من صحة البريد الإلكتروني ورقم الهاتف لتجنب التسجيلات الزائفة"}], "integration_with_other_systems": false, "constraints_and_assumptions": false, "testing_requirements": [{"requirement": "اختبارات وظيفية للتحقق من إنشاء الحساب بنجاح"}, {"requirement": "اختبارات الأمان للتحقق من قوة كلمة المرور وصحة البيانات المدخلة"}], "backend_details": [{"api_endpoints": [{"method": "POST", "endpoint": "/api/user/register", "description": "تسجيل مستخدم جديد", "request": [{"body": "name: نص\r\nemail: نص\r\nphone: نص\r\npassword: نص\r\nacceptedTerms: boolean", "headers": "Content-Type: application/json"}], "response": [{"status_code": "200", "description": "Success", "body": "userId: نص\r\nmessage: تم تسجيل المستخدم بنجاح"}, {"status_code": "400", "description": "Bad Request", "body": ""}, {"status_code": "5343", "description": "werewr", "body": "wer<PERSON><PERSON>"}]}]}], "frontend_details": [{"screens": [{"name": "شاشة التسجيل", "components": [{"acf_fc_layout": "form", "inputs": [{"type": "text", "label": "الاسم", "validation": "required", "options": ""}, {"type": "email", "label": "الب<PERSON>يد الإلكتروني", "validation": "required|^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$", "options": ""}, {"type": "text", "label": "الهاتف", "validation": "required|/^(009665|9665|\\+9665|05|5)5|0|3|6|4|9|1|8|7)([0-9]{7})$/", "options": ""}, {"type": "password", "label": "كلمة المرور", "validation": "required|min:8|contains:letters,numbers", "options": ""}, {"type": "checkbox", "label": "أو<PERSON><PERSON><PERSON> على الشروط والأحكام", "validation": "", "options": ""}], "submit_btn_message": "Register", "success_message": "تم التسجيل بنجاح", "success_redirect": "شاشة تسجيل الدخول"}]}]}, {"screens": [{"name": "شاشة تسجيل ناقل", "components": [{"acf_fc_layout": "form", "inputs": [{"type": "text", "label": "الاسم", "validation": "required", "options": ""}, {"type": "text", "label": "الهاتف", "validation": "required|/^(009665|9665|\\+9665|05|5)5|0|3|6|4|9|1|8|7)([0-9]{7})$/", "options": ""}, {"type": "password", "label": "كلمة المرور", "validation": "required|min:8|contains:letters,numbers", "options": ""}, {"type": "checkbox", "label": "أو<PERSON><PERSON><PERSON> على الشروط والأحكام", "validation": "", "options": ""}], "submit_btn_message": "Register", "success_message": "تم التسجيل بنجاح", "success_redirect": "شاشة تسجيل الدخول"}]}]}], "notifications": [{"methods": "الهات<PERSON> ,الايميل", "recipient": "المستخدم", "title": "تم التسجيل بنجاح", "message": "لقد تم إنشاء حسابك بنجاح. مرحبا بكم في منصتنا!"}, {"methods": "الهاتف", "recipient": "الناقل", "title": "تم التسجيل بنجاح", "message": "لقد تم إنشاء حسابك بنجاح. مرحبا بكم في منصتنا!"}], "flowchart_mermaid": "graph LR\r\n    A[User opens registration page] --> B[User enters personal details]\r\n    B --> C[User checks the I agree checkbox]\r\n    C --> D[User clicks Register]\r\n    D --> E[System validates data]\r\n    E --> F[System creates new account]"}, {"is_new": false, "case_id": "1.2", "description_simple": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP", "actor_simple": "المستخدم", "preconditions_simple": [{"condition": "أن يمتلك المستخدم رقم هاتف صحيح"}, {"condition": "إدخال رقم الهاتف بشكل صحيح"}], "postconditions_simple": [{"condition": "تم توثيق رقم الهاتف بنجاح"}], "main_flow_steps_simple": [{"step": "إدخال رقم الهاتف"}, {"step": "النقر على زر 'إرسال OTP'"}, {"step": "استلام رسالة OTP على الهاتف"}, {"step": "إدخال رمز OTP في الحقل المخصص"}, {"step": "النقر على زر 'توثيق'"}], "description": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لإداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "actor": "المستخدم", "preconditions": [{"condition": "إكمال خطوات التسجيل السابقة"}, {"condition": "فتح صفحة توثيق رقم الهاتف"}], "postconditions": [{"condition": "تم توثيق رقم الهاتف بنجاح"}], "main_flow_steps": [{"step": "المستخدم يفتح صفحة توثيق رقم الهاتف"}, {"step": "إدخال رقم الهاتف"}, {"step": "النقر على زر 'إرسال رمز التحقق'"}, {"step": "استلام رسالة OTP على الهاتف المدخل"}, {"step": "إدخال رمز OTP في الحقل المخصص"}, {"step": "النقر على زر 'تحقق'"}], "events_sequence": [{"event": "المستخدم يفتح صفحة توثيق رقم الهاتف"}, {"event": "إدخال رقم الهاتف"}, {"event": "النقر على زر 'إرسال رمز التحقق'"}, {"event": "استلام رسالة OTP على الهاتف المدخل"}, {"event": "إدخال رمز OTP في الحقل المخصص"}, {"event": "النقر على زر 'تحقق'"}], "alternative_flow_steps": [{"condition": "إذا لم يستلم المستخدم رسالة OTP", "steps": [{"step": "النقر على زر 'إعادة إرسال الرمز'"}, {"step": "استلام رمز OTP جديد"}, {"step": "إدخال الرمز الجديد في الحقل المخصص"}, {"step": "النقر على زر 'تحقق'"}]}], "exception_flow_steps": [{"condition": "إذا أدخل المستخدم رمز OTP غير صحيح", "steps": [{"step": "النظام يعرض رسالة خطأ تطلب إدخال الرمز الصحيح"}, {"step": "إعادة إدخال رمز OTP صحيح"}, {"step": "النقر على زر 'تحقق' مرة أخرى"}]}], "trigger": "المستخدم يرغب في توثيق رقم هاتفه كجزء من عملية التسجيل", "priority": "عالي", "business_rules": false, "assumptions": [{"assumption": "المستخدم يمتلك رقم هاتف صالح ويستطيع استلام رسائل OTP"}], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": false, "notes_and_issues": false, "inputs": [{"input": "رقم الهاتف"}, {"input": "<PERSON><PERSON><PERSON> OTP"}], "outputs": false, "user_interactions": false, "special_conditions": false, "usage_scenarios": [{"scenario": "كمستخدم جديد، أرغب في توثيق رقم هاتفي لضمان أمان حسابي"}], "security_requirements": [{"requirement": "يجب التحقق من صحة رمز OTP لضمان توثيق رقم الهاتف بشكل صحيح"}], "integration_with_other_systems": [{"system": "منصة نفاذ لتوثيق رقم الهاتف"}], "constraints_and_assumptions": false, "testing_requirements": [{"requirement": "اختبارات وظيفية للتحقق من عملية إرسال واستلام رمز OTP"}, {"requirement": "اختبارات الأمان للتحقق من صحة رمز OTP المدخل"}], "backend_details": [{"api_endpoints": [{"method": "POST", "endpoint": "/api/user/verify-phone", "description": "إرسال رمز OTP لتوثيق رقم الهاتف", "request": [{"body": "phoneNumber: نص", "headers": "Content-Type: application/json"}], "response": [{"status_code": "200", "description": "Success", "body": "status: نص\r\nmessage: تم إرسال رمز التحقق بنجاح"}, {"status_code": "400", "description": "Bad Request", "body": ""}]}, {"method": "POST", "endpoint": "/api/user/confirm-otp", "description": "التحقق من رمز OTP", "request": [{"body": "phoneNumber: نص\r\notp: نص", "headers": "Content-Type: application/json"}], "response": [{"status_code": "200", "description": "Success", "body": "status: نص\r\nmessage: تم التحقق من الهاتف بنجاح"}, {"status_code": "400", "description": "Invalid OTP", "body": ""}]}]}], "frontend_details": [{"screens": [{"name": "شاشة التحقق من الهاتف", "components": [{"acf_fc_layout": "form", "inputs": [{"type": "text", "label": "رقم الهاتف", "validation": "required|phone", "options": ""}, {"type": "text", "label": "<PERSON><PERSON><PERSON> التحقق", "validation": "required", "options": ""}], "submit_btn_message": "Verify", "success_message": "تم التحقق من الهاتف بنجاح", "success_redirect": "NextRegistrationStep"}]}]}], "notifications": [{"methods": "SMS", "recipient": "المستخدم", "title": "رمز التحقق الخاص بك هو: {code}", "message": "رمز التحقق الخاص بك هو: {code}"}], "flowchart_mermaid": "graph LR\r\n    A[User opens phone verification page] --> B[User enters phone number]\r\n    B --> C[User clicks 'Send OTP']\r\n    C --> D[User receives OTP]\r\n    D --> E[User enters OTP]\r\n    E --> F[User clicks 'Verify']\r\n    F --> G[Phone verified successfully]"}, {"is_new": false, "case_id": "1.3", "description_simple": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "actor_simple": "المستخدم", "preconditions_simple": [{"condition": "إكمال خطوات التسجيل الأساسية"}, {"condition": "توافر الوثائق المطلوبة"}], "postconditions_simple": [{"condition": "تم تحميل الوثائق التعريفية المطلوبة"}], "main_flow_steps_simple": [{"step": "اختيار نوع الوثيقة التعريفية"}, {"step": "تحميل صورة أو نسخة من الوثيقة"}, {"step": "النقر على زر 'تحميل الوثيقة'"}], "description": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة", "actor": "المستخدم", "preconditions": [{"condition": "إكمال خطوات التسجيل السابقة"}, {"condition": "توافر الوثائق المطلوبة "}], "postconditions": [{"condition": "تم تحميل الوثائق المطلوبة "}], "main_flow_steps": [{"step": "المستخدم يكمل خطوات التسجيل"}, {"step": "المستخدم يوثق رقم الهاتف عبر OTP "}, {"step": "النظام يطلب من المستخدم تحميل وثائق تعريفية "}, {"step": "المستخدم يرفع المستندات المطلوبة "}, {"step": "النظام يراجع المستندات"}], "events_sequence": [{"event": "النظام يطلب تحميل وثائق تعريفية"}, {"event": "المستخدم يقوم بتحميل الوثائق التعريفية"}, {"event": "النظام يتحقق من صحة المستندات"}], "alternative_flow_steps": [{"condition": "إذا  كان هناك خطأ في مستند", "steps": [{"step": "النظام يرسل رسالة بالخطأ للمستخدم"}, {"step": "المستخدم يقوم بإعادة تحميل الوثائق المطلوبة"}, {"step": "النظام يتحقق من الوثائق مرة أخرى"}]}], "exception_flow_steps": false, "trigger": "النظام يطلب من المستخدم وثائق تعريفية", "priority": "عالي", "business_rules": false, "assumptions": [{"assumption": "المستخدم يمتلك الوثائق اللازمة "}], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": false, "notes_and_issues": false, "inputs": [{"input": "الوثائق التعريفية"}], "outputs": [{"output": "حالة التحقق"}], "user_interactions": [{"interaction": "رفع المستندات عبر النظام"}, {"interaction": "تلقي اشعارا بحالة التحقق"}], "special_conditions": [{"condition": "يجب أن تكون المستندات واضحة للقراءة"}], "usage_scenarios": [{"scenario": "كمستخدم جديد، أرغب في تحميل الوثائق الخاصة بشكل صحيح "}], "security_requirements": [{"requirement": "يجب تأمين جميع المستندات المرفوعة وحمايتها من الوصول غير المصرح به"}], "integration_with_other_systems": [{"system": "النظام يجب أن يكون متكاملاً مع قواعد البيانات الحكومية للتحقق من صحة المستندات"}], "constraints_and_assumptions": [{"constraint": "النظام يجب أن يدعم رفع المستندات بصيغ وأحجام مختلفة"}], "testing_requirements": [{"requirement": "اختبارات وظيفية للتحقق من عملية إرسال واستلام المستندات"}, {"requirement": "اختبارات الأمان للتحقق من صحة المستندات المحملة"}], "backend_details": [{"api_endpoints": [{"method": "POST", "endpoint": "/api/user/upload-documents", "description": "تحميل الوثائق المطلوبة", "request": [{"body": "userId: نص\r\ndocuments: array", "headers": "Content-Type\": multipart/form-data"}], "response": [{"status_code": "200", "description": "Success", "body": "Documents uploaded successfully"}, {"status_code": "400", "description": "Bad Request", "body": ""}]}]}, {"api_endpoints": [{"method": "POST", "endpoint": "/verify-documents", "description": "Verifies the uploaded documents", "request": [{"body": "user_id: string", "headers": ""}], "response": [{"status_code": "200", "description": "Success", "body": "verification_status: string"}, {"status_code": "400", "description": "Bad Request", "body": ""}]}]}], "frontend_details": [{"screens": [{"name": "شاشة التحقق من المستندات", "components": [{"acf_fc_layout": "textblock", "text": "Please upload your verification documents"}, {"acf_fc_layout": "form", "inputs": [{"type": "file", "label": "document", "validation": "required: true\r\nerror_message: Please upload the required documents", "options": ""}], "submit_btn_message": "Upload Documents", "success_message": "", "success_redirect": ""}, {"acf_fc_layout": "textblock", "text": "Your documents are being verified. Please wait..."}, {"acf_fc_layout": "textblock", "text": "Document verification status will be displayed here."}]}]}], "notifications": [{"methods": "ايميل", "recipient": "المستخدم", "title": "التحقق من الوثائق التعريفية", "message": "Your document verification status: {status}"}], "flowchart_mermaid": "graph LR\r\n    A[Upload Documents] --> B[Verify Documents]\r\n    B --> C[Verification Successful]\r\n    B --> D[Verification Failed]\r\n    C --> E[Notify User of Success]\r\n    D --> F[Notify User of Failure]"}, {"is_new": false, "case_id": "1.4", "description_simple": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل", "actor_simple": "المستخدم", "preconditions_simple": [{"condition": "إكمال جميع خطوات التسجيل السابقة"}], "postconditions_simple": [{"condition": "تم إشعار المستخدم بحالة التسجيل"}], "main_flow_steps_simple": [{"step": "انتظار مراجعة وثائق التسجيل من قبل النظام"}, {"step": "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)"}, {"step": "متابعة التعليمات حسب حالة التسجيل"}], "description": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل سواء كان مقبولًا أو مرفوضًا مع سبب الرفض إذا كان متاحًا", "actor": "المستخدم", "preconditions": [{"condition": "إكمال جميع خطوات التسجيل السابقة"}], "postconditions": [{"condition": "تم إشعار المستخدم بحالة التسجيل"}], "main_flow_steps": [{"step": "انتظار مراجعة وثائق التسجيل من قبل النظام"}, {"step": "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض)"}, {"step": "متابعة التعليمات حسب حالة التسجيل"}], "events_sequence": [{"event": "النظام يراجع وثائق التسجيل"}, {"event": "النظام يرسل إشعارًا للمستخدم بحالة التسجيل"}, {"event": "المستخدم يتلقى الإشعار ويتابع التعليمات المناسبة"}], "alternative_flow_steps": false, "exception_flow_steps": false, "trigger": "إكمال المستخدم لخطوات التسجيل وانتظار الموافقة", "priority": "عالي", "business_rules": false, "assumptions": [{"assumption": "النظام يقوم بمراجعة الوثائق في وقت مناسب"}], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": false, "notes_and_issues": false, "inputs": false, "outputs": false, "user_interactions": false, "special_conditions": false, "usage_scenarios": [{"scenario": "كمستخدم، أرغب في تلقي إشعار بحالة تسجيل حسابي لمعرفة ما إذا كان قد تم قبوله أو رفضه وما هي الخطوات التالية"}], "security_requirements": false, "integration_with_other_systems": false, "constraints_and_assumptions": false, "testing_requirements": [{"requirement": "اختبارات وظيفية للتحقق من إرسال واستلام الإشعارات بحالة التسجيل"}, {"requirement": "اختبارات للتحقق من ظهور سبب الرفض في الإشعارات في حال وجوده"}], "backend_details": [{"api_endpoints": [{"method": "POST", "endpoint": "/api/user/registration-status", "description": "إرسال إشعار حالة التسجيل", "request": [{"body": "userId: نص\r\nstatus: نص\r\nrejectionReason: نص", "headers": "Content-Type: application/json"}], "response": [{"status_code": "200", "description": "Success", "body": "status: نص\r\nmessage: تم إرسال الإشعار بنجاح"}, {"status_code": "400", "description": "Bad Request", "body": ""}]}]}], "frontend_details": [{"screens": [{"name": "شاشة حالة التسجيل", "components": [{"acf_fc_layout": "textblock", "text": "Your registration status will be updated here"}]}]}], "notifications": [{"methods": "الاشعارات علي الهاتف, الاشعارات داخل التطبيق, ايميل", "recipient": "المستخدم", "title": "تحديث حالة التسجيل", "message": "تم تسجيلك {status}. {rejectionReason}"}], "flowchart_mermaid": "graph LR\r\n    A[Complete registration steps] --> B[System reviews registration documents]\r\n    B --> C[System sends registration status notification]\r\n    C --> D[User receives notification and follows instructions]"}, {"is_new": false, "case_id": "1.5", "description_simple": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor_simple": "المستخدم", "preconditions_simple": [{"condition": "استلام إشعار برفض الحساب"}, {"condition": "توافر سبب الرفض"}], "postconditions_simple": [{"condition": "إعاده تقديم طلب التسجيل بعد التعديل"}], "main_flow_steps_simple": [{"step": "قراءة سبب الرفض"}, {"step": "تعديل البيانات أو الوثائق المطلوبة"}, {"step": "إعادة تقديم طلب التسجيل"}], "description": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب", "actor": "المستخدم", "preconditions": [{"condition": "استلام إشعار برفض الحساب"}, {"condition": "توافر سبب الرفض"}], "postconditions": [{"condition": "إعادة تقديم طلب التسجيل بعد التعديل"}], "main_flow_steps": [{"step": "قراءة سبب الرفض"}, {"step": "تعديل البيانات أو الوثائق المطلوبة"}, {"step": "إعادة تقديم طلب التسجيل"}], "events_sequence": [{"event": "استلام إشعار برفض الحساب"}, {"event": "قراءة سبب الرفض"}, {"event": "تعديل البيانات أو الوثائق المطلوبة"}, {"event": "إعادة تقديم طلب التسجيل"}], "alternative_flow_steps": false, "exception_flow_steps": false, "trigger": "استلام إشعار برفض الحساب", "priority": "عالي", "business_rules": false, "assumptions": [{"assumption": "المستخدم يمكنه تعديل البيانات أو الوثائق المطلوبة"}], "frequency_of_use": "<PERSON><PERSON><PERSON> الحاجة", "special_requirements": false, "notes_and_issues": false, "inputs": false, "outputs": false, "user_interactions": false, "special_conditions": false, "usage_scenarios": [{"scenario": "كمستخدم، أرغب في إعادة تقديم طلب التسجيل بعد تعديله لضمان قبولي في النظام"}], "security_requirements": false, "integration_with_other_systems": false, "constraints_and_assumptions": false, "testing_requirements": [{"requirement": "اختبارات وظيفية للتحقق من عملية إعادة التقديم بعد التعديل"}], "backend_details": [{"api_endpoints": [{"method": "POST", "endpoint": "/api/user/resubmit-registration", "description": "إعادة تقديم طلب التسجيل بعد التعديل", "request": [{"body": "userId: نص\r\nupdatedData: object", "headers": "Authorization: Bearer token\r\nContent-Type: application/json"}], "response": [{"status_code": "200", "description": "Success", "body": "status: نص\r\nmessage: تم إعادة تقديم التسجيل بنجاح"}, {"status_code": "400", "description": "Bad Request", "body": ""}]}]}], "frontend_details": [{"screens": [{"name": "شاشة إعادة تقديم التسجيل", "components": [{"acf_fc_layout": "form", "inputs": [{"type": "text", "label": "تم تحديث البيانات", "validation": "required", "options": ""}], "submit_btn_message": "Resubmit", "success_message": "تم إعادة تقديم التسجيل بنجاح", "success_redirect": "شاشة حالة التسجيل"}]}]}], "notifications": false, "flowchart_mermaid": "graph LR\r\n    A[Receive rejection notification] --> B[Read rejection reason]\r\n    B --> C[Update required data]\r\n    C --> D[Resubmit registration]\r\n\r\n"}]}