{"4.2.1": {"title": "التسجيل في المنصة", "business_goals": ["التأكد من أن كل مستخدم للمنصة قادر على تقديم الخدمة أو طلب الخدمة بطريقة آمنة وفعالة.", "<PERSON><PERSON><PERSON> أن المعلومات المقدمة من المستخدمين صحيحة وحديثة."], "stakeholders": ["المستخدمون (العملاء، مقدمو الخدمات)", "إداريي النظام"], "main_steps": ["المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته.", "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية.", "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لاداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة.", "المستخدم يتلقى إشعارات النظام (أو البريد في حالة استخدامه) تحتوي على تحديث حالة التسجيل (مقبول، مرفوض وسبب الرفض)"], "alternative_steps": ["إذا تم رفض حساب المستخدم، المستخدم يمكنه المراجعة والتعديل وإعادة تقديم الطلب."], "user_stories": ["كمستخدم عميل، أريد أن أقوم بالتسجيل في التطبيق بسرعة وسهولة حتى أتمكن من البدء في استخدام المنصة ، من أجل استخدام الخدمات المتوفرة.", "كمقدم خدمة أريد أن أقوم بالتسجيل في التطبيق، من أجل عرض خدماتي و الحصول على فرص عمل جديدة وبناء شراكات.", "كمستخدم، أنا بحاجة لتوثيق رقم الهاتف الخاص بي لضمان أمان حسابي.", "كمستخدم، أنا بحاجة لتقديم وثائق تحقيق الشخصية الخاصة بي للتأكد من اعتمادي من النظام.", "موظف إداري في المنصة، أرغب في تلقي إشعارات عند تسجيل مستخدم جديد أو مقدم خدمة جديد حتى أتمكن من فحص وثائقهم والتحقق منها للتأكد من التزام المنصة بمعايير الجودة والسلامة.", "موظف إداري في المنصة، يحتاج إلى مراجعة طلبات الالتحاق, مراجعتها إما القبول أو وضع سبب الرفض.", "كمستخدم، أريد تلقي تحديثات حول حالة طلب التسجيل الخاص بي، من أجل معرفة ما إذا كان مقبولًا أم مرفوضًا أم في انتظار المراجعة."], "performance_indicators": ["عدد المسجلين مع تصنيف الفئات (تاجر، ناقل، وسيط....../ تسجيل مقبول، تسجيل مرفوض، إعادة تسجيل....).", "الفترة الزمنية للتسجيل."], "use_cases": [{"id": "4.2.1-1", "description": "المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته.", "actor": "المستخدم (العميل أو مقدم الخدمة)", "preconditions": ["يجب أن يكون المستخدم غير مسجل سابقًا في النظام."], "postconditions": ["المستخدم يكون لديه حساب جديد في النظام."], "main_flow_steps": ["فتح صفحة التسجيل.", "إدخال البيانات الشخصية المطلوبة.", "النقر على زر 'تسجيل'."]}, {"id": "4.2.1-2", "description": "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية.", "actor": "المستخدم", "preconditions": ["فتح صفحة شروط الخدمة وسياسة الخصوصية."], "postconditions": ["تمت الموافقة على الشروط والسياسات."], "main_flow_steps": ["عرض شروط الخدمة وسياسة الخصوصية.", "النقر على مربع الموافقة.", "النقر على زر 'أوافق'."]}, {"id": "4.2.1-3", "description": "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP.", "actor": "المستخدم", "preconditions": ["إدخال رقم الهاتف بشكل صحيح."], "postconditions": ["تم توثيق رقم الهاتف بنجاح."], "main_flow_steps": ["إدخال رقم الهاتف.", "النقر على زر 'إرسال OTP'.", "استلام رسالة OTP على الهاتف.", "إدخال رمز OTP في الحقل المخصص.", "النقر على زر 'توثيق'."]}, {"id": "4.2.1-4", "description": "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة.", "actor": "المستخدم", "preconditions": ["إكمال خطوات التسجيل الأساسية.", "توافر الوثائق المطلوبة."], "postconditions": ["تم تحميل الوثائق التعريفية المطلوبة."], "main_flow_steps": ["اختيار نوع الوثيقة التعريفية.", "تحميل صورة أو نسخة من الوثيقة.", "النقر على زر 'تحميل الوثيقة'."]}, {"id": "4.2.1-5", "description": "المستخدم يتلقى إشعارات النظام تحتوي على تحديث حالة التسجيل.", "actor": "المستخدم", "preconditions": ["إكمال جميع خطوات التسجيل السابقة."], "postconditions": ["تم إشعار المستخدم بحالة التسجيل."], "main_flow_steps": ["انتظار مراجعة وثائق التسجيل من قبل النظام.", "استلام إشعار حالة التسجيل (مقبول، مرفوض وسبب الرفض).", "متابعة التعليمات حسب حالة التسجيل."]}, {"id": "4.2.1-6", "description": "إذا تم رفض حساب المستخدم، يمكنه المراجعة والتعديل وإعادة تقديم الطلب.", "actor": "المستخدم", "preconditions": ["استلام إشعار برفض الحساب.", "توافر سبب الرفض."], "postconditions": ["إعادة تقديم طلب التسجيل بعد التعديل."], "main_flow_steps": ["قراءة سبب الرفض.", "تعديل البيانات أو الوثائق المطلوبة.", "إعادة تقديم طلب التسجيل."]}]}, "4.2.2": {"title": "الإشعارات", "business_goals": ["للتأكيد على أن كافة المستخدمين، بغض النظر عن الدور، يتلقون الإشعارات ذات الصلة بنشاطهم في النظام ولتحسين سهولة الاستخدام وتجربة المستخدم."], "stakeholders": ["إداريي النظام", "المستخدمون (مقدمو الخدمات والعملاء)"], "main_steps": ["تلقي الإشعارات الداخلية في لوحة التحكم للإداريين.", "تلقي الإشعارات الخارجية عبر البريد الإلكتروني أو الإشعارات push notification.", "تلقي الإشعارات الداخلية في مركز الإشعارات بالتطبيق للمستخدمين.", "إعدادات الإشعارات التي تسمح للمستخدمين بتفعيل/تعطيل أنواع محددة من الإشعارات.", "إعدادات الإشعارات التي تسمح للمستخدمين بتحديد طريقة استلام الإشعارات."], "alternative_steps": ["تعطيل الإشعارات.", "تغيير طرق تلقي الإشعارات."], "user_stories": ["مدير النظام: يج<PERSON> أن يحصل على إشعار داخلي عند حدوث حدث معين يتطلب انتباهه.", "مقدم الخدمة: يج<PERSON> أن يتلقى إشعارًا عند استلام طلب جديد للخدمة، أو رسالة جديدة، تحديث في حالة خدمة.", "العميل: يج<PERSON> أن يحصل على إشعار عندما يقوم مقدم الخدمة بتغييرات مهمة في الطلب مثل قبول الطلب، أو تغيير الحالة، أو إرسال رسالة جديدة.", "العميل و مقدم الخدمة: يجب أن يحصلوا على إشعار دعائي عند تقديم عروض جديدة أو خدمات أو تحديثات في النظام."], "performance_indicators": ["عدد الاشعارات المرسلة مع التصنيف حسب نوع المستخدم."], "use_cases": [{"id": "4.2.2-1", "description": "تلقي الإشعارات الداخلية في لوحة التحكم للإداريين.", "actor": "إداري النظام", "preconditions": ["وجود أحداث أو أنشطة تحتاج إلى إشعار الإداري."], "postconditions": ["إشعار الإداري بالأحداث المهمة."], "main_flow_steps": ["حدوث نشاط أو حدث في النظام.", "إرسال إشعار إلى لوحة التحكم للإداري.", "عرض الإشعار في لوحة التحكم."]}, {"id": "4.2.2-2", "description": "تلقي الإشعارات الخارجية عبر البريد الإلكتروني أو الإشعارات push notification.", "actor": "المستخدم (العميل أو مقدم الخدمة)", "preconditions": ["إعداد البريد الإلكتروني أو تمكين الإشعارات push."], "postconditions": ["تلقى المستخدم إشعارًا بالبريد الإلكتروني أو push notification."], "main_flow_steps": ["حدوث نشاط أو حدث في النظام.", "إرسال إشعار بالبريد الإلكتروني أو push notification.", "تلقي المستخدم للإشعار."]}, {"id": "4.2.2-3", "description": "تلقي الإشعارات الداخلية في مركز الإشعارات بالتطبيق للمستخدمين.", "actor": "المستخدم (العميل أو مقدم الخدمة)", "preconditions": ["تسجيل الدخول في التطبيق."], "postconditions": ["ظهور الإشعار في مركز الإشعارات بالتطبيق."], "main_flow_steps": ["حدوث نشاط أو حدث في النظام.", "إرسال إشعار إلى مركز الإشعارات في التطبيق.", "ظهور الإشعار في مركز الإشعارات للمستخدم."]}, {"id": "4.2.2-4", "description": "إعدادات الإشعارات التي تسمح للمستخدمين بتفعيل/تعطيل أنواع محددة من الإشعارات.", "actor": "المستخدم", "preconditions": ["الوصول إلى إعدادات الإشعارات في التطبيق."], "postconditions": ["تحديث تفضيلات الإشعارات الخاصة بالمستخدم."], "main_flow_steps": ["فتح إعدادات الإشعارات في التطبيق.", "اختيار أنواع الإشعارات لتفعيلها أو تعطيلها.", "حفظ التفضيلات."]}, {"id": "4.2.2-5", "description": "إعدادات الإشعارات التي تسمح للمستخدمين بتحديد طريقة استلام الإشعارات.", "actor": "المستخدم", "preconditions": ["الوصول إلى إعدادات الإشعارات في التطبيق."], "postconditions": ["تحديث طريقة استلام الإشعارات الخاصة بالمستخدم."], "main_flow_steps": ["فتح إعدادات الإشعارات في التطبيق.", "اختيار طريقة استلام الإشعارات (بريد إلكتروني، push notification).", "حفظ التفضيلات."]}]}, "4.2.3": {"title": "تعديل الحساب وتغيير كلمة المرور", "business_goals": ["السماح للمستخدمين بتحديث معلوماتهم الشخصية بما في ذلك كلمة المرور.", "تعزيز الأمان من خلال تحديد متطلبات لكلمات المرور."], "stakeholders": ["جميع المستخدمين"], "main_steps": ["المستخدم ينقر على 'تعديل الحساب' أو 'تغيير كلمة المرور' في حسابه.", "المستخدم يمكنه تحديث المعلومات الشخصية و/أو كلمة المرور.", "لتغيير كلمة المرور، يتوجب على المستخدم إدخال كلمة المرور القديمة ثم إدخال الجديدة.", "كلمة المرور الجديدة يجب أن تكون أكثر من 8 رموز وأن تحتوي على حروف وأرقام.", "المستخدم ينقر على 'حفظ' لتأكيد التغييرات."], "alternative_steps": ["ليس هناك أي حاجة لتأكيد المستخدم عبر البريد الإلكتروني أو الرسالة النصية."], "user_stories": ["كمستخدم، أريد تحديث معلومات حسابي الشخصية (بما في ذلك كلمة المرور) بحيث يمكنني الحفاظ على الدقة والأمان.", "كمستخدم، أريد تغيير كلمة السر الخاصة بي بإدخال القديمة أولاً، للتأكد من أنني الشخص الوحيد الذي يمكنه تغييرها.", "كمستخدم، أريد أن تكون كلمة السر التي اختارها آمنة ومعقدة، وذلك لحماية حسابي من الاختراق."], "performance_indicators": ["عدد المستخدمين الذي قاموا بتعديل او تحديث حساباتهم."], "use_cases": [{"id": "4.2.3-1", "description": "المستخدم ينقر على 'تعديل الحساب' أو 'تغيير كلمة المرور' في حسابه.", "actor": "المستخدم", "preconditions": ["المستخدم مسجل في النظام.", "المستخدم مسجل الدخول إلى حسابه."], "postconditions": ["فتح صفحة تعديل الحساب أو تغيير كلمة المرور."], "main_flow_steps": ["تسجيل الدخول إلى الحساب.", "النقر على 'تعديل الحساب' أو 'تغيير كلمة المرور'.", "فتح صفحة التعديل."]}, {"id": "4.2.3-2", "description": "المستخدم يمكنه تحديث المعلومات الشخصية و/أو كلمة المرور.", "actor": "المستخدم", "preconditions": ["فتح صفحة تعديل الحساب أو تغيير كلمة المرور."], "postconditions": ["تم تحديث المعلومات الشخصية و/أو كلمة المرور."], "main_flow_steps": ["تعديل المعلومات الشخصية و/أو كلمة المرور.", "النقر على زر 'حفظ' لتأكيد التغييرات."]}, {"id": "4.2.3-3", "description": "لتغيير كلمة المرور، يتوجب على المستخدم إدخال كلمة المرور القديمة ثم إدخال الجديدة.", "actor": "المستخدم", "preconditions": ["فتح صفحة تغيير كلمة المرور.", "تذكر كلمة المرور القديمة."], "postconditions": ["تم تغيير كلمة المرور بنجاح."], "main_flow_steps": ["إدخال كلمة المرور القديمة.", "إدخال كلمة المرور الجديدة.", "النقر على زر 'حفظ' لتأكيد التغيير."]}, {"id": "4.2.3-4", "description": "كلمة المرور الجديدة يجب أن تكون أكثر من 8 رموز وأن تحتوي على حروف وأرقام.", "actor": "المستخدم", "preconditions": ["فتح صفحة تغيير كلمة المرور.", "إدخال كلمة المرور القديمة."], "postconditions": ["تم التحقق من صحة كلمة المرور الجديدة."], "main_flow_steps": ["إدخال كلمة المرور الجديدة.", "التحقق من أنها تحتوي على أكثر من 8 رموز وتشمل حروف وأرقام.", "النقر على زر 'حفظ' لتأكيد التغيير."]}, {"id": "4.2.3-5", "description": "المستخدم ينقر على 'حفظ' لتأكيد التغييرات.", "actor": "المستخدم", "preconditions": ["فتح صفحة تعديل الحساب أو تغيير كلمة المرور.", "إدخال التغييرات المطلوبة."], "postconditions": ["تم حفظ التغييرات بنجاح."], "main_flow_steps": ["إدخال التغييرات المطلوبة.", "النقر على زر 'حفظ'.", "تأكيد حفظ التغييرات."]}]}, "4.2.4": {"title": "تسجيل الدخول", "business_goals": ["تزويد المستخدمين بوسيلة آمنة وسهلة للدخول إلى حساباتهم.", "تقليل فرص الاختراق والوصول غير الشرعي من خلال استخدام OTP."], "stakeholders": ["جميع المستخدمين"], "main_steps": ["المستخدم يدخل رقم الهاتف وكلمة المرور ثم ينقر على زر 'تسجيل الدخول'.", "في حالة الرغبة في استخدام OTP، المستخدم يضغط على الزر 'إرسال OTP'. المنصة ترسل رسالة نصية مع الOTP إلى رقم الهاتف.", "المستخدم يدخل ال OTP المرسلة عبر الرسالة النصية ويضغط على 'تسجيل الدخول'.", "جعل التطبيق في حالة تسجيل (دخول للحساب) مستمرة للعمل في الخلفية (نشط – غير نشط)."], "alternative_steps": ["إذا كان المستخدم قد نسي كلمة المرور، يمكنه الضغط على 'نسيت كلمة المرور' واتباع الإرشادات لاستعادة الحساب.", "إذا كان الOTP غير صالح (مرت أكثر من 3 دقائق منذ إرسالها)، يمكن للمستخدم طلب OTP جديدة."], "user_stories": ["كمستخدم، أريد تسجيل الدخول إلى حسابي بسهولة وأمان باستخدام رقم الهاتف وكلمة السر.", "كمستخدم، أريد القدرة على استخدام OTP كطريقة بديلة لتسجيل الدخول، للحفاظ على أمان حسابي وتقديم خيار تسجيل دخول مرن.", "كمستخدم، أريد القدرة على طلب OTP جديدة إذا كانت القديمة غير صالحة."], "performance_indicators": ["عدد المستخدمين الذين قاموا بتسجيل الدخول (حالياً او خلال فترات سابقة).", "عدد المستخدمين النشطين حاليآ او خلال فترات سابقة.", "عدد المستخدمين الذين لا يمكنهم ممارسة الأعمال بسبب احد الوثائق المقدمة مثل انتهاء صلاحيتها أو عدم تقديمها."], "use_cases": [{"id": "4.2.4-1", "description": "المستخدم يدخل رقم الهاتف وكلمة المرور ثم ينقر على زر 'تسجيل الدخول'.", "actor": "المستخدم", "preconditions": ["المستخدم مسجل في النظام.", "المستخدم يمتلك رقم هاتف مسجل وكلمة مرور صحيحة."], "postconditions": ["تم تسجيل دخول المستخدم إلى حسابه."], "main_flow_steps": ["فتح صفحة تسجيل الدخول.", "إدخال رقم الهاتف وكلمة المرور.", "النقر على زر 'تسجيل الدخول'."]}, {"id": "4.2.4-2", "description": "في حالة الرغبة في استخدام OTP، المستخدم يضغط على الزر 'إرسال OTP'. المنصة ترسل رسالة نصية مع الOTP إلى رقم الهاتف.", "actor": "المستخدم", "preconditions": ["المستخدم مسجل في النظام.", "المستخدم يمتلك رقم هاتف مسجل."], "postconditions": ["تم إرسال OTP إلى هاتف المستخدم."], "main_flow_steps": ["النقر على زر 'إرسال OTP'.", "استلام رسالة نصية تحتوي على OTP.", "إدخال OTP في الحقل المخصص.", "النقر على زر 'تسجيل الدخول'."]}, {"id": "4.2.4-3", "description": "جعل التطبيق في حالة تسجيل (دخول للحساب) مستمرة للعمل في الخلفية (نشط – غير نشط).", "actor": "المستخدم", "preconditions": ["تم تسجيل دخول المستخدم بنجاح."], "postconditions": ["التطبيق يعمل في الخلفية بحالة تسجيل دخول مستمرة."], "main_flow_steps": ["تسجيل الدخول إلى الحساب.", "التبديل إلى تطبيق آخر أو العودة إلى الشاشة الرئيسية.", "<PERSON><PERSON>ان بقاء حالة تسجيل الدخول مستمرة في التطبيق."]}, {"id": "4.2.4-4", "description": "إذا كان المستخدم قد نسي كلمة المرور، يمكنه الضغط على 'نسيت كلمة المرور' واتباع الإرشادات لاستعادة الحساب.", "actor": "المستخدم", "preconditions": ["المستخدم غير قادر على تذكر كلمة المرور.", "وجود خيار 'نسيت كلمة المرور'."], "postconditions": ["استعادة المستخدم لحسابه عن طريق إعادة تعيين كلمة المرور."], "main_flow_steps": ["النقر على 'نسيت كلمة المرور'.", "اتباع الإرشادات لإعادة تعيين كلمة المرور.", "استعادة الوصول إلى الحساب."]}, {"id": "4.2.4-5", "description": "إذا كان الOTP غير صالح (مرت أكثر من 3 دقائق منذ إرسالها)، يمكن للمستخدم طلب OTP جديدة.", "actor": "المستخدم", "preconditions": ["انتهاء صلاحية الOTP.", "وجود خيار لإعادة إرسال OTP."], "postconditions": ["استلام المستخدم OTP جديدة صالحة."], "main_flow_steps": ["النقر على خيار 'إعادة إرسال OTP'.", "استلام OTP جديدة.", "إدخال OTP الجديدة في الحقل المخصص."]}]}, "4.2.5": {"title": "إعادة تعيين كلمة السر", "business_goals": ["تزويد المستخدمين بالقدرة على استعادة الوصول إلى حساباتهم بسهولة في حالة نسيان كلمة المرور.", "تقليل فرص تعطل الخدمة أو التأخير بسبب نسيان كلمة السر وتحقيق أقصى قدر من الكفاءة والراحة للمستخدم."], "stakeholders": ["المستخدمون الذين نسوا كلمة المرور."], "main_steps": ["المستخدم ينقر على 'نسيت كلمة المرور'.", "المستخدم يدخل رقم الهاتف وينقر على 'إرسال'.", "يتم إرسال رسالة نصية إلى الهاتف المحمول للمستخدم تحتوي على OTP وكلمة سر مؤقتة.", "المستخدم يدخل OTP ويقوم بالدخول.", "بمجرد تسجيل الدخول، يتم توجيه المستخدم إلى صفحة تعديل الحساب لوضع كلمة سر جديدة."], "alternative_steps": ["إذا تم إدخال OTP أو كلمة السر المؤقتة بشكل خاطئ، يتم تقديم الرسائل الخطأ المناسبة.", "إذا شعر المستخدم بالحاجة لإعادة المحاولة، يمكن طلب OTP وكلمة السر المؤقتة مرة أخرى."], "user_stories": ["كمستخدم، أريد أن أتمكن من استعادة الوصول إلى حسابي إذا نسيت كلمة المرور.", "كمستخدم، أرغب في الحصول على OTP وكلمة سر مؤقتة بسرعة وسهولة عبر الرسائل النصية.", "كمستخدم، أرغب في تغيير كلمة السر إلى واحدة جديدة بمجرد تسجيل الدخول باستخدام كلمة المرور المؤقتة."], "performance_indicators": ["عدد المستخدمين الذين قاموا بإعادة او تغيير كلمات المرور."], "use_cases": [{"id": "4.2.5-1", "description": "يبدأ المستخدم عملية إعادة تعيين كلمة المرور من خلال النقر على 'نسيت كلمة المرور'.", "actor": "المستخدم", "preconditions": ["المستخدم لديه حساب مسجل في النظام."], "postconditions": ["تم إرسال OTP وكلمة سر مؤقتة إلى رقم الهاتف المسجل."], "main_flow_steps": ["المستخدم ينقر على 'نسيت كلمة المرور'.", "المستخدم يدخل رقم الهاتف وينقر على 'إرسال'.", "يتم إرسال رسالة نصية تحتوي على OTP وكلمة سر مؤقتة إلى الهاتف المسجل."]}, {"id": "4.2.5-2", "description": "يستخدم المستخدم OTP وكلمة السر المؤقتة لتسجيل الدخول وتعيين كلمة سر جديدة.", "actor": "المستخدم", "preconditions": ["المستخدم تلقى OTP وكلمة السر المؤقتة."], "postconditions": ["تم تعيين كلمة سر جديدة بنجاح."], "main_flow_steps": ["المستخدم يدخل OTP المرسلة عبر الرسالة النصية.", "المستخدم يقوم بتسجيل الدخول باستخدام كلمة السر المؤقتة.", "النظام يوجه المستخدم إلى صفحة تعديل الحساب.", "المستخدم يعين كلمة سر جديدة ويؤكدها.", "المستخدم ينقر على 'حفظ' لتأكيد التغييرات."]}, {"id": "4.2.5-3", "description": "في حال إدخال OTP أو كلمة السر المؤقتة بشكل خاطئ، يتم تقديم الرسائل الخطأ المناسبة.", "actor": "المستخدم", "preconditions": ["المستخدم يحاول تسجيل الدخول باستخدام OTP أو كلمة السر المؤقتة."], "postconditions": ["المستخدم يعلم أن البيانات المدخلة خاطئة ويمكنه إعادة المحاولة."], "main_flow_steps": ["المستخدم يدخل OTP أو كلمة السر المؤقتة.", "النظام يتحقق من صحة البيانات.", "إذا كانت البيانات خاطئة، يعرض النظام رسالة خطأ."]}, {"id": "4.2.5-4", "description": "في حال فشل المستخدم في إدخال OTP أو كلمة السر المؤقتة بشكل صحيح، يمكنه طلب إعادة إرسال OTP وكلمة السر المؤقتة.", "actor": "المستخدم", "preconditions": ["المستخدم لم ينجح في تسجيل الدخول باستخدام OTP أو كلمة السر المؤقتة."], "postconditions": ["تم إرسال OTP وكلمة سر مؤقتة جديدة."], "main_flow_steps": ["المستخدم ينقر على خيار 'إعادة إرسال OTP وكلمة السر المؤقتة'.", "النظام يرسل OTP وكلمة سر مؤقتة جديدة إلى الهاتف المسجل."]}]}, "4.2.6": {"title": "تسجيل المركبات", "business_goals": ["<PERSON><PERSON><PERSON> أن مقدمي خدمة النقل مستعدون بشكل كافي ولديهم المركبات اللازمة للقيام بعمليات النقل.", "أن المركبات لديها التراخيص القانونية وتلبي معايير الجودة والسلامة المطلوبة."], "stakeholders": ["مقدم خدمة النقل (المستخدم)"], "main_steps": ["التحقق من أن المستخدم مقدم خدمة نقل.", "تقديم خيارات لإدخال معلومات السيارة / السيارات المستخدمة في النقل.", "التحقق من صحة المعلومات المقدمة والوثائق المرفقة."], "alternative_steps": ["في حالة كانت وثائق السيارة غير صحيحة، يتم تقديم رسالة خطأ مع توضيح الخطأ وطلب تصحيحه."], "user_stories": ["كمقدم خدمة نقل فردي، أريد إمكانية إضافة معلومات سيارتي ورفع الوثائق المطلوبة، لكي أثبت صلاحية سيارتي لأداء عمليات النقل.", "كمقدم خدمة نقل تابع إلى (مؤسسة - شركة)، أريد إمكانية إضافة معلومات السيارة التي أعمل عليها وإرفاقها إلى أسطول الشركة التي أعمل لديها.", "كمقدم خدمة نقل أعمل كسائق، في حالة وجود أي أخطاء في البيانات أو الوثائق المرفقة، أرغب في تلقي رسالة خطأ توضح المشكلة، حتى أتمكن من تصحيحها واستكمال التسجيل.", "كشركة نقل، أرغب في القدرة على إضافة عدد غير محدود من السيارات، لكي أتمكن من تسجيل كل السيارات التي تعتبر جزءًا من أسطول الشركة."], "performance_indicators": ["عدد المركبات المسجلة حسب تصنيف فئات محدد (نوع المركبة، حجم المركبة، موديل المركبة...).", "عدد المركبات المسجلة والتي لا يمكنها ممارسة الأعمال بسبب أحد الوثائق المسجلة (انتهاء الصلاحية، عدم تقديم المستند...)."], "use_cases": [{"id": "4.2.6-1", "description": "إضافة معلومات سيارة جديدة إلى نظام تسجيل المركبات", "actor": "مقد<PERSON> خدمة النقل", "preconditions": ["وجود حساب لمقدم خدمة النقل على النظام", "توفر الوثائق المطلوبة للمركبة"], "postconditions": ["إضافة السيارة الجديدة إلى نظام التسجيل", "تحديث حالة المركبة لتكون جاهزة للاستخدام في عمليات النقل"], "main_flow_steps": ["مقدم خدمة النقل يدخل إلى صفحة إدارة المركبات.", "يختار خيار 'إضافة مركبة جديدة'.", "يدخل معلومات السيارة (نوع السيارة، رقم اللوحة، السنة، موديل السيارة).", "يرفع الوثائق المطلوبة (رخصة المركبة، تأمين المركبة).", "يقوم النظام بالتحقق من صحة المعلومات والوثائق المرفقة.", "يتم إضافة السيارة الجديدة إلى نظام التسجيل."]}, {"id": "4.2.6-2", "description": "تعديل معلومات سيارة مسجلة مسبقًا", "actor": "مقد<PERSON> خدمة النقل", "preconditions": ["وجود حساب لمقدم خدمة النقل على النظام", "وجود سيارة مسجلة مسبقًا في النظام"], "postconditions": ["تحديث معلومات السيارة في النظام", "إمكانية رؤية التحديثات في تفاصيل السيارة"], "main_flow_steps": ["مقدم خدمة النقل يدخل إلى صفحة إدارة المركبات.", "يبحث عن السيارة المطلوب تعديل معلوماتها.", "يقوم بتعديل المعلومات المطلوبة (مثل رقم اللوحة، السنة، موديل السيارة).", "يرفع الوثائق الجديدة إذا كانت هناك حاجة لذلك.", "يحفظ التعديلات."]}, {"id": "4.2.6-3", "description": "التحقق من صحة وثائق سيارة مسجلة", "actor": "إداري النظام", "preconditions": ["وجود حساب إداري على النظام", "وجود سيارة مسجلة ضمن النظام"], "postconditions": ["التأكد من صحة وثائق السيارة", "إبلاغ مقدم خدمة النقل في حال وجود أي مشكلات في الوثائق"], "main_flow_steps": ["إداري النظام يدخل إلى صفحة إدارة المركبات.", "يبحث عن السيارة المطلوب التحقق من وثائقها.", "يتحقق من صحة الوثائق المرفقة (رخصة المركبة، تأمين المركبة).", "في حال وجود أي مشكلات، يتم إرسال رسالة خطأ إلى مقدم خدمة النقل مع توضيح المشكلة وطلب تصحيحها."]}]}, "4.2.7": {"title": "إضافة معلومات السائقين وربطهم بالسيارات", "business_goals": ["تحقيق القدرة على تقديم معلومات دقيقة للعملاء حول السائقين والسيارات المتوفرة لخدمات النقل."], "stakeholders": ["مقدمي الخدمات (شركات النقل)"], "main_steps": ["مقدم الخدمة يدخل إلى صفحة إدارة السائقين.", "يختار إضافة سائق جديد.", "يدخل معلومات السائق (اسم السائق، رقم الهاتف، العنوان، رقم الرخصة، صورة الرخصة).", "يتم ربط السائق مع سيارة محددة من قائمة السيارات المتاحة له."], "alternative_steps": ["مقدم الخدمة يستطيع تعديل معلومات السائق أو فصله عن السيارة في أي وقت."], "user_stories": ["مقدم الخدمة: يرغب في إضافة سائق جديد، لذا يقوم بدخول صفحة إدارة السائقين، يضغط على إضافة سائق جديد، يملأ معلومات السائق ويربطه بالسيارة المناسبة.", "مقدم الخدمة: يرغب في تعديل معلومات سائق، يقوم بالدخول إلى صفحة إدارة السائقين، يبحث عن السائق المطلوب، يقوم بتعديل المعلومات المطلوبة.", "مقدم الخدمة: يرغب في فصل سائق عن سيارة، يقوم بالدخول إلى صفحة إدارة السائقين، يبحث عن السائق المطلوب، يقوم بفصل السائق عن السيارة."], "performance_indicators": ["عد<PERSON> المركبات المضافة من شركات النقل.", "عد<PERSON> المركبات المضافة والغير مرتبطة بسائقين."], "use_cases": [{"id": "4.2.7-1", "description": "إضافة سائق جديد إلى نظام إدارة السائقين وربطه بسيارة متاحة", "actor": "مقدم الخدمة", "preconditions": ["وجود حساب لمقدم الخدمة على النظام", "وجود سيارات متاحة ضمن حساب مقدم الخدمة"], "postconditions": ["إضافة السائق الجديد وربطه بسيارة محددة", "إمكانية تعديل معلومات السائق أو فصله عن السيارة في المستقبل"], "main_flow_steps": ["مقدم الخدمة يدخل إلى صفحة إدارة السائقين.", "مقدم الخدمة يختار خيار 'إضافة سائق جديد'.", "مقدم الخدمة يدخل معلومات السائق (اسم السائق، رقم الهاتف، العنوان، رقم الرخصة، صورة الرخصة).", "مقدم الخدمة يربط السائق بسيارة محددة من قائمة السيارات المتاحة له."]}, {"id": "4.2.7-2", "description": "تعديل معلومات سائق موجود", "actor": "مقدم الخدمة", "preconditions": ["وجود حساب لمقدم الخدمة على النظام", "وجود سائق مسجل مسبقًا في النظام"], "postconditions": ["تحديث معلومات السائق في النظام", "إمكانية رؤية التحديثات في تفاصيل السائق"], "main_flow_steps": ["مقدم الخدمة يدخل إلى صفحة إدارة السائقين.", "مقدم الخدمة يبحث عن السائق المطلوب.", "مقدم الخدمة يقوم بتعديل المعلومات المطلوبة.", "مقدم الخدمة يحفظ التعديلات."]}, {"id": "4.2.7-3", "description": "فصل سائق عن سيارة", "actor": "مقدم الخدمة", "preconditions": ["وجود حساب لمقدم الخدمة على النظام", "وجود سائق مسجل ومربوط بسيارة ضمن النظام"], "postconditions": ["فصل السائق عن السيارة المحددة", "تحديث الحالة في النظام لإظهار أن السيارة غير مرتبطة بسائق"], "main_flow_steps": ["مقدم الخدمة يدخل إلى صفحة إدارة السائقين.", "مقدم الخدمة يبحث عن السائق المطلوب.", "مقدم الخدمة يقوم بفصل السائق عن السيارة.", "النظام يحدّث حالة السيارة لإظهار أنها غير مرتبطة بسائق."]}]}, "4.2.8": {"title": "إضافة معلومات محطات الوقود", "business_goals": ["تزويد المستخدمين بأماكن محطات الوقود المحلية وتقديم معلومات مفصلة عنها لتعزيز التجربة الكلية للمستخدم وتوفير خدمات الدعم التقني."], "stakeholders": ["مقدمي الخدمات المتخصصة (أصحاب محطات الوقود)"], "main_steps": ["يدخل مالك المحطة إلى صفحة إدارة المحطات.", "يختار إضافة محطة جديدة.", "يدخل معلومات المحطة (اسم الفرع، العنوان).", "يحد<PERSON> الإحداثيات على الخريطة."], "alternative_steps": ["يستطيع مالك المحطة تعديل معلومات المحطة أو تحديث موقعها على الخريطة في أي وقت."], "user_stories": ["مالك المحطة: يرغب في إضافة فرع جديد، فيقوم بالدخول إلى صفحة إدارة المحطات، يضغط على 'إضافة محطة جديدة'، يقوم بتقديم معلومات الفرع وموقعه على الخريطة.", "مالك المحطة: يرغب في تعديل معلومات فرع، فيقوم بالدخول إلى صفحة إدارة المحطات، يبحث عن الفرع المطلوب، يقوم بتعديل المعلومات المطلوبة.", "مالك المحطة: في حالة تغير موقع الفرع، يقوم بدخول صفحة إدارة المحطات، يبحث عن الفرع، تعديل موقعه على الخريطة."], "performance_indicators": ["عدد المحطات المسجلة حسب المواقع (مدن أو مناطق أو مسارات طريق)."], "use_cases": [{"id": "4.2.8-1", "description": "إضافة فرع جديد لمحطة الوقود", "actor": "مالك محطة الوقود", "preconditions": ["وجود حساب مسجل لمالك المحطة في المنصة"], "postconditions": ["إضافة محطة جديدة إلى قاعدة بيانات المحطات"], "main_flow_steps": ["يدخل مالك المحطة إلى صفحة إدارة المحطات", "يختار إضافة محطة جديدة", "يدخل معلومات المحطة (اسم الفرع، العنوان)", "يح<PERSON><PERSON> الإحداثيات على الخريطة", "يؤكد إضافة المحطة"]}, {"id": "4.2.8-2", "description": "تعديل معلومات فرع موجود لمحطة الوقود", "actor": "مالك محطة الوقود", "preconditions": ["وجود حساب مسجل لمالك المحطة في المنصة", "وجود فرع مسجل في النظام"], "postconditions": ["تحديث معلومات الفرع في قاعدة بيانات المحطات"], "main_flow_steps": ["يدخل مالك المحطة إلى صفحة إدارة المحطات", "يب<PERSON><PERSON> عن الفرع المطلوب", "يقوم بتعديل المعلومات المطلوبة", "يؤكد التعديلات"]}, {"id": "4.2.8-3", "description": "تعديل موقع فرع لمحطة الوقود على الخريطة", "actor": "مالك محطة الوقود", "preconditions": ["وجود حساب مسجل لمالك المحطة في المنصة", "وجود فرع مسجل في النظام"], "postconditions": ["تحديث موقع الفرع في قاعدة بيانات المحطات"], "main_flow_steps": ["يدخل مالك المحطة إلى صفحة إدارة المحطات", "يب<PERSON><PERSON> عن الفرع المطلوب", "يقوم بتعديل موقع الفرع على الخريطة", "يؤكد التعديلات"]}]}, "4.2.9": {"title": "طل<PERSON> خدمة النقل", "business_goals": ["تمكين العملاء من تقديم طلبات خدمة النقل مع تحديد تفاصيلها."], "stakeholders": ["طال<PERSON> خدمة النقل (التاجر، وسيط النقل، الفرد)"], "main_steps": ["تحديد نوع العميل: مرسل/مرسل إليه.", "اختيار فئة المركبة المطلوبة (نقل خفيف، نقل ثقيل).", "اختيار نوع المركبة المطلوبة حسب الفئة المحددة (قائمة منسدلة لكل فئة) - اختيار متعدد.", "وصف الحمل والوزن الإجمالي.", "تحديد قيمة الحمولة.", "تحديد موقع التحميل والتنزيل (موقع واحد أو مواقع متعددة).", "تحديد موعد التحميل (الآن/مجدول بحد أقصى ٢٠ ساعة).", "تحديد نطاق البحث (١٠كم، ٥٠كم، ١٠٠كم، ٢٠٠كم).", "اختيار آلية التسعير (طلب تسعيرة {مزايدة عامة، مزايدة خاصة}/ سعر ثابت)."], "alternative_steps": ["الخدمة غير متاحة في حال لم يكن هناك مقدمي الخدمة المتاحين وفقا للمعايير المحددة."], "user_stories": ["كطالب خدمة، أريد أن أحدد دوري في العملية (مرسل/مستلم)، لكي يتم تحديد اتجاه الحمولة ومسئوليتي فيها.", "كطالب خدمة، أريد اختيار فئة المركبة المطلوبة، لكي يتم البحث عن مقدمي الخدمة المتوافقين مع احتياجاتي.", "كطالب خدمة، أريد اختيار نوع المركبة المطلوبة بناءً على الفئة التي اخترتها، لكي يتم البحث عن مقدمي الخدمة المناسبين.", "كطالب خدمة، أريد تحديد الوزن الكلي للحمولة، لكي يتم تقدير التكلفة الكلية للخدمة.", "كطالب خدمة، أريد تحديد مواقع تحميل وتنزيل الحمولة، لكي يتم توجيه مقدم الخدمة بشكل صحيح.", "كطالب خدمة، أريد تحديد موعد التحميل (الآن / مجدول)، لضمان توفر مقدم الخدمة في الوقت المطلوب.", "كطالب خدمة، أريد أن أحدد نطاق البحث، للحصول على قائمة من مقدمي الخدمات المتاحين في المنطقة المطلوبة.", "كطالب خدمة، أريد اختيار آلية التسعير (طلب تسعير / سعر ثابت)، لكي أتمكن من التحكم في تكلفة الخدمة.", "كمقدم خدمة، أريد أن أرى تفاصيل طلبات النقل المطابقة لمعايير خاصة بي، لكي أتمكن من تقديم عروض أسعار مناسبة."], "performance_indicators": ["عدد الطلبات مع تصنيفها (نوع وفئة المركبة المستخدمة، مدينة الارسال والاستلام،....)."], "use_cases": [{"id": "4.2.9-1", "description": "تحديد نوع العميل (مرسل/مرسل إليه) عند طلب خدمة النقل", "actor": "طال<PERSON> خدمة النقل (التاجر، وسيط النقل، الفرد)", "preconditions": ["وجود حساب مستخدم مسجل في المنصة", "المستخدم قد قرأ ووافق على شروط الخدمة"], "postconditions": ["تحديد دور المستخدم في العملية وتوجيه الطلب بناءً على ذلك"], "main_flow_steps": ["المستخدم يدخل إلى صفحة طلب خدمة النقل", "المستخدم يحدد نوعه كمرسل أو مرسل إليه", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-2", "description": "اختيار فئة المركبة المطلوبة (نقل خفيف، نقل ثقيل)", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["تحديد نوع العميل"], "postconditions": ["تحديد نوع المركبة المناسبة للخدمة المطلوبة"], "main_flow_steps": ["المستخدم يختار فئة المركبة المطلوبة من قائمة الفئات المتاحة", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-3", "description": "اختيار نوع المركبة المطلوبة بناءً على الفئة المحددة", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["اختيار فئة المركبة"], "postconditions": ["تحديد النوع المناسب للمركبة المطلوبة"], "main_flow_steps": ["المستخدم يختار نوع المركبة من القائمة المنسدلة حسب الفئة المختارة", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-4", "description": "وصف الحمل والوزن الإجمالي", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["اختيار نوع المركبة المطلوبة"], "postconditions": ["تحديد تفاصيل الحمولة لتقديم الطلب بدقة"], "main_flow_steps": ["المستخدم يدخل وصفًا للحمولة", "المستخد<PERSON> يحدد الوزن الإجمالي للحمولة", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-5", "description": "تحديد قيمة الحمولة", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["وصف الحمل والوزن الإجمالي"], "postconditions": ["تحديد قيمة الحمولة للمساعدة في حساب التكلفة"], "main_flow_steps": ["المستخدم يحدد قيمة الحمولة", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-6", "description": "تحديد موقع التحميل والتنزيل (موقع واحد أو مواقع متعددة)", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["تحديد قيمة الحمولة"], "postconditions": ["تحديد موقع التحميل والتنزيل بدقة لتوجيه مقدم الخدمة"], "main_flow_steps": ["المستخدم يحدد موقع التحميل", "المستخدم يحدد موقع التنزيل", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-7", "description": "تحديد موعد التحميل (الآن/مجدول بحد أقصى ٢٠ ساعة)", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["تحديد موقع التحميل والتنزيل"], "postconditions": ["تحديد موعد التحميل لتنسيق الخدمة"], "main_flow_steps": ["المستخدم يحد<PERSON> موعد التحميل (الآن أو مجدول)", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-8", "description": "تحديد نطاق البحث (١٠كم، ٥٠كم، ١٠٠كم، ٢٠٠كم)", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["تحديد موعد التحميل"], "postconditions": ["تحديد نطاق البحث للعثور على مقدمي الخدمة المتاحين"], "main_flow_steps": ["المستخدم يحدد نطاق البحث المناسب", "المستخدم ينقر على زر 'التالي' لاستكمال الطلب"]}, {"id": "4.2.9-9", "description": "اختيار آلية التسعير (طلب تسعيرة {مزايدة عامة، مزايدة خاصة}/ سعر ثابت)", "actor": "طا<PERSON><PERSON> خدمة النقل", "preconditions": ["تحديد نطاق البحث"], "postconditions": ["تحديد آلية التسعير للمساعدة في اختيار مقدم الخدمة"], "main_flow_steps": ["المستخدم يختار آلية التسعير المناسبة", "المستخدم ينقر على زر 'إرسال الطلب' لاستكمال الطلب"]}]}, "4.2.10": {"title": "تسعير الحمولة", "business_goals": ["تقنين وتنظيم عملية تسعير الحمولة وتوفير الشفافية."], "stakeholders": ["العميل", "مقدمي الخدمات", "إداريي المنصة"], "main_steps": ["العميل يختار بين نوع التسعير الثابت أو تلقي عروض الأسعار.", "في حالة التسعير الثابت، يتم إبلاغ العميل بالقيمة الأساسية لأجرة النقل، ومن ثم إضافة الرسوم الإضافية لتأكيد المبلغ الإجمالي.", "في حالة طلب عروض الأسعار، يتم عرض طلب النقل للمقدمين لتقديم عروضهم، سواء بشكل عام أو خاص."], "alternative_steps": ["النظام يسمح برؤية العروض في حالة المزايدة العامة من قبل جميع مقدمي الخدمات.", "العميل يمكنه ترتيب وفلترة العروض حسب السعر، التقييم، ومعايير أخرى.", "يجب تحديد آلية واضحة لعرض ومشاركة أسعار المزايدات؛ في العروض العامة، يمكن لجميع مقدمي الخدمة رؤية العروض، بينما في العروض الخاصة، تظهر التفاصيل فقط لطالب الخدمة."], "user_stories": ["العميل، يريد تحديد سعر ثابت للنقل، للتحكم الكامل في تكلفة الخدمة.", "العميل، يريد استقبال عروض الأسعار للحصول على أفضل صفقة ممكنة.", "مقدم الخدمة، يريد مشاهدة طلبات النقل لتقديم عرض تنافسي.", "الأدمن، يريد تحديد حدود للتسعير للحفاظ على استقرار وعدالة السوق."], "performance_indicators": ["عد<PERSON> الطلبات حسب تصنيف آلية التسعير (ثابت، عرض سعر)، مزايدة عامة أو خاصة وربطها بالفترات الزمنية.", "نسبة المستجيبين للطلبات إلى المستلمين."], "use_cases": [{"id": "4.2.10-1", "description": "اختيار العميل بين نوع التسعير الثابت أو تلقي عروض الأسعار.", "actor": "العميل", "preconditions": ["وجود خدمة النقل المطلوبة.", "توافر معلومات الحمولة."], "postconditions": ["تحديد نوع التسعير المطلوب."], "main_flow_steps": ["العميل يدخل إلى صفحة تسعير الحمولة.", "العميل يختار بين التسعير الثابت أو تلقي عروض الأسعار."]}, {"id": "4.2.10-2", "description": "إبلاغ العميل بالقيمة الأساسية لأجرة النقل في حالة التسعير الثابت.", "actor": "النظام", "preconditions": ["اختيار العميل للتسعير الثابت."], "postconditions": ["إبلاغ العميل بالقيمة الأساسية لأجرة النقل."], "main_flow_steps": ["النظام يحسب القيمة الأساسية لأجرة النقل.", "النظام يظهر القيمة الأساسية لأجرة النقل للعميل."]}, {"id": "4.2.10-3", "description": "إضافة الرسوم الإضافية لتأكيد المبلغ الإجمالي في حالة التسعير الثابت.", "actor": "النظام", "preconditions": ["إبلاغ العميل بالقيمة الأساسية لأجرة النقل."], "postconditions": ["إظهار المبلغ الإجمالي للعميل."], "main_flow_steps": ["النظام يحسب الرسوم الإضافية.", "النظام يظهر المبلغ الإجمالي للعميل."]}, {"id": "4.2.10-4", "description": "عرض طلب النقل للمقدمين لتقديم عروضهم في حالة طلب عروض الأسعار.", "actor": "النظام", "preconditions": ["اختيار العميل لطلب عروض الأسعار."], "postconditions": ["عرض الطلب على مقدمي الخدمات."], "main_flow_steps": ["النظام يعرض طلب النقل على جميع مقدمي الخدمات.", "مقدمي الخدمات يقدمون عروضهم على الطلب."]}, {"id": "4.2.10-5", "description": "رؤية العروض في حالة المزايدة العامة من قبل جميع مقدمي الخدمات.", "actor": "مقدمي الخدمات", "preconditions": ["عرض الطلب للمزايدة العامة."], "postconditions": ["رؤية العروض من قبل جميع مقدمي الخدمات."], "main_flow_steps": ["النظام يعرض جميع العروض المقدمة في المزايدة العامة."]}, {"id": "4.2.10-6", "description": "ترتيب وفلترة العروض حسب السعر، التقييم، ومعايير أخرى.", "actor": "العميل", "preconditions": ["تلقي العروض من مقدمي الخدمات."], "postconditions": ["العروض مرتبة ومفلترة حسب معايير العميل."], "main_flow_steps": ["العميل يقوم بترتيب وفلترة العروض المقدمة.", "النظام يظهر العروض مرتبة ومفلترة حسب طلب العميل."]}, {"id": "4.2.10-7", "description": "تحديد آلية عرض ومشاركة أسعار المزايدات.", "actor": "النظام", "preconditions": ["عرض العروض من مقدمي الخدمات."], "postconditions": ["العروض منظمة حسب نوع المزايدة (عامة أو خاصة)."], "main_flow_steps": ["النظام يعرض أسعار المزايدات بشكل واضح.", "النظام يحدد من يمكنه رؤية العروض حسب نوع المزايدة."]}]}, "4.2.11": {"title": "إتمام الصفقة", "business_goals": ["<PERSON><PERSON><PERSON> أن يتم دفع أتعاب الناقل مقابل خدمات النقل.", "توفير آلية لحجز واحتجاز الأموال حتى يتم إتمام الخدمة بالكامل.", "تعزيز الثقة بين الأطراف عن طريق ضمان الدفع.", "تمكين العميل من الغاء طلب الحمولة بأقل خسائر للناقل."], "stakeholders": ["طالب الخدمة", "الناقل", "المنصة", "خدمة الدفع"], "main_steps": ["يتم التحقق من أن لدى طالب الخدمة مبلغ كافٍ في محفظته على المنصة.", "إرسال رسالة برمز تأكيد لإتمام عملية الحجز.", "يتم حجز مبلغ المعاملة وتجميده في حساب طالب الخدمة.", "في حالة عدم توافر الرصيد الكافي، يجب إرسال رسالة تحتوي على رمز تأكيد لإكمال عملية إضافة الأموال.", "يتم توجيه طالب الخدمة لإضافة الأموال إلى محفظته من خلال واحدة من وسائل الدفع.", "يبقى المبلغ محجوزًا حتى يتم إتمام عملية النقل."], "alternative_steps": ["في حال إلغاء طالب الخدمة لعملية النقل، يتم تحديد قيمة الاسترداد بناءً على مدى قرب الناقل من الموقع وما إذا كان قد بدأ في الرحلة وعدة عوامل أخرى تحددها الادارة."], "user_stories": ["طالب الخدمة: يريد تأكيد الصفقة، ليتيح للناقل بدأ عمله.", "الناقل: يري<PERSON> التأكد من اتمام العميل للصفقة حتى يبدأ عمله بدون تأخير.", "طالب الخدمة: يريد إمكانية إلغاء طلب النقل، حتى يظهر في امان اذا تغيرت الظروف."], "performance_indicators": ["عد<PERSON> الطلبات التي تم إتمام الصفقة لها و نسبتها لإجمالي الطلبات خلال فترات محددة.", "عد<PERSON> الطلبات التي لم يكن لها رصيد في المحفظة وتم إضافة الأموال لحظة الإتمام.", "عدد الطلبات التي لم يتم إتمامها بسبب عدم توفر مبلغ كافي في المحفظة.", "قيمة المبالغ المحجوزة."], "use_cases": [{"id": "4.2.11-1", "description": "التحقق من أن لدى طالب الخدمة مبلغ كافٍ في محفظته على المنصة.", "actor": "طالب الخدمة", "preconditions": ["طالب الخدمة قد أضاف مبلغًا في محفظته من قبل."], "postconditions": ["تأكيد وجود المبلغ الكافي في المحفظة."], "main_flow_steps": ["النظام يتحقق من رصيد المحفظة لطالب الخدمة.", "النظام يظهر رسالة تأكيد بوجود المبلغ الكافي في المحفظة."]}, {"id": "4.2.11-2", "description": "إرسال رسالة برمز تأكيد لإتمام عملية الحجز.", "actor": "النظام", "preconditions": ["طالب الخدمة لديه مبلغ كافٍ في محفظته."], "postconditions": ["استلام طالب الخدمة لرسالة تحتوي على رمز التأكيد."], "main_flow_steps": ["النظام يرسل رسالة نصية إلى طالب الخدمة تحتوي على رمز التأكيد."]}, {"id": "4.2.11-3", "description": "حجز مبلغ المعاملة وتجميده في حساب طالب الخدمة.", "actor": "النظام", "preconditions": ["طالب الخدمة قد أدخل رمز التأكيد بشكل صحيح."], "postconditions": ["المبلغ محجوز ومجمد في حساب طالب الخدمة."], "main_flow_steps": ["النظام يتحقق من رمز التأكيد المدخل من قبل طالب الخدمة.", "النظام يقوم بحجز المبلغ المطلوب وتجميده في حساب طالب الخدمة."]}, {"id": "4.2.11-4", "description": "إرسال رسالة تحتوي على رمز تأكيد لإكمال عملية إضافة الأموال في حالة عدم توافر الرصيد الكافي.", "actor": "النظام", "preconditions": ["رصيد طالب الخدمة في المحفظة غير كافٍ لإتمام الحجز."], "postconditions": ["استلام طالب الخدمة لرسالة تحتوي على رمز تأكيد لإضافة الأموال."], "main_flow_steps": ["النظام يتحقق من رصيد المحفظة لطالب الخدمة.", "النظام يرسل رسالة نصية إلى طالب الخدمة تحتوي على رمز تأكيد لإكمال عملية إضافة الأموال."]}, {"id": "4.2.11-5", "description": "إضافة الأموال إلى محفظة طالب الخدمة من خلال واحدة من وسائل الدفع.", "actor": "طالب الخدمة", "preconditions": ["استلام طالب الخدمة لرسالة تحتوي على رمز تأكيد لإضافة الأموال."], "postconditions": ["إضافة المبلغ المطلوب إلى محفظة طالب الخدمة."], "main_flow_steps": ["طالب الخدمة يدخل إلى صفحة إضافة الأموال في التطبيق.", "طالب الخدمة يختار وسيلة الدفع المناسبة ويدخل تفاصيلها.", "طالب الخدمة يدخل رمز التأكيد لتأكيد العملية.", "النظام يضيف المب<PERSON>غ المطلوب إلى محفظة طالب الخدمة."]}, {"id": "4.2.11-6", "description": "بقاء المبلغ محجوزًا حتى يتم إتمام عملية النقل.", "actor": "النظام", "preconditions": ["نجاح عملية حجز المبلغ في المحفظة."], "postconditions": ["المبلغ يبقى محجوزًا في المحفظة حتى يتم إتمام عملية النقل."], "main_flow_steps": ["النظام يحفظ حالة المبلغ المحجوز ويضمن عدم إتاحته للاستخدام حتى يتم إتمام عملية النقل."]}, {"id": "4.2.11-7", "description": "تحديد قيمة الاسترداد في حالة إلغاء طالب الخدمة لعملية النقل.", "actor": "النظام", "preconditions": ["إلغاء طالب الخدمة لعملية النقل."], "postconditions": ["حساب قيمة الاسترداد بناءً على معايير محددة."], "main_flow_steps": ["النظام يتحقق من حالة الإلغاء.", "النظام يحسب قيمة الاسترداد بناءً على مدى قرب الناقل من الموقع وما إذا كان قد بدأ في الرحلة.", "النظام يرسل رسالة تحتوي على تفاصيل قيمة الاسترداد لطالب الخدمة."]}]}, "4.2.12": {"title": "إدخال بيانات الحمولة من العميل", "business_goals": ["تحقيق تجربة يسيرة ومرنة للعميل أثناء تقديم فائدة متعددة الجوانب بتحديد دقيق لتفاصيل الحمولة بما في ذلك الأصناف، الوزن، القيمة، وظروف النقل.", "تعزيز الشفافية في عملية النقل من خلال السماح للمستلم بتأكيد معرفته باستلام الحمولة.", "تعزيز حماية البضائع من خلال التدقيق الشامل والحصر الدقيق لها."], "stakeholders": ["العميل", "المرسل إليه (المستلم)"], "main_steps": ["العميل يدخل عدد الأصناف للحمولة.", "العميل يدخل البيانات التالية لكل صنف: الوصف، العدد، الوزن/لكل حبة أو وزن الصنف الإجمالي، القيمة لكل حبة أو القيمة الإجمالية للصنف.", "العميل يدخل اشتراطات النقل (ظروف التخزين والعناية المطلوبة من الناقل).", "إذا كان التحميل أو التنزيل لأكثر من موقع، العميل يوضح ذلك لكل صنف على حدة مع توضيح المُرسل إليه (المستلم) لكل صنف.", "يتم إرسال رسالة نصية للمستلم / للمستلمين تحتوي على رابط لتأكيد معرفته بالحمولة المتجهة إليه والتأكيد عليها من خلال التطبيق اذا كان مسجلا في التطبيق."], "alternative_steps": ["في حال عدم تسجيل المستلم في المنصة، يُمكن إجراء التأكيد عن طريق إرسال رمز مؤقت إلى هاتفه المحمول لضمان تعريفه. أما إذا كان المستلم مُسجلاً بالفعل في التطبيق، فيتم تنفيذ عملية التسليم باستخدام رمز QR الموفر من المنصة نفسها."], "user_stories": ["العميل، بعد إتمام الدفع، أريد أن أتمكن من توصيف الحمولة، حتي اضمن حقوقي المالية.", "العميل، أريد أن أتمكن من إضافة مستلمين الحمولة ومعلوماتهم، حتي يسهل علي الناقل توصيل الحمولة بدون مشاكل.", "المستلم، أود استلام رسالة تضم التفاصيل الشاملة للشحنة الموجهة إلى عنواني، تشمل الوزن، القيمة، وموقع التحميل، لتمكيني من تأكيد معرفتي بهذه المعلومات والاستعداد المناسب.", "العميل، في حالة النقل إلى مواقع متعددة، أريد أن أتمكن من منظمة الحمولة بشكل معكوس لتأكيد استلام كل أصناف الحمولة في الموقع المناسب."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المرسلة للمستلمين غير مسجلين في التطبيق."], "use_cases": [{"id": "4.2.12-1", "description": "العميل يدخل عدد الأصناف للحمولة.", "actor": "العميل", "preconditions": ["تواجد الحمولة المراد نقلها", "إمكانية دخول العميل على المنصة"], "postconditions": ["النظام يسجل عدد الأصناف المدخلة"], "main_flow_steps": ["العميل يدخل إلى منصة النظام.", "العميل يقوم بإدخال عدد الأصناف للحمولة."]}, {"id": "4.2.12-2", "description": "العميل يدخل البيانات التالية لكل صنف: الوصف، العدد، الوزن/لكل حبة أو وزن الصنف الإجمالي، القيمة لكل حبة أو القيمة الإجمالية للصنف.", "actor": "العميل", "preconditions": ["إدخال العميل لعدد الأصناف"], "postconditions": ["النظام يسجل تفاصيل الأصناف"], "main_flow_steps": ["العميل يدخل تفاصيل كل صنف في الحمولة.", "العميل يقوم بإدخال الوصف، العدد، الوزن والقيمة لكل صنف."]}, {"id": "4.2.12-3", "description": "العميل يدخل اشتراطات النقل (ظروف التخزين والعناية المطلوبة من الناقل).", "actor": "العميل", "preconditions": ["إدخال تفاصيل الأصناف"], "postconditions": ["النظام يسجل اشتراطات النقل"], "main_flow_steps": ["العميل يدخل إلى قسم اشتراطات النقل في المنصة.", "العميل يقوم بإدخال ظروف التخزين والعناية المطلوبة لكل صنف."]}, {"id": "4.2.12-4", "description": "إذا كان التحميل أو التنزيل لأكثر من موقع، العميل يوضح ذلك لكل صنف على حدة مع توضيح المُرسل إليه (المستلم) لكل صنف.", "actor": "العميل", "preconditions": ["إدخال العميل لتفاصيل الأصناف واشتراطات النقل"], "postconditions": ["النظام يسجل مواقع التحميل والتنزيل", "النظام يسجل معلومات المستلم لكل صنف"], "main_flow_steps": ["العميل يدخل إلى قسم التحميل والتنزيل في المنصة.", "العميل يوضح إذا كان التحميل أو التنزيل لأكثر من موقع.", "العميل يقوم بتوضيح المستلم لكل صنف."]}, {"id": "4.2.12-5", "description": "يتم إرسال رسالة نصية للمستلم / للمستلمين تحتوي على رابط لتأكيد معرفته بالحمولة المتجهة إليه والتأكيد عليها من خلال التطبيق اذا كان مسجلا في التطبيق.", "actor": "النظام", "preconditions": ["النظام يسجل جميع تفاصيل الحمولة ومعلومات المستلمين"], "postconditions": ["المستلم يتلقى رسالة نصية تحتوي على رابط لتأكيد الحمولة"], "main_flow_steps": ["النظام يرسل رسالة نصية للمستلم تحتوي على رابط لتأكيد معرفته بالحمولة.", "المستلم يضغط على الرابط ويؤكد معرفته بالحمولة عبر التطبيق."]}]}, "4.2.13": {"title": "قبول الناقل للحمولة", "business_goals": ["إتاحة الفرصة للناقلين لقبول أو رفض النقلات المقترحة، وتأكيد الربط بين العميل والناقل في حالة القبول."], "stakeholders": ["العميل", "الناقل"], "main_steps": ["العميل يؤكد معلومات الحمولة.", "النظام يرسل QR للعميل.", "الناقل يمسح QR لكي يوافق على نقل الحمولة.", "النظام يقوم بربط العميل والناقل سوياً."], "alternative_steps": [], "user_stories": ["الناقل، بمجرد تلقي إشعار من النظام، أرغب في القدرة على الموافقة على الحمولة التي تم اسنادها لي.", "العميل، بعد تأكيد معلومات الحمولة، أرغب في العلم بأن المهمة قد تم توجيهها إلى الناقل وأن الناقل قد وافق عليها."], "performance_indicators": [], "use_cases": [{"id": "4.2.13-1", "description": "العميل يؤكد معلومات الحمولة.", "actor": "العميل", "preconditions": ["وجود الحمولة المراد نقلها", "اكتمال بيانات الحمولة"], "postconditions": ["النظام يرسل QR للعميل"], "main_flow_steps": ["العميل يدخل على منصة النظام.", "العميل يقوم بمراجعة وتأكيد معلومات الحمولة."]}, {"id": "4.2.13-2", "description": "النظام يرسل QR للعميل.", "actor": "النظام", "preconditions": ["تأكيد العميل لمعلومات الحمولة"], "postconditions": ["العميل يستلم رمز QR", "الناقل يستلم إشعار النقل"], "main_flow_steps": ["النظام ينشئ رمز QR فريد لمعلومات الحمولة المؤكدة.", "النظام يرسل رمز QR للعميل."]}, {"id": "4.2.13-3", "description": "الناقل يمسح QR لكي يوافق على نقل الحمولة.", "actor": "الناقل", "preconditions": ["استلام الناقل لإشعار النقل", "توافر رمز QR لدى العميل"], "postconditions": ["النظام يقوم بربط العميل والناقل سوياً"], "main_flow_steps": ["الناقل يستلم إشعار النقل مع رمز QR من العميل.", "الناقل يقوم بمسح رمز QR للموافقة على نقل الحمولة."]}, {"id": "4.2.13-4", "description": "النظام يقوم بربط العميل والناقل سوياً.", "actor": "النظام", "preconditions": ["موافقة الناقل على نقل الحمولة عبر مسح رمز QR"], "postconditions": ["تأكيد الربط بين العميل والناقل"], "main_flow_steps": ["النظام يتلقى تأكيد الموافقة من الناقل عبر مسح رمز QR.", "النظام يربط معلومات الحمولة بالعميل والناقل سوياً.", "النظام يرسل تأكيد الربط لكل من العميل والناقل."]}]}, "4.2.14": {"title": "جرد الناقل للحمولة", "business_goals": ["تأكيد استلام الناقل للحمولة المطلوب نقلها وإثبات هذا من خلال التقاط صور والايصالات الالكترونية."], "stakeholders": ["الناقل", "المستلم", "إداري النظام"], "main_steps": ["يقوم الناقل بمعاينة بيان الحمولة واختيار الأصناف التي يستلمها في شاحنته، إما كل صنف على حدة أو جميعها معًا.", "عند انتهاء استلام كافة الأصناف في بيان الحمولة، يقر الناقل باستلام الأصناف حسب وصفاتها وأعدادها في بيان الحمولة وأنها سليمة.", "يلتقط الناقل ثلاث صور للحمل (ثلث الحمولة، ثلثي الحمولة، كامل الحمولة) من خلال التطبيق.", "يرفق الناقل نسخة إلكترونية من المستندات الخاصة بالحمولة ويؤكد تسليم الأصول للمستلم."], "alternative_steps": ["في حالة وجود أية ملاحظات على الأصناف، يمكن للناقل اختيار خيار استلام الأصناف (تم الاستلام بدون ملاحظات/تم الاستلام مع ملاحظات)، حيث يجب كتابة الملاحظات المرصودة."], "user_stories": ["الناقل: أرغب في مراجعة بيان الحمولة واختيار الأصناف التي يمكنني استلامها، لضمان أنني قمت بتحميل جميع الأصناف المطلوبة.", "الناقل: أرغب في التقاط صور للشحنة ورفعها عبر التطبيق، للتحقق من استلامي للأصناف بالتطابق مع المواصفات المذكورة في بيان الحمولة.", "الناقل: أريد رفع نسخة إلكترونية من المستندات الخاصة بالحمولة، للتأكيد على استلامي للأصناف.", "الإداري: أو<PERSON> أن أمتلك القدرة على تفعيل وتعطيل عملية الجرد بأكملها أو جزء منها حسب رغبتي، بهدف مراقبة وتنظيم العملية بطريقة أكثر فاعلية."], "performance_indicators": ["المدة الزمنية المحددة لتحميل وقبول استلام الشحنة."], "use_cases": [{"id": "4.2.14-1", "description": "معاينة الناقل لبيان الحمولة واختيار الأصناف التي يستلمها.", "actor": "الناقل", "preconditions": ["وجود بيان الحمولة."], "postconditions": ["تحديد الأصناف المستلمة في الشاحنة."], "main_flow_steps": ["الناقل يعاين بيان الحمولة.", "الناقل يختار الأصناف التي يستلمها.", "الناقل يقوم بتأكيد استلام الأصناف."]}, {"id": "4.2.14-2", "description": "التقاط صور الحمولة بواسطة الناقل.", "actor": "الناقل", "preconditions": ["استلام الأصناف في الشاحنة."], "postconditions": ["وجود صور الحمولة المرفوعة على النظام."], "main_flow_steps": ["الناقل يلتقط ثلاث صور للحمولة (ثلث، ثلثين، كامل الحمولة).", "الناقل يرفع الصور عبر التطبيق."]}, {"id": "4.2.14-3", "description": "إرفاق المستندات الإلكترونية للحمولة بواسطة الناقل.", "actor": "الناقل", "preconditions": ["وجود المستندات الخاصة بالحمولة."], "postconditions": ["المستندات مرفوعة على النظام."], "main_flow_steps": ["الناقل يرفق نسخة إلكترونية من المستندات.", "الناقل يؤكد تسليم الأصول للمستلم."]}, {"id": "4.2.14-4", "description": "استلام الأصناف بوجود ملاحظات.", "actor": "الناقل", "preconditions": ["وجود ملاحظات على الأصناف."], "postconditions": ["الملاحظات مسجلة في النظام."], "main_flow_steps": ["الناقل يختار خيار 'تم الاستلام مع ملاحظات'.", "الناقل يكتب الملاحظات المرصودة.", "النظام يسجل الملاحظات."]}]}, "4.2.15": {"title": "متابعة الرحلة", "business_goals": ["اتاحة الامكانية للأطراف المعنية بمتابعة الحمولة في الوقت الحقيقي."], "stakeholders": ["العميل (مرسل، مستلم)", "شركة النقل"], "main_steps": ["بدء الرحلة عند تأكيد استلام الحمولة.", "يحسب النظام الوقت المتوقع للوصول إلى الوجهة/الوجهات المقصودة بناءً على العوامل التالية: السرعة المتوسطة (65كم/ساعة)، العدد المتوقع لساعات التوقف خلال الرحلة، عدد ساعات الراحة المتوقعة للشاحن خلال الرحلة.", "يتيح النظام تتبع الموقع الحالي للشاحن على الخريطة.", "مراقبة البيانات الواردة من أجهزة الاستشعار إذا كانت متوفرة.", "مراقبة حالة العقبات المتوقعة في مسار الرحلة."], "alternative_steps": [], "user_stories": ["كمشرف، أريد أن أعرف الموقع الحالي للحمولة، حتى أتمكن من توفير تحديثات سريعة للعميل.", "كمشرف، أريد أن أتابع بيانات الاستشعار، بما في ذلك الحرارة والصور، حتى أتأكد من سلامة الحمولة.", "كمشرف، أريد أن أعرف حالة الطرق المقبلة، حتى أتمكن من إبلاغ الشاحن بالتحديثات المطلوبة.", "كعميل، أريد أن أتابع الحمولة في الوقت الحقيقي، حتى أتمكن من التخطيط بشكل أفضل لوصول الحمولة."], "performance_indicators": ["الزمن المستغرق للرحلة.", "المسافة المقطوعة.", "عدد فترات التوقف لفترات طويلة.", "قيمة أجرة النقل بالنسبة للمسافة والزمن للرحلة (ريال/كم، ريال/ساعة أو يوم)."], "use_cases": [{"id": "4.2.15-1", "description": "بدء الرحلة عند تأكيد استلام الحمولة.", "actor": "الناقل", "preconditions": ["الناقل قد استلم الحمولة."], "postconditions": ["الرحلة تبدأ والنظام يبدأ في متابعة الرحلة."], "main_flow_steps": ["الناقل يستلم الحمولة.", "النظام يؤكد استلام الحمولة.", "النظام يبدأ في متابعة الرحلة."]}, {"id": "4.2.15-2", "description": "حساب الوقت المتوقع للوصول.", "actor": "النظام", "preconditions": ["الرحلة قد بدأت.", "النظام يحتوي على بيانات السرعة المتوسطة والمدة المتوقعة للتوقف."], "postconditions": ["النظام يعرض الوقت المتوقع للوصول."], "main_flow_steps": ["النظام يجمع بيانات السرعة المتوسطة.", "النظام يجمع بيانات التوقف والراحة المتوقعة.", "النظام يحسب الوقت المتوقع للوصول.", "النظام يعرض الوقت المتوقع للوصول."]}, {"id": "4.2.15-3", "description": "تتبع الموقع الحالي للشاحن.", "actor": "النظام", "preconditions": ["الرحلة قد بدأت.", "النظام يحتوي على بيانات الموقع من أجهزة الاستشعار."], "postconditions": ["الموقع الحالي للشاحن يتم عرضه على الخريطة."], "main_flow_steps": ["النظام يجمع بيانات الموقع من أجهزة الاستشعار.", "النظام يعرض الموقع الحالي للشاحن على الخريطة."]}, {"id": "4.2.15-4", "description": "مراقبة البيانات الواردة من أجهزة الاستشعار.", "actor": "النظام", "preconditions": ["الرحلة قد بدأت.", "النظام يحتوي على أجهزة استشعار متصلة بالشاحن."], "postconditions": ["النظام يعرض بيانات الاستشعار."], "main_flow_steps": ["النظام يجمع البيانات من أجهزة الاستشعار.", "النظام يعرض البيانات الواردة من أجهزة الاستشعار."]}, {"id": "4.2.15-5", "description": "مراقبة حالة العقبات المتوقعة في مسار الرحلة.", "actor": "النظام", "preconditions": ["الرحلة قد بدأت.", "النظام يحتوي على بيانات الحالة المرورية والعقبات المتوقعة."], "postconditions": ["النظام يعرض حالة العقبات المتوقعة."], "main_flow_steps": ["النظام يجمع بيانات الحالة المرورية.", "النظام يعرض حالة العقبات المتوقعة على المسار."]}]}, "4.2.16": {"title": "تسليم النقل", "business_goals": ["إتمام عملية نقل بسهوله مع ضمان حقوق الطرفين."], "stakeholders": ["المستلم", "المرسل", "مقدم الخدمة (الناقل)"], "main_steps": ["يتم تسليم الحمولة للمستلم.", "إذا كان المستلم مسجل في التطبيق، يتم التسليم خلال التطبيق بواسطة QR.", "إذا لم يكن المستلم مسجلاً في التطبيق، سيتم إرسال رسالة نصية إلى هاتفه المحمول تتضمن رابطًا لتأكيد استلام الشحنة وبدء عملية الاستلام.", "تتم عملية استلام الشحنة باستخدام آلية جرد تشابه تلك المستخدمة في عملية التسليم بين المرسل والناقل. بمجرد استلام كافة الأصناف وتنزيل الشحنة بالكامل، يُطلب من المستلم التأكيد على استلام الشحنة بالكامل دون أية ملاحظات، أو تسجيل أي ملاحظات متعلقة بالتسليم. هذا الإقرار مشروط بدفع قيمة النقل أو عدمه. في حالة عدم الدفع، يتوجب على المستلم رفع الملاحظات وتوثيقها بالصور مباشرةً عبر التطبيق.", "يُصدر إشعار إلى طالب الخدمة الأساسي، وكذلك إلى الأطراف ذات العلاقة مثل المرسل، يُفيد بأن الشحنة قد تم تسليمها بنجاح.", "في حالة عدم وجود أي ملاحظات تشترط عدم دفع تكلفة النقل، يتم تحويل التكاليف من حساب طالب الخدمة إلى حساب الناقل بعد مرور ساعة من وقت إتمام الإرسال.", "إضافة ارسال إشعار للنظام (المشر<PERSON>) في حال وجود ملاحظات تعيق عملية تحويل التكاليف."], "alternative_steps": [], "user_stories": ["كمستلم، عند استلام الشحنة، أود التأكد من استلام جميع الأصناف وتفريغ الحمولة بالكامل. سأقر بأن التسليم تم بدون أي ملاحظات تعوق الإجراءات، لضمان إتمام المعاملة بسلاسة وحفظ حقوقي.", "كمستلم غير مسجل في التطبيق، أريد أن أستقبل رسالة نصية تحتوي على رابط لتأكيد استلام الحمولة ووضع ملاحظات على التسليم لضمان سلامة القطع والأصناف.", "كطالب خدمة، أريد استلام إشعار بأن الحمولة قد تم تسليمها لكي أكون مطمئن على سير العملية بالشكل المطلوب.", "كمقدم خدمة (ناقل)، أريد تحويل الأجرة من حساب العميل إلى محفظتي أو حسابي البنكي بعد ساعة من تسليم الحمولة، لكي أضمن حصولي على مستحقاتي."], "performance_indicators": ["إحصاء عدد الطلبات التي تم إتمامها بنجاح، بما في ذلك تسليم الشحنة وتصنيفها إلى: مقبولة بدون ملاحظات، مقبولة مع ملاحظات مشروطة بالدفع، أو مقبولة مع ملاحظات بدون شروط الدفع.", "الفترة الزمنية للتنزيل."], "use_cases": [{"id": "4.2.16-1", "description": "تسليم الحمولة للمستلم المسجل في التطبيق باستخدام QR.", "actor": "المستلم", "preconditions": ["إتمام عملية النقل.", "المستلم مسجل في التطبيق."], "postconditions": ["تأكيد استلام الشحنة.", "تحديث حالة الطلب في النظام."], "main_flow_steps": ["الناقل يسلم الحمولة للمستلم.", "المستلم يمسح رمز QR لتأكيد الاستلام.", "النظام يحدث حالة الطلب بناءً على تأكيد المستلم."]}, {"id": "4.2.16-2", "description": "تسليم الحمولة للمستلم غير المسجل في التطبيق باستخدام رسالة نصية.", "actor": "النظام", "preconditions": ["إتمام عملية النقل.", "المستلم غير مسجل في التطبيق."], "postconditions": ["تأكيد استلام الشحنة.", "تحديث حالة الطلب في النظام."], "main_flow_steps": ["النظام يرسل رسالة نصية إلى هاتف المستلم تتضمن رابطًا لتأكيد استلام الشحنة.", "المستلم يفتح الرابط ويؤكد استلام الشحنة.", "النظام يحدث حالة الطلب بناءً على تأكيد المستلم."]}, {"id": "4.2.16-3", "description": "استخدام آلية جرد لاستلام الشحنة.", "actor": "المستلم", "preconditions": ["إتمام عملية النقل.", "استعداد المستلم لاستلام الشحنة."], "postconditions": ["تأكيد استلام كافة الأصناف.", "تحديث حالة الطلب في النظام."], "main_flow_steps": ["الناقل يقدم الأصناف المدرجة في بيان الحمولة.", "المستلم يراجع الأصناف ويقوم بجردها.", "المستلم يؤكد استلام كافة الأصناف بدون ملاحظات أو مع ملاحظات.", "النظام يحدث حالة الطلب بناءً على تأكيد المستلم."]}, {"id": "4.2.16-4", "description": "تحويل التكاليف بعد استلام الشحنة.", "actor": "النظام", "preconditions": ["استلام الشحنة من قبل المستلم.", "عدم وجود ملاحظات تعوق عملية الدفع."], "postconditions": ["تحويل التكاليف من حساب طالب الخدمة إلى حساب الناقل.", "تحديث سجل المعاملات في النظام."], "main_flow_steps": ["النظام يتحقق من استلام الشحنة بنجاح.", "النظام يقوم بتحويل التكاليف من حساب طالب الخدمة إلى حساب الناقل بعد مرور ساعة من وقت إتمام الإرسال."]}]}, "4.2.17": {"title": "تحويل أرباح عملية النقل", "business_goals": ["ضمان دقة وشفافية في توزيع الأرباح بين الجهات المعنية والتطبيق."], "stakeholders": ["الناقل", "التطبيق", "هيئة الزكاة والضريبة والجمارك في المملكة العربية السعودية"], "main_steps": ["يتم تحويل أجرة النقل مباشرة إلى محفظة الناقل الإلكترونية.", "يتم تحويل رسوم الخدمة إلى حساب التطبيق تحت تصنيف 'رسوم خدمات النقل'، مع تفريق بين نقل ثقيل وخفيف، وإضافة تفاصيل إضافية تتعلق بنوع المركبة المستخدمة.", "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق."], "alternative_steps": [], "user_stories": ["الناقل: بعد تنفيذ الخدمة، يتم تحويل أجرة النقل التي دفعها العميل إلى محفظة الناقل الإلكترونية."], "performance_indicators": ["عدد الطلبات التي تم اكتمال تحويل تكاليفها وقيمتها خلال فترات محددة مع تصنيف (أجرة النقل، رسوم الخدمة، نسبة الضريبة).", "عدد الطلبات المعلقة وقيمتها خلال فترات محددة مع تصنيف (أجرة النقل، رسوم الخدمة، نسبة الضريبة)."], "use_cases": [{"id": "4.2.17-1", "description": "تحويل أجرة النقل إلى محفظة الناقل الإلكترونية بعد إتمام الخدمة.", "actor": "النظام", "preconditions": ["إتمام عملية النقل بنجاح.", "توفر معلومات المحفظة الإلكترونية للناقل."], "postconditions": ["تحويل أجرة النقل إلى محفظة الناقل.", "تحديث سجل المعاملات في النظام."], "main_flow_steps": ["النظام يتحقق من إتمام عملية النقل.", "النظام يقوم بتحويل أجرة النقل إلى محفظة الناقل الإلكترونية."]}, {"id": "4.2.17-2", "description": "تحويل رسوم الخدمة إلى حساب التطبيق بعد إتمام النقل.", "actor": "النظام", "preconditions": ["إتمام عملية النقل بنجاح.", "تحديد رسوم الخدمة المطلوبة."], "postconditions": ["تحويل رسوم الخدمة إلى حساب التطبيق.", "تحديث سجل المعاملات في النظام."], "main_flow_steps": ["النظام يتحقق من إتمام عملية النقل.", "النظام يقوم بتحويل رسوم الخدمة إلى حساب التطبيق تحت تصنيف 'رسوم خدمات النقل'."]}, {"id": "4.2.17-3", "description": "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق بعد إتمام النقل.", "actor": "النظام", "preconditions": ["إتمام عملية النقل بنجاح.", "تحديد قيمة ضريبة القيمة المضافة المستحقة."], "postconditions": ["تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق.", "تحديث سجل المعاملات في النظام."], "main_flow_steps": ["النظام يتحقق من إتمام عملية النقل.", "النظام يقوم بتحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق."]}]}, "4.2.18": {"title": "تقييم خدمة النقل", "business_goals": ["نظام التقييم مطلوب لضمان الشفافية وبناء الثقة بين العملاء ومقدمي الخدمات. هذا سيخدم كوسيلة للتحسين المستمر للخدمات التي تقدم من قبل مقدمي الخدمات."], "stakeholders": ["العملاء", "إداري المنصة"], "main_steps": ["عند اكتمال عملية النقل، يتقدم النظام برسالة إلى العميل لتقييم تجربة النقل.", "يتم أرشفة التقييمات وجعلها مرئية للجمهور في صفحة مقدم الخدمة."], "alternative_steps": ["في حالة إغفال العميل للتقييم، يجب تقديم تذكيرات بالتقييم."], "user_stories": ["كعميل، بعد استلام نقلتي، أريد تقييم جودة الخدمة التي تلقيتها، حتى يتمكن الآخرون من الاطلاع على تجربتي.", "كإداري للمنصة، أريد رؤية جميع التقييمات للمعاملات التي تمت على المنصة، حتى أتمكن من مراقبة الجودة والتحسين المستمر للمنصة.", "كعميل آخر، أرغب في رؤية تقييمات العملاء والخدمات الأخرى لمقدم الخدمة، حتى أتمكن من اتخاذ قرار مستنير بشأن التعامل معهم."], "performance_indicators": ["تحديد نسبة التقييم الإجمالية وربط تصنيفها بفئة المركبة المستخدمة.", "عد<PERSON> الطلبات التي تم تقييمها من إجمالي الطلبات."], "use_cases": [{"id": "4.2.18-1", "description": "عند اكتمال عملية النقل، يتقدم النظام برسالة إلى العميل لتقييم تجربة النقل.", "actor": "النظام", "preconditions": ["اكتمال عملية النقل", "العميل مسجل في النظام"], "postconditions": ["استلام العميل لرسالة التقييم"], "main_flow_steps": ["اكتمال عملية النقل", "النظام يرسل رسالة تقييم للعميل", "العميل يتلقى الرسالة"]}, {"id": "4.2.18-2", "description": "يتم أرشفة التقييمات وجعلها مرئية للجمهور في صفحة مقدم الخدمة.", "actor": "النظام", "preconditions": ["العميل قام بتقييم الخدمة", "النظام يستلم التقييم"], "postconditions": ["التقييم مرئي للجمهور في صفحة مقدم الخدمة"], "main_flow_steps": ["النظام يتلقى التقييم", "أرشفة التقييم", "عرض التقييم على صفحة مقدم الخدمة"]}, {"id": "4.2.18-3", "description": "في حالة إغفال العميل للتقييم، يجب تقديم تذكيرات بالتقييم.", "actor": "النظام", "preconditions": ["العميل لم يقم بتقييم الخدمة بعد فترة محددة"], "postconditions": ["استلام العميل لتذكير بتقييم الخدمة"], "main_flow_steps": ["مرور فترة محددة دون تقييم", "النظام يرسل تذكير للعميل", "العميل يتلقى التذكير"]}, {"id": "4.2.18-4", "description": "كعميل، بعد استلام نقلتي، أريد تقييم جودة الخدمة التي تلقيتها، حتى يتمكن الآخرون من الاطلاع على تجربتي.", "actor": "العميل", "preconditions": ["اكتمال عملية النقل", "استلام العميل لرسالة التقييم"], "postconditions": ["تقديم التقييم من قبل العميل", "التقييم مرئي للجمهور"], "main_flow_steps": ["استلام رسالة التقييم", "تقديم التقييم عبر النظام", "حفظ وعرض التقييم"]}, {"id": "4.2.18-5", "description": "كإداري للمنصة، أريد رؤية جميع التقييمات للمعاملات التي تمت على المنصة، حتى أتمكن من مراقبة الجودة والتحسين المستمر للمنصة.", "actor": "إداري المنصة", "preconditions": ["وجود تقييمات محفوظة في النظام"], "postconditions": ["الإداري يستطيع عرض ومراجعة التقييمات"], "main_flow_steps": ["فتح واجهة الإدارة", "عرض التقييمات", "مراجعة التقييمات لتحسين الجودة"]}]}, "4.2.19": {"title": "طلب خدمة دعم فني للناقل", "business_goals": ["توفير خدمات الرحلة بطريقة سهلة وفعالة، تمكن العملاء من طلب الخدمات المطلوبة وتحديد الموقع بطريقة سريعة ودقيقة."], "stakeholders": ["العميل: الذي سيطلب الخدمة ويحدد حالتها.", "مقدم الخدمة: سيكون له القدرة على تقديم الخدمات بناء على حالة ووصف الطلب."], "main_steps": ["الناقل يحدد حالة الخدمة (طارئة أو عادية).", "الناقل يحدد نوع الخدمة (مثل الصيانة).", "الناقل يدخل وصف الخدمة (عبر النص أو التسجيل الصوتي أو الصور أو الكل).", "يتم تحديد الموقع الجغرافي عبر النظام GPS."], "alternative_steps": ["في حالة فقدان الاتصال بالإنترنت، يمكن للمالك توفير معلومات الموقع يدوياً."], "user_stories": ["كعميل، أسعى لتوضيح حالة الخدمة (طارئة أو عادية) المطلوبة، وذلك لمنح مقدمي الخدمة فرصة كاملة لفهم السياق الخاص بطلبي، مما يمكنهم من تقديم الخدمة بأعلى مستوى من الكفاءة والجودة.", "كعميل، أريد تحديد نوع الخدمة، لأضمن أن المزود المناسب للخدمة هو من سيتعامل مع طلبي.", "كعميل، أسعى إلى تقديم وصف دقيق للخدمة المطلوبة، بهدف تمكين مزود الخدمة من فهم احتياجاتي بشكل واضح ومحدد.", "كعميل، أريد أن أكون قادرًا على تحديد موقعي تلقائيًا، لضمان أن مزود الخدمة يعرف بالضبط أين أنا.", "كمقدم خدمة، أريد أن أرى تفاصيل الطلب، بما في ذلك حالة الخدمة والنوع والوصف والموقع، حتى أتمكن من تقديم الخدمة بشكل أكثر فعالية وسرعة."], "performance_indicators": ["عدد الطلبات خلال فترات محددة مع تحديد التصنيفات (صيانة عادية، طارئة،...).", "عدد الورش الفنية التي استلمت الطلب ونسبة الاستجابة."], "use_cases": [{"id": "4.2.19-1", "description": "الناقل يحدد حالة الخدمة (طارئة أو عادية).", "actor": "الناقل", "preconditions": ["الناقل مسجل في النظام", "الناقل لديه مركبة مسجلة"], "postconditions": ["النظام يحتفظ بحالة الخدمة المحددة"], "main_flow_steps": ["فتح تطبيق النظام", "تحديد حالة الخدمة كطارئة أو عادية", "ح<PERSON><PERSON> الإعدادات"]}, {"id": "4.2.19-2", "description": "الناقل يحدد نوع الخدمة (مثل الصيانة).", "actor": "الناقل", "preconditions": ["الناقل مسجل في النظام", "الناقل لديه مركبة مسجلة"], "postconditions": ["النظام يحتفظ بنوع الخدمة المحدد"], "main_flow_steps": ["فتح تطبيق النظام", "تحديد نوع الخدمة المطلوبة", "ح<PERSON><PERSON> الإعدادات"]}, {"id": "4.2.19-3", "description": "الناقل يدخل وصف الخدمة (عبر النص أو التسجيل الصوتي أو الصور أو الكل).", "actor": "الناقل", "preconditions": ["الناقل مسجل في النظام", "الناقل لديه مركبة مسجلة", "الناقل حدد حالة ونوع الخدمة"], "postconditions": ["النظام يحتفظ بوصف الخدمة"], "main_flow_steps": ["فتح تطبيق النظام", "إدخال وصف الخدمة بالنص أو التسجيل الصوتي أو الصور", "<PERSON><PERSON><PERSON> الوصف"]}, {"id": "4.2.19-4", "description": "يتم تحديد الموقع الجغرافي عبر النظام GPS.", "actor": "النظام", "preconditions": ["الناقل قام بإدخال وصف الخدمة", "الجهاز المستخدم يحتوي على نظام GPS مفعل"], "postconditions": ["تحديد الموقع الجغرافي الحالي للناقل"], "main_flow_steps": ["النظام يتصل بنظام GPS", "تحديد الموقع الجغرافي", "حفظ الموقع في النظام"]}, {"id": "4.2.19-5", "description": "في حالة فقدان الاتصال بالإنترنت، يمكن للمالك توفير معلومات الموقع يدوياً.", "actor": "الناقل", "preconditions": ["الناقل قام بإدخال وصف الخدمة", "فقدان الاتصال بالإنترنت"], "postconditions": ["تحديد الموقع الجغرافي يدوياً من قبل الناقل"], "main_flow_steps": ["الناقل يفتح تطبيق النظام", "الناقل يدخل معلومات الموقع الجغرافي يدوياً", "حفظ الموقع في النظام"]}, {"id": "4.2.19-6", "description": "كعميل، أسعى لتوضيح حالة الخدمة (طارئة أو عادية) المطلوبة، وذلك لمنح مقدمي الخدمة فرصة كاملة لفهم السياق الخاص بطلبي، مما يمكنهم من تقديم الخدمة بأعلى مستوى من الكفاءة والجودة.", "actor": "العميل", "preconditions": ["العميل مسجل في النظام", "العميل يطلب خدمة دعم فني"], "postconditions": ["حالة الخدمة محددة في النظام"], "main_flow_steps": ["فتح تطبيق النظام", "تحديد حالة الخدمة المطلوبة", "ح<PERSON><PERSON> الإعدادات"]}, {"id": "4.2.19-7", "description": "كعميل، أريد تحديد نوع الخدمة، لأضمن أن المزود المناسب للخدمة هو من سيتعامل مع طلبي.", "actor": "العميل", "preconditions": ["العميل مسجل في النظام", "العميل يطلب خدمة دعم فني"], "postconditions": ["نوع الخدمة محدد في النظام"], "main_flow_steps": ["فتح تطبيق النظام", "تحديد نوع الخدمة المطلوبة", "ح<PERSON><PERSON> الإعدادات"]}, {"id": "4.2.19-8", "description": "كعميل، أسعى إلى تقديم وصف دقيق للخدمة المطلوبة، بهدف تمكين مزود الخدمة من فهم احتياجاتي بشكل واضح ومحدد.", "actor": "العميل", "preconditions": ["العميل مسجل في النظام", "العميل يطلب خدمة دعم فني"], "postconditions": ["وصف الخدمة محفوظ في النظام"], "main_flow_steps": ["فتح تطبيق النظام", "إدخال وصف الخدمة بالنص أو التسجيل الصوتي أو الصور", "<PERSON><PERSON><PERSON> الوصف"]}, {"id": "4.2.19-9", "description": "كعميل، أريد أن أكون قادرًا على تحديد موقعي تلقائيًا، لضمان أن مزود الخدمة يعرف بالضبط أين أنا.", "actor": "العميل", "preconditions": ["العميل مسجل في النظام", "الجهاز المستخدم يحتوي على نظام GPS مفعل"], "postconditions": ["الموقع الجغرافي للعميل محدد في النظام"], "main_flow_steps": ["فتح تطبيق النظام", "النظام يتصل بنظام GPS", "تحديد الموقع الجغرافي", "حفظ الموقع في النظام"]}, {"id": "4.2.19-10", "description": "كمقدم خدمة، أريد أن أرى تفاصيل الطلب، بما في ذلك حالة الخدمة والنوع والوصف والموقع، حتى أتمكن من تقديم الخدمة بشكل أكثر فعالية وسرعة.", "actor": "مقدم الخدمة", "preconditions": ["مقدم الخدمة مسجل في النظام", "مقدم الخدمة يتلقى إشعار طلب خدمة"], "postconditions": ["مقدم الخدمة يطلع على تفاصيل الطلب"], "main_flow_steps": ["فتح تطبيق النظام", "عرض تفاصيل الطلب", "مراجعة حالة الخدمة والنوع والوصف والموقع"]}]}, "4.2.20": {"title": "خدمة فنية لرحلة النقل", "business_goals": ["توفير خدمة تتيح للعملاء الحصول على خدمات فنية للمركبات بشكل مريح وفعال، وفي الوقت نفسه تتيح لورش العمل الفنية استقبال الطلبات التي تتفق مع مجال خبراتهم وإمكانياتهم."], "stakeholders": ["العميل (الذي يرسل طلب الخدمة)", "ورش العمل الفنية (التي تستقبل الطلبات وتجيب عليها)"], "main_steps": ["يتم تصنيف كل طلب خدمة فنية بناءً على نوع الخدمة المحددة وبيانات الخدمة، مثل نوع المركبة والمسافة التقريبية من موقع الخدمة.", "يتم إرسال الطلب إلى ورش العمل الفنية المسجلة التي تفي بالمعايير.", "يقوم المتلقي بمراجعة الطلب ويحدد رده (قبول تقديم الخدمة مع الرد وتحديد التكلفة، طلب مزيد من المعلومات، أو رفض الطلب مع ذكر الأسباب)."], "alternative_steps": [], "user_stories": ["العميل، كمستخدم: أريد تقديم طلب لخدمة فنية، حتى أستطيع الحصول على الخدمات المناسبة لحالة مركبتي.", "الورشة الفنية، كمستخدم: أريد استعراض الطلبات المتاحة، حتى أتمكن من الاختيار والاستجابة للطلبات التي هي في نطاق خبرتي وإمكانياتي.", "الورشة الفنية، كمستخدم: أريد أن أكون قادرًا على رفض الطلبات التي لا أستطيع التعامل معها، حتى لا أضيع وقتي وجهدي بلا داع."], "performance_indicators": ["عدد الطلبات خلال فترات محددة مع تحديد التصنيفات (صيانة عادية، طارئة ...)."], "use_cases": [{"id": "4.2.20-1", "description": "يتم تصنيف كل طلب خدمة فنية بناءً على نوع الخدمة المحددة وبيانات الخدمة، مثل نوع المركبة والمسافة التقريبية من موقع الخدمة.", "actor": "النظام", "preconditions": ["وجود طلب خدمة فنية", "توفر بيانات المركبة والموقع"], "postconditions": ["تصنيف الطلب وإرساله إلى الورش الفنية المناسبة"], "main_flow_steps": ["النظام يصن<PERSON> الطلب", "إرسال الطلب إلى الورش الفنية"]}, {"id": "4.2.20-2", "description": "يتم إرسال الطلب إلى ورش العمل الفنية المسجلة التي تفي بالمعايير.", "actor": "النظام", "preconditions": ["تصنيف الطلب", "توفر الورش الفنية المسجلة"], "postconditions": ["الورش الفنية تتلقى الطلب"], "main_flow_steps": ["النظام يرسل الطلب إلى الورش الفنية المسجلة", "تحديث حالة الطلب"]}, {"id": "4.2.20-3", "description": "يقوم المتلقي بمراجعة الطلب ويحدد رده (قبول تقديم الخدمة مع الرد وتحديد التكلفة، طلب مزيد من المعلومات، أو رفض الطلب مع ذكر الأسباب).", "actor": "الورشة الفنية", "preconditions": ["تلقي الطلب", "توفر معلومات كافية لتحديد الرد"], "postconditions": ["النظام يتلقى رد الورشة الفنية", "إبلاغ العميل برد الورشة الفنية"], "main_flow_steps": ["الورشة الفنية تراجع الطلب", "الورشة الفنية تحدد الرد (قبول/رفض/طلب معلومات إضافية)", "إرسال الرد عبر النظام"]}]}, "4.2.21": {"title": "الرد على طلب خدمة النقل", "business_goals": ["توفير واجهة سهلة الاستخدام تُمكن الورشة الفنية من الرد على الطلبات بما يحقق تقديرها الخاص للتكلفة والوقت، مما يزيد فرص تلقي العميل الخدمة التي يحتاجها بشكل ملائم ومريح."], "stakeholders": ["الورشة الفنية (التي تقدم الرد على الطلب وتحدد الرسوم)"], "main_steps": ["الورشة الفنية تدخل الزمن المتوقع للرد (مثل: خلال كم ساعة خدمة).", "الورشة الفنية تدخل الأجرة المتوقعة للإصلاح المطلوب.", "يتم حساب الرسوم المقررة على الخدمة.", "يتم حساب الضريبة على الخدمة المقررة.", "يتم عرض صافي المبلغ المستحق للورشة بعد خصم الرسوم من أجرة الإصلاح.", "الموافقة وإرسال العرض لطالب الخدمة."], "alternative_steps": ["في حالة عدم الموافقة على الطلب، يمكن للورشة الفنية رفض الطلب مع ذكر السبب."], "user_stories": ["الورشة الفنية، كمستخدم: أريد إدخال الزمن المتوقع للرد وتحديد الأجرة للإصلاح، حتى أستطيع تقديم عرض ملائم ومنافس.", "الورشة الفنية، كمستخدم: أريد أن أرى صافي المبلغ المستحق بعد خصم الرسوم والضرائب من أجرة الإصلاح، حتى يكون لدي فكرة واضحة عن ما سأحصل عليه.", "الورشة الفنية، كمستخدم: أريد أن أتمكن من الموافقة على العرض وإرساله إلى العميل، لكي أحصل على الفرصة لتقديم الخدمة."], "performance_indicators": ["عدد الورش الفنية التي استلمت الطلب ونسبة الاستجابة."], "use_cases": [{"id": "4.2.21-1", "description": "الورشة الفنية تدخل الزمن المتوقع للرد (مثل: خلال كم ساعة خدمة).", "actor": "الورشة الفنية", "preconditions": ["طل<PERSON> خدمة النقل موجود", "الورشة الفنية لديها الصلاحيات اللازمة"], "postconditions": ["يتم عرض الزمن المتوقع للرد لطالب الخدمة"], "main_flow_steps": ["الورشة الفنية تدخل الزمن المتوقع للرد", "تحديث الزمن المتوقع في النظام"]}, {"id": "4.2.21-2", "description": "الورشة الفنية تدخل الأجرة المتوقعة للإصلاح المطلوب.", "actor": "الورشة الفنية", "preconditions": ["طل<PERSON> خدمة النقل موجود", "الورشة الفنية لديها الصلاحيات اللازمة"], "postconditions": ["يتم عرض الأجرة المتوقعة لطالب الخدمة"], "main_flow_steps": ["الورشة الفنية تدخل الأجرة المتوقعة للإصلاح", "تحديث الأجرة المتوقعة في النظام"]}, {"id": "4.2.21-3", "description": "يتم حساب الرسوم المقررة على الخدمة.", "actor": "النظام", "preconditions": ["الأجرة المتوقعة للإصلاح مدخلة"], "postconditions": ["عرض الرسوم المقررة لطالب الخدمة"], "main_flow_steps": ["النظام يحسب الرسوم المقررة", "تحديث الرسوم المقررة في النظام"]}, {"id": "4.2.21-4", "description": "يتم حساب الضريبة على الخدمة المقررة.", "actor": "النظام", "preconditions": ["الأجرة المتوقعة للإصلاح مدخلة"], "postconditions": ["عرض الضريبة المقررة لطالب الخدمة"], "main_flow_steps": ["النظام يحسب الضريبة المقررة", "تحديث الضريبة المقررة في النظام"]}, {"id": "4.2.21-5", "description": "يتم عرض صافي المبلغ المستحق للورشة بعد خصم الرسوم من أجرة الإصلاح.", "actor": "النظام", "preconditions": ["الأجرة المتوقعة للإصلاح مدخلة", "الرسوم والضرائب محسوبة"], "postconditions": ["عرض صافي المبلغ المستحق للورشة"], "main_flow_steps": ["النظام يحسب صافي المبلغ المستحق", "تحديث صافي المبلغ المستحق في النظام"]}, {"id": "4.2.21-6", "description": "الموافقة وإرسال العرض لطالب الخدمة.", "actor": "الورشة الفنية", "preconditions": ["الأجرة المتوقعة للإصلاح مدخلة", "الرسوم والضرائب محسوبة"], "postconditions": ["عرض الورشة الفنية مرسل لطالب الخدمة"], "main_flow_steps": ["الورشة الفنية توافق على العرض", "النظام يرسل العرض لطالب الخدمة"]}, {"id": "4.2.21-7", "description": "رفض الطلب مع ذكر السبب.", "actor": "الورشة الفنية", "preconditions": ["طل<PERSON> خدمة النقل موجود"], "postconditions": ["يتم إبلاغ طالب الخدمة برفض الطلب وسببه"], "main_flow_steps": ["الورشة الفنية ترفض الطلب", "إدخال سبب الرفض", "النظام يرسل سبب الرفض لطالب الخدمة"]}]}, "4.2.22": {"title": "الموافقة على خدمة فنية للرحلة", "business_goals": ["توفير واجهة سهلة الاستخدام تُمكن العميل من الاختيار بين العروض المتاحة وقبول الأنسب بناءً على احتياجاته وتوقعاته."], "stakeholders": ["العميل (طالب الخدمة)"], "main_steps": ["العميل يستعرض كافة العروض المستلمة.", "العميل يقارن بين العروض بناءً على أجرة الإصلاح، المسافة بين الورشة والزمن المتوقع للإصلاح.", "العميل يقرر قبول أحد العروض.", "يتم حساب التكلفة بإجمالي: رسوم أجرة الإصلاح + رسوم الخدمة المقررة + رسوم الضريبة على الخدمة."], "alternative_steps": ["في حالة عدم الموافقة على أي من العروض، يمكن للعميل إعادة إصدار طلب جديد أو التواصل مع الورش الفنية مباشرة."], "user_stories": ["العميل، كمستخدم: أريد مطالعة كافة العروض المستلمة والاختيار من بينها بناءً على تقديري للسعر والمدة والبعد عن الورشة، حتى أحصل على الخدمة بالوقت والمال المناسب لي.", "العميل، كمستخدم: أريد أن أعرف التكلفة الإجمالية للخدمة بما في ذلك الرسوم والضرائب، حتى يكون لدي فكرة واضحة عما سأدفعه.", "العميل، كمستخدم: أريد أن أتمكن من الموافقة على العرض، حتى نتمكن من بدء الإصلاح بأسرع وقت ممكن."], "performance_indicators": [], "use_cases": [{"id": "4.2.22-1", "description": "العميل يستعرض كافة العروض المستلمة.", "actor": "العميل", "preconditions": ["يجب أن تكون العروض متاحة بعد استلامها من الورش الفنية.", "يجب أن يكون العميل قد سجل الدخول إلى النظام."], "postconditions": ["تكون العروض متاحة للمقارنة من قبل العميل.", "يتم تحديث حالة العروض التي تمت مراجعتها في النظام."], "main_flow_steps": ["العميل يدخل إلى صفحة العروض المستلمة في التطبيق.", "العميل يستعرض تفاصيل كل عرض بما في ذلك أجرة الإصلاح، المسافة إلى الورشة، والزمن المتوقع للإصلاح."]}, {"id": "4.2.22-2", "description": "العميل يقارن بين العروض بناءً على أجرة الإصلاح، المسافة بين الورشة والزمن المتوقع للإصلاح.", "actor": "العميل", "preconditions": ["توافر عدة عروض مستلمة من الورش الفنية.", "العميل قد استعرض تفاصيل العروض."], "postconditions": ["يتم اختيار العرض الأنسب من قبل العميل.", "يتم تحديث حالة العروض التي تمت مقارنتها في النظام."], "main_flow_steps": ["العميل ينقر على زر المقارنة بين العروض.", "العميل يقارن بين العروض بناءً على المعايير المتاحة (أجرة الإصلاح، المسافة، الزمن المتوقع).", "العميل يحدد العرض الأنسب."]}, {"id": "4.2.22-3", "description": "العميل يقرر قبول أحد العروض.", "actor": "العميل", "preconditions": ["تمت مقارنة العروض المتاحة.", "العميل قد حدد العرض الأنسب بناءً على المعايير."], "postconditions": ["يتم تثبيت العرض المقبول وتحديث حالة الطلب في النظام.", "يتم الانتقال إلى مرحلة حساب التكلفة الإجمالية."], "main_flow_steps": ["العميل ينقر على زر 'قبول العرض' بجانب العرض المحدد.", "يتم تثبيت العرض المقبول في النظام.", "يتم تحديث حالة الطلب وإبلاغ الورشة الفنية بقبول العرض."]}, {"id": "4.2.22-4", "description": "يتم حساب التكلفة بإجمالي: رسوم أجرة الإصلاح + رسوم الخدمة المقررة + رسوم الضريبة على الخدمة.", "actor": "النظام", "preconditions": ["تم قبول عرض من قبل العميل.", "تفاصيل الرسوم والخدمات متاحة في النظام."], "postconditions": ["يتم عرض التكلفة الإجمالية للعميل.", "يتم تحديث حالة الطلب في النظام لتشمل التكلفة الإجمالية."], "main_flow_steps": ["النظام يحسب التكلفة الإجمالية استنادًا إلى أجرة الإصلاح ورسوم الخدمة والضريبة.", "يتم عرض التكلفة الإجمالية للعميل لمراجعتها.", "يتم تحديث حالة الطلب في النظام لتشمل التكلفة الإجمالية."]}]}, "4.2.23": {"title": "إتمام التعاقد", "business_goals": ["تأكيد حجز الخدمة وتجميد الرسوم المطلوبة للخدمة في حساب طالب الخدمة لضمان تنفيذ الخدمة وسداد الرسوم."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["عبور العميل لمرحلة القبول النهائي للصفقة عبر التطبيق.", "التحقق من متطلبات الصفقة من حيث الرسوم والخدمات المطلوبة.", "التحقق من متطلبات الدفع، بما في ذلك توافر المبلغ في المحفظة.", "في حالة عدم توافر المبلغ، يتم اتمام خطوات الدفع الالكتروني لتعبئة المحفظة والاستمرار في العملية.", "إرسال كود تأكيد للحجز على المبلغ وتجميده ضمن حساب طالب الخدمة."], "alternative_steps": ["في حالة عدم توافر المبلغ لدى طالب الخدمة يتم إيقاف العملية حتى يتم تزويد الحساب بالمبلغ المطلوب.", "قبل انتهاء الزمن المحدد للاستجابة، يمكن لطالب الخدمة أو للورشة الفنية طلب الإلغاء وبعدها يتم الرجوع للخطوات السابقة."], "user_stories": ["كعميل: أو<PERSON> إعطاء تأكيد نهائي للصفقة، لضمان حجز الخدمة وتأمين الرسوم المتفق عليها بأمان.", "كعميل: إذا كان المبلغ الكافي غير متوفر في محفظتي، أريد إضافة المبلغ المطلوب عبر وسائل الدفع الإلكترونية المتاحة لي.", "كعميل: أ<PERSON><PERSON><PERSON> ض<PERSON>ان إمكانية الإلغاء في حالة عدم استجابة الورشة الفنية في الوقت المحدد أو في حالة تغيير رأيي في الخدمة المطلوبة."], "performance_indicators": ["عدد الطلبات التي تم إتمام التعاقد عليها ونسبتها للاجمالي الطلبات التي تم الاستجابة لها."], "use_cases": [{"id": "4.2.23-1", "description": "عبور العميل لمرحلة القبول النهائي للصفقة عبر التطبيق.", "actor": "العميل", "preconditions": ["يجب أن يكون العميل قد اختار عرضًا محددًا من الورشة الفنية.", "يجب أن تكون معلومات الصفقة متاحة."], "postconditions": ["يتم الانتقال إلى مرحلة التحقق من متطلبات الصفقة.", "يتم تحديث حالة الصفقة في النظام."], "main_flow_steps": ["العميل ينقر على زر 'قبول الصفقة' في التطبيق.", "يتم عرض ملخص للصفقة وشروطها النهائية.", "العميل يؤكد القبول."]}, {"id": "4.2.23-2", "description": "التحقق من متطلبات الصفقة من حيث الرسوم والخدمات المطلوبة.", "actor": "النظام", "preconditions": ["تم قبول الصفقة من قبل العميل.", "البيانات المطلوبة للرسوم والخدمات متوفرة في النظام."], "postconditions": ["يتم عرض تفاصيل الرسوم والخدمات للعميل.", "يتم الانتقال إلى مرحلة التحقق من متطلبات الدفع."], "main_flow_steps": ["النظام يستعرض تفاصيل الرسوم المطلوبة.", "النظام يستعرض تفاصيل الخدمات المتضمنة في الصفقة.", "يتم عرض ملخص التكاليف النهائية للعميل."]}, {"id": "4.2.23-3", "description": "التحقق من متطلبات الدفع، بما في ذلك توافر المبلغ في المحفظة.", "actor": "النظام", "preconditions": ["تم التحقق من تفاصيل الصفقة بنجاح.", "يجب أن تكون المحفظة الإلكترونية للعميل مفعّلة."], "postconditions": ["يتم التأكد من توفر المبلغ المطلوب في المحفظة.", "الانتقال إلى مرحلة إرسال كود التأكيد."], "main_flow_steps": ["النظام يتحقق من رصيد المحفظة للعميل.", "إذا كان الرصيد كافيًا، يتم الانتقال إلى الخطوة التالية.", "إذا كان الرصيد غير كافٍ، يتم عرض خيارات الدفع للعميل لتعبئة المحفظة."]}, {"id": "4.2.23-4", "description": "في حالة عدم توافر المبلغ، يتم اتمام خطوات الدفع الالكتروني لتعبئة المحفظة والاستمرار في العملية.", "actor": "العميل", "preconditions": ["التحقق من رصيد المحفظة أظهر عدم كفاية المبلغ.", "العميل يرغب في الاستمرار في الصفقة."], "postconditions": ["يتم تعبئة المحفظة بالمبلغ المطلوب.", "الانتقال إلى مرحلة إرسال كود التأكيد."], "main_flow_steps": ["العميل يختار وسيلة الدفع الإلكتروني المناسبة.", "العميل يُدخل تفاصيل الدفع ويؤكد العملية.", "النظام يتحقق من نجاح عملية التعبئة."]}, {"id": "4.2.23-5", "description": "إرسال كود تأكيد للحجز على المبلغ وتجميده ضمن حساب طالب الخدمة.", "actor": "النظام", "preconditions": ["تم التأكد من توفر المبلغ المطلوب في المحفظة.", "العميل أكد رغبته في إتمام الصفقة."], "postconditions": ["يتم تجميد المبلغ في حساب المحفظة.", "يتم إرسال كود التأكيد للعميل."], "main_flow_steps": ["النظام يولد كود تأكيد فريد.", "النظام يرسل كود التأكيد إلى العميل عبر رسالة نصية أو إشعار داخل التطبيق.", "يتم تجميد المبلغ المطلوب في حساب المحفظة حتى إتمام الخدمة."]}]}, "4.2.24": {"title": "مباشرة الحالة", "business_goals": ["تأكيد بدء الخدمة بين العميل ومزود الخدمة وضمان انتقال سلس وآمن للعملية إلى المرحلة التالية."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["إعطاء (العميل) رمز تأكيد فريد يتولد بشكل تلقائي من قبل النظام.", "يقوم مزود الخدمة بطلب رمز التحقق من طالب الخدمة للتحقق من بداية الخدمة.", "التحقق من نجاح التفعيل ومباشرة الخدمة من خلال التطبيق برمز QR."], "alternative_steps": ["في حالة عدم التمكن من استخدام العميل للرمز، يمكنه الاتصال بخدمة العملاء للمساعدة.", "في حالة فقدان الرمز، يتم إنشاء رمز جديد."], "user_stories": ["كعميل: بمجرد تأكيد الصفقة، أريد تلقي رمز تأكيد فريد يمكنني من التحقق من بداية الخدمة.", "كعميل: إذا واجهت مشكلة في استخدام الرمز، أريد الاتصال بخدمة العملاء للمساعدة.", "كمقدم الخدمة: عند بداية الخدمة، أريد استخدام رمز التحقق المقدم من العميل لتأكيد بدء الخدمة."], "performance_indicators": ["نسبة عدد الطلبات التي تم مباشرتها من إجمالي الطلبات التي تم التعاقد عليها."], "use_cases": [{"id": "4.2.24-1", "description": "إعطاء (العميل) رمز تأكيد فريد يتولد بشكل تلقائي من قبل النظام.", "actor": "النظام", "preconditions": ["تأكيد الصفقة بين العميل ومزود الخدمة"], "postconditions": ["تولد رمز التأكيد الفريد وإرساله للعميل"], "main_flow_steps": ["يقوم النظام بتوليد رمز تأكيد فريد للعميل.", "يتم إرسال الرمز إلى العميل عبر التطبيق أو الرسالة النصية."]}, {"id": "4.2.24-2", "description": "يقوم مزود الخدمة بطلب رمز التحقق من طالب الخدمة للتحقق من بداية الخدمة.", "actor": "مزو<PERSON> الخدمة", "preconditions": ["تسلم مزود الخدمة رمز التأكيد من العميل"], "postconditions": ["التأكد من صلاحية رمز التأكيد"], "main_flow_steps": ["مزو<PERSON> الخدمة يطلب رمز التأكيد من العميل عند بدء الخدمة.", "يدخل مزود الخدمة الرمز في النظام للتحقق من صلاحيته."]}, {"id": "4.2.24-3", "description": "التحقق من نجاح التفعيل ومباشرة الخدمة من خلال التطبيق برمز QR.", "actor": "النظام", "preconditions": ["إدخال مزود الخدمة رمز التأكيد الصحيح"], "postconditions": ["بدء الخدمة بنجاح وتحديث حالة الطلب"], "main_flow_steps": ["النظام يتحقق من رمز التأكيد المقدم من مزود الخدمة.", "في حالة صحة الرمز، يتم تفعيل بدء الخدمة في النظام.", "يتم تحديث حالة الطلب وإعلام الأطراف ببدء الخدمة."]}, {"id": "4.2.24-4", "description": "في حالة عدم التمكن من استخدام العميل للرمز، يمكنه الاتصال بخدمة العملاء للمساعدة.", "actor": "العميل", "preconditions": ["تأكيد الصفقة وفشل استخدام رمز التأكيد"], "postconditions": ["الحصول على مساعدة من خدمة العملاء أو رمز تأكيد جديد"], "main_flow_steps": ["العميل يتصل بخدمة العملاء للمساعدة في حالة فشل استخدام الرمز.", "خدمة العملاء تتحقق من المشكلة وتقدم حلاً مناسبًا، مثل توليد رمز جديد."]}, {"id": "4.2.24-5", "description": "في حالة فقدان الرمز، يتم إنشاء رمز جديد.", "actor": "النظام", "preconditions": ["إبلاغ العميل عن فقدان الرمز"], "postconditions": ["توليد رمز تأكيد جديد وإرساله إلى العميل"], "main_flow_steps": ["النظام يتلقى طلب إنشاء رمز جديد من العميل.", "يتم توليد رمز تأكيد جديد وإرساله للعميل عبر التطبيق أو الرسالة النصية."]}]}, "4.2.25": {"title": "إنهاء الخدمة الفنية", "business_goals": ["تأكيد انتهاء تقديم الخدمة من قبل الورشة الفنية وضمان تحصيل المبلغ المستحق بشكل صحيح."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["إعطاء مزود الخدمة (الورشة الفنية) القدرة على إدخال ملخص الإصلاح (اختياري).", "السماح لمزود الخدمة بعرض المبلغ المستحق أو التعديل عليه (بالتقليل فقط في حالة عدم الاتفاق).", "تأكيد العملية من طالب الخدمة من خلال استخدام كود أو رسالة استجابة."], "alternative_steps": ["في حالة عدم الاتفاق على المبلغ المطلوب، يمكن للعميل الاتصال بخدمة العملاء للمساعدة."], "user_stories": ["كمقدم الخدمة: بعد الانتهاء من تقديم الخدمة، أريد إدخال ملخص الإصلاح والمبلغ المطلوب بشكل سهل ومباشر.", "كعميل: بعد استلام ملخص الإصلاح والمبلغ المطلوب، أريد قابلية التأكيد على المبلغ المطلوب أو طلب مراجعته."], "performance_indicators": ["نسبة عدد الطلبات المنتهية من إجمالي الطلبات التي تمت مباشرتها."], "use_cases": [{"id": "4.2.25-1", "description": "إعطاء مزود الخدمة (الورشة الفنية) القدرة على إدخال ملخص الإصلاح (اختياري).", "actor": "مزو<PERSON> الخدمة", "preconditions": ["انتهاء تقديم الخدمة الفنية"], "postconditions": ["إدخال ملخص الإصلاح في النظام"], "main_flow_steps": ["مزود الخدمة يدخل ملخص الإصلاح في النظام بعد الانتهاء من الخدمة."]}, {"id": "4.2.25-2", "description": "السماح لمزود الخدمة بعرض المبلغ المستحق أو التعديل عليه (بالتقليل فقط في حالة عدم الاتفاق).", "actor": "مزو<PERSON> الخدمة", "preconditions": ["إدخال ملخص الإصلاح"], "postconditions": ["عرض المبلغ المستحق على العميل"], "main_flow_steps": ["مزود الخدمة يعرض المبلغ المستحق على العميل.", "في حالة عدم الاتفاق، يمكن لمزود الخدمة تقليل المبلغ المعروض."]}, {"id": "4.2.25-3", "description": "تأكيد العملية من طالب الخدمة من خلال استخدام كود أو رسالة استجابة.", "actor": "طالب الخدمة", "preconditions": ["عرض المبلغ المستحق من قبل مزود الخدمة"], "postconditions": ["تأكيد أو طلب مراجعة المبلغ من قبل طالب الخدمة"], "main_flow_steps": ["طالب الخدمة يستلم ملخص الإصلاح والمبلغ المطلوب.", "طالب الخدمة يؤكد المبلغ المطلوب أو يطلب مراجعته."]}, {"id": "4.2.25-4", "description": "في حالة عدم الاتفاق على المبلغ المطلوب، يمكن للعميل الاتصال بخدمة العملاء للمساعدة.", "actor": "العميل", "preconditions": ["عدم الات<PERSON><PERSON><PERSON> على المبلغ المطلوب"], "postconditions": ["الحصول على مساعدة من خدمة العملاء"], "main_flow_steps": ["العميل يتصل بخدمة العملاء للحصول على المساعدة في حالة عدم الاتفاق على المبلغ المطلوب.", "خدمة العملاء تتدخل لحل النزاع وتقديم الحل المناسب."]}]}, "4.2.26": {"title": "إغلاق الخدمة الفنية", "business_goals": ["ضمان توزيع الأموال المستحقة بشكل صحيح بين الأطراف المعنية (الورشة الفنية، المنصة، الضرائب) عند إغلاق الخدمة الفنية."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)", "المنصة (التطبيق)"], "main_steps": ["خصم المبلغ الإجمالي من حساب المحفظة لطالب الخدمة.", "تحويل أجرة الإصلاح إلى حساب المحفظة الإلكترونية للورشة الفنية.", "تحويل رسوم الخدمة إلى حساب التطبيق تحت مسمى رسوم خدمات مساندة.", "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق."], "alternative_steps": [], "user_stories": ["كعميل: بعد الموافقة على المبلغ المطلوب، أريد أن يتم خصم المبلغ من محفظتي وتحويله بشكل صحيح دون أي تدخل مني.", "كمزود الخدمة: بعد تقديم الخدمة وتأكيد العميل، أريد تلقي أجرة الإصلاح بشكل مباشر وسهل في محفظتي على التطبيق.", "كإداري للمنصة: أريد تلقي رسوم الخدمة المستحقة وتوزيع الأموال المستحقة بشكل آلي وصحيح على الأطراف المعنية."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المغلقة ونسبتها للطلبات التي تم الانتهاء منها.", "إجمالي تكاليف الخدمة وتفصيلها (رسوم الخدمة والضريبة)."], "use_cases": [{"id": "4.2.26-1", "description": "خصم المبلغ الإجمالي من حساب المحفظة لطالب الخدمة.", "actor": "النظام", "preconditions": ["تأكيد العميل على المبلغ المطلوب"], "postconditions": ["خصم المبلغ من حساب محفظة العميل"], "main_flow_steps": ["يقوم النظام بخصم المبلغ الإجمالي من حساب محفظة طالب الخدمة بعد تأكيد العميل."]}, {"id": "4.2.26-2", "description": "تحويل أجرة الإصلاح إلى حساب المحفظة الإلكترونية للورشة الفنية.", "actor": "النظام", "preconditions": ["خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة"], "postconditions": ["تحويل أجرة الإصلاح إلى محفظة الورشة الفنية"], "main_flow_steps": ["يقوم النظام بتحويل أجرة الإصلاح إلى حساب المحفظة الإلكترونية للورشة الفنية بعد خصم المبلغ من محفظة طالب الخدمة."]}, {"id": "4.2.26-3", "description": "تحويل رسوم الخدمة إلى حساب التطبيق تحت مسمى رسوم خدمات مساندة.", "actor": "النظام", "preconditions": ["خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة"], "postconditions": ["تحويل رسوم الخدمة إلى حساب التطبيق"], "main_flow_steps": ["يقوم النظام بتحويل رسوم الخدمة إلى حساب التطبيق تحت مسمى رسوم خدمات مساندة بعد خصم المبلغ من محفظة طالب الخدمة."]}, {"id": "4.2.26-4", "description": "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق.", "actor": "النظام", "preconditions": ["خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة"], "postconditions": ["تحويل ضريبة القيمة المضافة إلى حساب الضريبة"], "main_flow_steps": ["يقوم النظام بتحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق بعد خصم المبلغ من محفظة طالب الخدمة."]}]}, "4.2.27": {"title": "تقييم خدمة فنية", "business_goals": ["ض<PERSON>ان تقييم شفاف للخدمات المقدمة والحفاظ على سجل دقيق وموثق للبيانات، بهدف تعزيز الجودة وتحسين الخدمات مستقبلاً."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)", "المنصة (التطبيق)"], "main_steps": ["تقييم الخدمة من قبل كل الأطراف.", "أرشفة البيانات الأساسية (وصف الحالة، موقع الإصلاح، تاريخ الطلب) للخدمة في سجلات الطلبات لكل الأطراف.", "أرشفة كافة بيانات الخدمة في سجلات المنصة."], "alternative_steps": [], "user_stories": ["كعميل: بعد استكمال الخدمة، أريد تقييم الخدمة حتى يعرف الآخرون عن جودة العمل والكفاءة.", "كمزود الخدمة: أريد تلقي تغذية راجعة من العملاء حتى أستطيع تحسين أدائي وخدمتي في المستقبل.", "كإداري للمنصة: أريد التأكد من تقييم الجودة الشامل لاستكمال بناء منصة موثوقة وفعالة."], "performance_indicators": ["إحصاء عدد الطلبات التي خضعت للتقييم وحساب نسبتها من إجمالي الطلبات المقدمة."], "use_cases": [{"id": "4.2.27-1", "description": "تقييم الخدمة من قبل كل الأطراف.", "actor": "العميل، مزود الخدمة", "preconditions": ["انتهاء تقديم الخدمة الفنية"], "postconditions": ["تسجيل التقييم في النظام"], "main_flow_steps": ["العميل ومزود الخدمة يقيمون الخدمة المقدمة.", "يدخل كل طرف تقييمه في النظام."]}, {"id": "4.2.27-2", "description": "أرشفة البيانات الأساسية (وصف الحالة، موقع الإصلاح، تاريخ الطلب) للخدمة في سجلات الطلبات لكل الأطراف.", "actor": "النظام", "preconditions": ["إدخال التقييم من قبل جميع الأطراف"], "postconditions": ["أرشفة البيانات الأساسية في سجلات الطلبات"], "main_flow_steps": ["يقوم النظام بأرشفة البيانات الأساسية للخدمة في سجلات الطلبات لكل الأطراف بعد إدخال التقييم."]}, {"id": "4.2.27-3", "description": "أرشفة كافة بيانات الخدمة في سجلات المنصة.", "actor": "النظام", "preconditions": ["إدخال التقييم من قبل جميع الأطراف"], "postconditions": ["أرشفة كافة بيانات الخدمة في سجلات المنصة"], "main_flow_steps": ["يقوم النظام بأرشفة كافة بيانات الخدمة في سجلات المنصة بعد إدخال التقييم."]}]}, "4.2.28": {"title": "إدخال بيانات قطع الغيار", "business_goals": ["تسهيل الوصول إلى خدمات قطع الغيار من محلات مختارة وموثوقة، وتوفير تجربة سلسة ومريحة للعملاء في اختيار وطلب قطع الغيار."], "stakeholders": ["العميل (طالب الخدمة)", "محلات القطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["العميل يختار نوع القطع (جديد/مستعمل).", "النظام يستدعي بيانات المركبة للعميل.", "العميل يدخل بيانات القطع الوصفية والكمية (يمكن استخدام النص، التسجيل الصوتي، الصورة، أو الكل).", "تحديد الموقع بشكل آلي من خلال النظام GPS."], "alternative_steps": ["العميل لا يمكنه تسجيل أرقام الهاتف للتواصل بين الأطراف."], "user_stories": ["كعميل: أ<PERSON><PERSON><PERSON> اختيار نوع المحل، البحث عن قطع الغيار وطلبها بناءً على بيانات مركبتي.", "محلات قطع الغيار: أريد استلام الطلبات من العملاء ومعرفة تفاصيلها لإمكانية تقديم الخدمة المطلوبة.", "كإداري للمنصة كمستخدم: أريد توفير منصة موثوقة وسهلة الاستخدام للعملاء لطلب قطع الغيار، وأريد تحقيق تواصل فعال بين العميل ومحلات قطع الغيار."], "performance_indicators": [], "use_cases": [{"id": "4.2.28-1", "description": "العميل يختار نوع القطع (جديد/مستعمل).", "actor": "العميل", "preconditions": ["تسجيل دخول العميل إلى النظام", "توفر بيانات المركبة في النظام"], "postconditions": ["تحديد نوع القطع في النظام"], "main_flow_steps": ["العميل يفتح صفحة طلب قطع الغيار في التطبيق.", "العميل يختار نوع القطع (جديد/مستعمل)."]}, {"id": "4.2.28-2", "description": "النظام يستدعي بيانات المركبة للعميل.", "actor": "النظام", "preconditions": ["تحديد نوع القطع من قبل العميل"], "postconditions": ["عرض بيانات المركبة للعميل"], "main_flow_steps": ["النظام يستدعي بيانات المركبة من قاعدة البيانات.", "النظام يعرض بيانات المركبة للعميل في صفحة طلب قطع الغيار."]}, {"id": "4.2.28-3", "description": "العميل يدخل بيانات القطع الوصفية والكمية (يمكن استخدام النص، التسجيل الصوتي، الصورة، أو الكل).", "actor": "العميل", "preconditions": ["عرض بيانات المركبة للعميل"], "postconditions": ["إدخال بيانات القطع في النظام"], "main_flow_steps": ["العميل يدخل بيانات القطع الوصفية والكمية باستخدام النص أو التسجيل الصوتي أو الصورة أو الكل."]}, {"id": "4.2.28-4", "description": "تحديد الموقع بشكل آلي من خلال النظام GPS.", "actor": "النظام", "preconditions": ["إدخال بيانات القطع من قبل العميل"], "postconditions": ["تحديد موقع العميل بشكل آلي"], "main_flow_steps": ["النظام يستخدم خدمة GPS لتحديد موقع العميل بشكل آلي.", "النظام يعرض موقع العميل في صفحة طلب قطع الغيار."]}, {"id": "4.2.28-5", "description": "العميل لا يمكنه تسجيل أرقام الهاتف للتواصل بين الأطراف.", "actor": "النظام", "preconditions": ["إدخال بيانات القطع وتحديد الموقع"], "postconditions": ["منع تسجيل أرقام الهاتف في النظام"], "main_flow_steps": ["النظام يمنع تسجيل أرقام الهاتف أو أي بيانات اتصال شخصية بين الأطراف."]}]}, "4.2.29": {"title": "نشر خدمة قطع الغيار", "business_goals": ["إذا لم يتم العثور على عدد كافٍ من المحلات ضمن النطاق المحدد، يمكن للنظام توسيع نطاق البحث تدريجيًا.", "المحلات التي لا تستطيع تقديم عرض قد تقترح توفير القطعة المطلوبة في وقت لاحق أو توفير بديل."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["العميل ينشر طلبًا محددًا بالقطع التي يحتاجها، مثل فانوس لعربية نقل شيفروليه.", "النظام يبحث تلقائيًا عن المحلات الأقرب إلى موقع العميل، مستهدفًا تحديد أقرب 5 محلات ضمن نطاق 10 كيلومترات. إذا لم يجد 5 محلات ضمن هذا النطاق، يتوسع قطر البحث تلقائيًا حتى يجد على الأقل 5 محلات.", "محلات قطع الغيار تستعرض الطلب وتقدم عروضها مع تفاصيل القطع والأسعار."], "alternative_steps": ["إذا لم يجد العميل العرض المناسب، يمكنه طلب مزيد من التفاصيل أو عروض جديدة.", "إعطاء خيار إعادة البحث وتوسيع نطاق البحث (من خلال النظام يبحث عن المحلات)", "المحلات قد تقدم عروضًا ترويجية أو بدائل إضافية بناءً على المخزون المتاح."], "user_stories": ["كعميل: أ<PERSON><PERSON><PERSON> أن يحدد النظام تلقائيًا المحلات الأقرب إلي ويعرض عليّ الخيارات لضمان سرعة الحصول على القطعة.", "كمحل قطع غيار: أريد أن يعرض عليّ النظام الطلبات القريبة من موقعي لتقديم عروض تنافسية وسريعة.", "كمدير للمنصة: أريد توفير نظام يكفل توزيع الطلبات بشكل عادل وفعال بين المحلات القريبة والبعيدة، معززًا الكفاءة والرضا لدى العملاء ومقدمي الخدمة."], "performance_indicators": ["عد<PERSON> الطلبات المنشورة خلال فترات محددة.", "عدد المستلمين لطلبات المنشورة (لكل طلب لمعرفة قاعدة المستجيبين)."], "use_cases": [{"id": "4.2.29-1", "description": "العميل ينشر طلبًا محددًا بالقطع التي يحتاجها، مثل فانوس لعربية نقل شيفروليه.", "actor": "العميل", "preconditions": ["تسجيل دخول العميل إلى النظام", "توفر بيانات المركبة في النظام"], "postconditions": ["نشر الطلب في النظام"], "main_flow_steps": ["العميل يفتح صفحة طلب قطع الغيار في التطبيق.", "العميل يحدد القطع التي يحتاجها ويدخل تفاصيل الطلب."]}, {"id": "4.2.29-2", "description": "النظام يبحث تلقائيًا عن المحلات الأقرب إلى موقع العميل، مستهدفًا تحديد أقرب 5 محلات ضمن نطاق 10 كيلومترات. إذا لم يجد 5 محلات ضمن هذا النطاق، يتوسع قطر البحث تلقائيًا حتى يجد على الأقل 5 محلات.", "actor": "النظام", "preconditions": ["نشر طلب قطع الغيار من قبل العميل"], "postconditions": ["عرض نتائج البحث عن المحلات على العميل"], "main_flow_steps": ["النظام يبحث عن المحلات الأقرب إلى موقع العميل ضمن نطاق 10 كيلومترات.", "إذا لم يجد النظام 5 محلات ضمن هذا النطاق، يتوسع البحث تدريجيًا حتى يجد 5 محلات.", "النظام يعرض نتائج البحث على العميل."]}, {"id": "4.2.29-3", "description": "محلات قطع الغيار تستعرض الطلب وتقدم عروضها مع تفاصيل القطع والأسعار.", "actor": "محل<PERSON><PERSON> قطع الغيار", "preconditions": ["عرض طلب قطع الغيار من قبل النظام"], "postconditions": ["تقديم عروض الأسعار للعميل"], "main_flow_steps": ["محلات قطع الغيار تستعرض تفاصيل الطلب المقدم من العميل.", "محلات قطع الغيار تقدم عروضها مع تفاصيل القطع والأسعار."]}, {"id": "4.2.29-4", "description": "إذا لم يجد العميل العرض المناسب، يمكنه طلب مزيد من التفاصيل أو عروض جديدة.", "actor": "العميل", "preconditions": ["استلام العروض من المحلات"], "postconditions": ["طل<PERSON> مزيد من التفاصيل أو عروض جديدة"], "main_flow_steps": ["العميل يستعرض العروض المقدمة من المحلات.", "إذا لم يكن أي عرض مناسب، يمكن للعميل طلب مزيد من التفاصيل أو عروض جديدة."]}, {"id": "4.2.29-5", "description": "إعطاء خيار إعادة البحث وتوسيع نطاق البحث (من خلال النظام يبحث عن المحلات).", "actor": "النظام", "preconditions": ["استلام العميل للعروض غير المناسبة"], "postconditions": ["إعادة البحث عن المحلات وتوسيع النطاق"], "main_flow_steps": ["النظام يقدم خيار إعادة البحث وتوسيع النطاق للعميل.", "النظام يبحث مرة أخرى عن المحلات ضمن نطاق موسع."]}, {"id": "4.2.29-6", "description": "المحلات قد تقدم عروضًا ترويجية أو بدائل إضافية بناءً على المخزون المتاح.", "actor": "محل<PERSON><PERSON> قطع الغيار", "preconditions": ["عرض تفاصيل الطلب على المحلات"], "postconditions": ["تقديم عروض ترويجية أو بدائل إضافية"], "main_flow_steps": ["محلات قطع الغيار تستعرض الطلب وتقدم عروض ترويجية أو بدائل إضافية إذا كان المخزون لا يتوفر على القطعة المطلوبة."]}]}, "4.2.30": {"title": "قطع غيار بيانات إضافية", "business_goals": ["توفير تعامل مرن ومريح بين العملاء ومحلات قطع الغيار، مع ضمان الخصوصية والأمان للمعلومات الشخصية."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["مقدم الخدمة يطلب بيانات إضافية من خلال فتح محادثة مؤقتة والتي تسمح بالكتابة والتسجيل الصوتي وإرفاق الصور.", "على العميل الرد على طلب بيانات الخدمة الإضافية وتوفيرها.", "قواعد النظام تمنع تسجيل أرقام الهاتف أو بيانات الاتصال الشخصية بين الأطراف في المحادثات وتمنع ظهورها في استعراض الملفات."], "alternative_steps": ["في حالة عدم استجابة العميل، يمكن لمقدم الخدمة الرجوع إلى الطلب الأصلي ومتابعة القرار الأمثل بناءً على المعلومات المتوفرة فقط."], "user_stories": ["العميل، كمستخدم: أريد استلام طلب لتقديم معلومات إضافية، وأيضًا أن أكون قادرًا على الرد على طلبات الخدمة بسهولة.", "محلات قطع الغيار، كمستخدم: أريد أن أتمكن من طلب بيانات إضافية من العميل في حالة الحاجة، وأن يكون الاتصال تابع للمنصة ولا يتعلق ببيانات الاتصال الشخصية.", "كإداري للمنصة: أريد تزويد المستخدمين بأدوات لتوفير تحسين التواصل والتعاون، مع الحفاظ على الأمان والخصوصية لمعلومات المستخدم."], "performance_indicators": ["عد<PERSON> الطلبات التي تم طلب بيانات إضافية لها بالنسبة لإجمالي الطلبات."], "use_cases": [{"id": "4.2.30-1", "description": "مقدم الخدمة يطلب بيانات إضافية من خلال فتح محادثة مؤقتة.", "actor": "مقدم الخدمة", "preconditions": ["وجود طلب سابق من العميل."], "postconditions": ["تمكن مقدم الخدمة من الحصول على البيانات الإضافية المطلوبة."], "main_flow_steps": ["مقدم الخدمة يفتح محادثة مؤقتة.", "مقدم الخدمة يطلب البيانات الإضافية.", "العميل يستلم طلب البيانات."]}, {"id": "4.2.30-2", "description": "على العميل الرد على طلب بيانات الخدمة الإضافية وتوفيرها.", "actor": "العميل", "preconditions": ["استلام طلب البيانات الإضافية من مقدم الخدمة."], "postconditions": ["تقديم البيانات المطلوبة لمقدم الخدمة."], "main_flow_steps": ["العميل يقرأ طلب البيانات الإضافية.", "العميل يجهز البيانات المطلوبة.", "العميل يرسل البيانات إلى مقدم الخدمة."]}, {"id": "4.2.30-3", "description": "قواعد النظام تمنع تسجيل أرقام الهاتف أو بيانات الاتصال الشخصية بين الأطراف في المحادثات وتمنع ظهورها في استعراض الملفات.", "actor": "النظام", "preconditions": ["بدء محادثة مؤقتة بين العميل ومقدم الخدمة."], "postconditions": ["حماية بيانات الاتصال الشخصية للأطراف المشاركة في المحادثة."], "main_flow_steps": ["النظام يراقب المحادثة المؤقتة.", "النظام يمنع تسجيل أرقام الهاتف أو بيانات الاتصال الشخصية.", "النظام يمنع ظهور بيانات الاتصال الشخصية في استعراض الملفات."]}, {"id": "4.2.30-4", "description": "في حالة عدم استجابة العميل، يمكن لمقدم الخدمة الرجوع إلى الطلب الأصلي ومتابعة القرار الأمثل بناءً على المعلومات المتوفرة فقط.", "actor": "مقدم الخدمة", "preconditions": ["عدم استجابة العميل لطلب البيانات الإضافية."], "postconditions": ["متابعة القرار الأمثل بناءً على المعلومات المتاحة."], "main_flow_steps": ["مقدم الخدمة ينتظر استجابة العميل.", "عدم تلقي استجابة من العميل خلال فترة محددة.", "مقدم الخدمة يتخذ قرارًا بناءً على المعلومات المتوفرة."]}]}, "4.2.31": {"title": "استجابة لطلب قطع غيار", "business_goals": [], "stakeholders": ["محل<PERSON><PERSON> قطع الغيار", "العميل", "المنصة"], "main_steps": ["محل قطع الغيار يقوم بإدخال مواصفات القطعة المطلوبة، بما في ذلك الحجم، الوزن التقريبي، والعدد.", "محل قطع الغيار يقوم بإدخال تكلفة القطع.", "تقوم المنصة بحساب رسوم الخدمة.", "يتم إحتساب الضريبة على رسوم الخدمة المقررة.", "يتم عرض صافي المبلغ المستحق للمحل بعد خصم الرسوم والضريبة من تكلفة القطعة.", "محل قطع الغيار يقدم الموافقة ويرسل العرض إلى طالب الخدمة."], "alternative_steps": ["في حالة عدم القدرة على تقديم القطعة المطلوبة، يمكن لمحل قطع الغيار إرسال رسالة نصية للعميل تشرح السبب."], "user_stories": ["محلات قطع الغيار، كمستخدم: أري<PERSON> التمكن من تحديد بيانات قطعة الغيار بدقة (بما في ذلك الحجم، الوزن، والعدد) وتحديد التكلفة المرتبطة، وبالتالي تقديم عرض شاف وواضح للعميل.", "العميل، كمستخدم: أريد أن أتلقى عروضًا شفافة ومفصلة من محلات قطع الغيار، تشمل الكلفة الكاملة والرسوم المرتبطة.", "كاداري للمنصة: أريد تقديم نظام يمكن من خلاله محلات قطع الغيار من تقديم عروض شافية وشفافة للعملاء، كل ذلك مع تجنب الاحتيال أو أي سلوك غير مرغوب فيه."], "performance_indicators": ["نسبة عدد الطلبات التي تم الاستجابة لها بالنسبة لعدد الطلبات الإجمالي."], "use_cases": [{"id": "4.2.31-1", "description": "محل قطع الغيار يقوم بإدخال مواصفات القطعة المطلوبة، بما في ذلك الحجم، الوزن التقريبي، والعدد.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": ["استلام طلب من العميل لقطع الغيار."], "postconditions": ["إدخال مواصفات القطعة المطلوبة في النظام."], "main_flow_steps": ["محل قطع الغيار يستعرض الطلب المستلم.", "محل قطع الغيار يدخل مواصفات القطعة المطلوبة (الحجم، الوزن، العدد)."]}, {"id": "4.2.31-2", "description": "محل قطع الغيار يقوم بإدخال تكلفة القطع.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": ["إدخال مواصفات القطعة المطلوبة."], "postconditions": ["إدخال تكلفة القطع في النظام."], "main_flow_steps": ["محل قطع الغيار يحسب تكلفة القطعة.", "محل قطع الغيار يدخل تكلفة القطعة في النظام."]}, {"id": "4.2.31-3", "description": "تقوم المنصة بحساب رسوم الخدمة.", "actor": "المنصة", "preconditions": ["إدخال مواصفات وتكلفة القطعة من قبل محل قطع الغيار."], "postconditions": ["حساب رسوم الخدمة وإضافتها للتكلفة الإجمالية."], "main_flow_steps": ["النظام يستلم مواصفات وتكلفة القطعة.", "النظام يحسب رسوم الخدمة.", "النظام يعرض رسوم الخدمة على محل قطع الغيار."]}, {"id": "4.2.31-4", "description": "يتم احتساب الضريبة على رسوم الخدمة المقررة.", "actor": "المنصة", "preconditions": ["حساب رسوم الخدمة."], "postconditions": ["إضافة الضريبة إلى التكلفة الإجمالية."], "main_flow_steps": ["النظام يحسب الضريبة على رسوم الخدمة.", "النظام يعرض الضريبة المقررة على محل قطع الغيار."]}, {"id": "4.2.31-5", "description": "يتم عرض صافي المبلغ المستحق للمحل بعد خصم الرسوم والضريبة من تكلفة القطعة.", "actor": "المنصة", "preconditions": ["حساب رسوم الخدمة والضريبة."], "postconditions": ["عرض صافي المبلغ المستحق لمحل قطع الغيار."], "main_flow_steps": ["النظام يحسب صافي المبلغ بعد خصم الرسوم والضريبة.", "النظام يعرض صافي المبلغ المستحق لمحل قطع الغيار."]}, {"id": "4.2.31-6", "description": "محل قطع الغيار يقدم الموافقة ويرسل العرض إلى طالب الخدمة.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": ["استلام صافي المبلغ المستحق من النظام."], "postconditions": ["إرسال العرض إلى طالب الخدمة."], "main_flow_steps": ["محل قطع الغيار يراجع صافي المبلغ المستحق.", "مح<PERSON> قطع الغيار يوافق على العرض.", "محل قطع الغيار يرسل العرض إلى طالب الخدمة."]}, {"id": "4.2.31-7", "description": "في حالة عدم القدرة على تقديم القطعة المطلوبة، يمكن لمحل قطع الغيار إرسال رسالة نصية للعميل تشرح السبب.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": ["عدم توفر القطعة المطلوبة."], "postconditions": ["إبلاغ العميل بعدم توفر القطعة المطلوبة."], "main_flow_steps": ["محل قطع الغيار يكتشف عدم توفر القطعة المطلوبة.", "مح<PERSON> قطع الغيار يرسل رسالة نصية للعميل تشرح السبب."]}]}, "4.2.32": {"title": "الموافقة على طلب قطع غيار", "business_goals": [], "stakeholders": ["العميل", "المنصة"], "main_steps": ["العميل يستعرض كافة العروض المستلمة من محلات قطع الغيار.", "العميل ينظر إلى تفاصيل العروض بما في ذلك أجرة قطع الغيار (تكون رقم واحد شاملة أجرة قطع الغيار والرسوم والضريبة)، المسافة إلى محل قطع الغيار، والوقت المتوقع للاستجابة.", "العميل يختار العرض الأكثر ملاءمة ويرسل موافقته على التطبيق.", "المنصة تحسب التكلفة الإجمالية للخدمة بما في ذلك أجرة قطع الغيار، رسوم خدمة المنصة، ورسوم الضريبة على الخدمة.", "يتم إبلاغ العميل بالتكلفة الإجمالية ويتم طلب موافقته النهائية."], "alternative_steps": ["في حالة عدم موافقة العميل على التكلفة الإجمالية، يمكنه العودة لمرحلة اختيار العرض واختيار عرض آخر."], "user_stories": ["كعميل: أري<PERSON> القدرة على رؤية كافة العروض المتاحة، مع تفاصيل كل عرض، واختصار الخيارات بناءً على الأحكام والمصلحة الشخصية، والقدرة على الموافقة أو الرفض.", "كاداري للمنصة: أريد توفير نظام بسيط وواضح يمكن من خلاله المستخدم من اختيار العرض الأمثل والتأكد من وضوح وشفافية الرسوم المستخدمة."], "performance_indicators": ["إجمالي عدد الطلبات المقبولة و نسبتها لإجمالي عدد الطلبات التي تم إنشاؤها.", "تكاليف اجرة قطع الغيار وتفاصيلها من حيث الرسوم وغيره."], "use_cases": [{"id": "4.2.32-1", "description": "العميل يستعرض كافة العروض المستلمة من محلات قطع الغيار.", "actor": "العميل", "preconditions": ["استلام عروض من محلات قطع الغيار."], "postconditions": ["العميل لديه قائمة بالعروض المفصلة."], "main_flow_steps": ["العميل يفتح التطبيق ويذهب إلى صفحة العروض المستلمة.", "العميل يستعرض جميع العروض المقدمة من المحلات."]}, {"id": "4.2.32-2", "description": "العميل ينظر إلى تفاصيل العروض بما في ذلك أجرة قطع الغيار (تكون رقم واحد شاملة أجرة قطع الغيار والرسوم والضريبة)، المسافة إلى محل قطع الغيار، والوقت المتوقع للاستجابة.", "actor": "العميل", "preconditions": ["العميل يستعرض العروض المقدمة."], "postconditions": ["العميل لديه تفاصيل كاملة عن كل عرض."], "main_flow_steps": ["العميل يفتح تفاصيل كل عرض.", "العميل يقرأ المعلومات الخاصة بتكلفة القطعة، المسافة، ووقت الاستجابة."]}, {"id": "4.2.32-3", "description": "العميل يختار العرض الأكثر ملاءمة ويرسل موافقته على التطبيق.", "actor": "العميل", "preconditions": ["العميل يستعرض ويقارن بين العروض المختلفة."], "postconditions": ["العرض المختار يتم إرساله إلى مقدم الخدمة."], "main_flow_steps": ["العميل يحدد العرض الذي يناسبه.", "العميل ينقر على زر الموافقة لإرسال العرض إلى مقدم الخدمة."]}, {"id": "4.2.32-4", "description": "المنصة تحسب التكلفة الإجمالية للخدمة بما في ذلك أجرة قطع الغيار، رسوم خدمة المنصة، ورسوم الضريبة على الخدمة.", "actor": "المنصة", "preconditions": ["العميل يرسل موافقته على العرض."], "postconditions": ["عرض التكلفة الإجمالية للعميل للموافقة النهائية."], "main_flow_steps": ["النظام يتلقى موافقة العميل على العرض.", "النظام يحسب التكلفة الإجمالية للعرض بما في ذلك الرسوم والضريبة.", "النظام يعرض التكلفة الإجمالية للعميل."]}, {"id": "4.2.32-5", "description": "يتم إبلاغ العميل بالتكلفة الإجمالية ويتم طلب موافقته النهائية.", "actor": "المنصة", "preconditions": ["حساب التكلفة الإجمالية للعرض."], "postconditions": ["استلام موافقة العميل النهائية."], "main_flow_steps": ["النظام يرسل إشعار للعميل بالتكلفة الإجمالية.", "العميل يراجع التكلفة الإجمالية.", "العميل يؤكد موافقته النهائية على التكلفة."]}, {"id": "4.2.32-6", "description": "في حالة عدم موافقة العميل على التكلفة الإجمالية، يمكنه العودة لمرحلة اختيار العرض واختيار عرض آخر.", "actor": "العميل", "preconditions": ["عدم موافقة العميل على التكلفة الإجمالية."], "postconditions": ["اختيار عرض جديد من العروض المتاحة."], "main_flow_steps": ["العميل يرفض العرض الحالي.", "العميل يعود لصفحة العروض.", "العميل يختار عرض جديد."]}]}, "4.2.33": {"title": "إتمام صفقة قطع الغيار", "business_goals": [], "stakeholders": ["العميل", "المنصة"], "main_steps": ["العميل يتأكد من وجود المبلغ الكافي في المحفظة الخاصة به.", "إذا لم يكن المبلغ المتوافر كافياً، فإن العميل يقوم بإضافة الأموال من خلال واحدة من طرق الدفع الإلكترونية المتاحة.", "في حالة التحقق من كود التحقق يتم حجز المبلغ وتجميده في انتظار التأكيد النهائي على الصفقة.", "العميل يؤكد الصفقة، وعلى إثر ذلك يتم تنفيذ أوامر النقل.", "إذا تم طلب الإلغاء قبل بداية عملية التوصيل، يتم إعادة المبلغ المحجوز إلى المحفظة."], "alternative_steps": ["في حالة عدم توافر العميل على  المبلغ المطلوب في المحفظة، يمكنه العودة لمرحلة اختيار العرض واختيار عرض آخر أو إلغاء العملية تماماً."], "user_stories": ["العميل، كمستخدم: أريد التحقق من أن القيمة المطلوبة متوفرة في المحفظة وإضافة المزيد إذا كان ذلك ضرورياً، وتأكيد الصفقة أو إلغائها حسب الحاجة.", "كاداري للمنصة: أري<PERSON> ض<PERSON>ان توافر الأموال الكافية من قبل العميل قبل التأكيد النهائي على الصفقة وتوفير آلية لإلغاء الصفقة في حالة الحاجة."], "performance_indicators": ["عد<PERSON> الطلبات التي تمت الصفقة لها ونسبتها من إجمالي الطلبات التي تم الاستجابة لها.", "قيمة المبالغ المحجوزة."], "use_cases": [{"id": "4.2.33-1", "description": "التحقق من وجود المبلغ الكافي في المحفظة الخاصة بالعميل", "actor": "العميل", "preconditions": ["العميل لديه حساب على المنصة", "العميل لديه محفظة مفعلة على المنصة"], "postconditions": ["تأكيد وجود المبلغ الكافي في المحفظة"], "main_flow_steps": ["العميل يدخل إلى حسابه على المنصة", "العميل يتحقق من رصيد المحفظة", "العميل يتأكد من أن الرصيد كافٍ لإتمام الصفقة"]}, {"id": "4.2.33-2", "description": "إضافة الأموال إلى المحفظة في حالة عدم كفاية الرصيد", "actor": "العميل", "preconditions": ["العميل تحقق من أن رصيد المحفظة غير كافٍ لإتمام الصفقة"], "postconditions": ["تمت إضافة الأموال إلى المحفظة", "تم تجميد المبلغ المطلوب في المحفظة"], "main_flow_steps": ["العميل يختار خيار إضافة الأموال", "العميل يحدد طريقة الدفع الإلكترونية المتاحة", "العميل يكمل عملية الدفع", "المنصة تضيف الأموال إلى المحفظة", "المنصة تقوم بتجميد المبلغ المطلوب في المحفظة"]}, {"id": "4.2.33-3", "description": "تأكيد الصفقة من قبل العميل", "actor": "العميل", "preconditions": ["المبلغ المطلوب متوفر في المحفظة", "المبلغ محجوز في المحفظة"], "postconditions": ["تم تأكيد الصفقة", "بد<PERSON>ت أوامر النقل"], "main_flow_steps": ["العميل يستعرض تفاصيل الصفقة", "العميل يضغط على زر تأكيد الصفقة", "النظام يتحقق من الرصيد المحجوز", "النظام يبدأ أوامر النقل"]}, {"id": "4.2.33-4", "description": "إلغاء الصفقة قبل بدء عملية التوصيل", "actor": "العميل", "preconditions": ["الصفقة تم تأكيدها ولكن لم تبدأ عملية التوصيل"], "postconditions": ["تم إلغاء الصفقة", "تم إعادة المبلغ المحجوز إلى المحفظة"], "main_flow_steps": ["العميل يختار خيار إلغاء الصفقة", "النظام يتحقق من أن عملية التوصيل لم تبدأ", "النظام يلغي الصفقة", "النظام يعيد المب<PERSON>غ المحجوز إلى محفظة العميل"]}]}, "4.2.34": {"title": "طلب توصيل قطعة غيار", "business_goals": [], "stakeholders": ["طالب الخدمة", "المحل", "النظام", "المركبات"], "main_steps": ["إنشاء الطلب من قبل طالب الخدمة.", "استدعاء بيانات القطع وبيانات موقع الاستلام والتسليم ونشرها لكافة المركبات في محيط ٥٠ كم وتكون متجهة لنفس نقطة التسليم.", "استلام العروض من المركبات واستعراضها من قبل طالب الخدمة.", "إرسال العرض لطالب الخدمة.", "يتم قبول الطلب وحجز المبلغ الإجمالي بمجرد التأكيد على كود التحقق المرسل."], "alternative_steps": ["تحديد نطاقات مختلفة للمسافة والأسعار من قبل النظام بناءًا على المعايير المحددة.", "في حالة عدم وجود عرض مناسب، يمكن استحداث سلة لوضع الطلبات فيها وتكون هذه السلة قابلة للاستعراض من الناقلين حسب معايير محددة (وجهة الناقل، مكان تواجده وغيرها) بحيث يمكن للناقل عند الدخول لهذه السلة اختيار الطلبات التي تناسبه."], "user_stories": ["طالب الخدمة: كطا<PERSON><PERSON> خدمة, أريد أن أقوم بإنشاء طلب لتوصيل القطع المطلوبة من المحل إلى موقعي حتى يتم تأمين القطعة بأسرع وقت ممكن", "كاداري للمنصة: كنظام, أريد أن أقوم باستدعاء بيانات القطع وموقع الاستلام والتسليم ونشرها لكافة المركبات في محيط  ٥٠ كم (لا يوجد عروض في هذه المرحلة سيكون سعر التوصيل ثابت)", "طالب الخدمة: كطا<PERSON><PERSON> خدمة, أريد أن أستلم العروض المقدمة من المركبات واستعراضها حتى أختار العرض الأنسب لي من حيث السرعة والتكلفة . في حالة توجد مركبات في نطاق 50كم من موقع الاستلام (لا يوجد عروض في هذه المرحلة سيكون سعر التوصيل ثابت)", "طالب الخدمة: كطا<PERSON><PERSON> خدمة, أريد أن أقبل العرض وأتم الخدمة بالحجز على المبلغ الإجمالي حتى يتم تأمين الخدمة وبدء عملية النقل."], "performance_indicators": ["عدد طلبات التوصيل و نسبتها من إجمالي الطلبات التي تمت (الموافقة عليها و تمت الصفقة عليها).", "عد<PERSON> المستجيبين للطلبات."], "use_cases": [{"id": "4.2.34-1", "description": "إنشاء الطلب من قبل طالب الخدمة", "actor": "طالب الخدمة", "preconditions": ["طالب الخدمة لديه حساب على المنصة", "طالب الخدمة يحتاج إلى توصيل قطع غيار"], "postconditions": ["تم إنشاء الطلب بنجاح"], "main_flow_steps": ["طالب الخدمة يدخل إلى حسابه على المنصة", "طالب الخدمة يختار خيار إنشاء طلب توصيل قطع غيار", "طالب الخدمة يدخل تفاصيل القطع المطلوبة", "طالب الخدمة يحدد موقع الاستلام والتسليم", "طالب الخدمة ينشر الطلب على المنصة"]}, {"id": "4.2.34-2", "description": "استدعاء بيانات القطع وبيانات موقع الاستلام والتسليم ونشرها لكافة المركبات في محيط ٥٠ كم", "actor": "النظام", "preconditions": ["تم إنشاء الطلب من قبل طالب الخدمة"], "postconditions": ["تم نشر الطلب لكافة المركبات في محيط ٥٠ كم"], "main_flow_steps": ["النظام يستدعي بيانات القطع المطلوبة", "النظام يستدعي بيانات موقع الاستلام والتسليم", "النظام ينشر الطلب لكافة المركبات في محيط ٥٠ كم"]}, {"id": "4.2.34-3", "description": "استلام العروض من المركبات واستعراضها من قبل طالب الخدمة", "actor": "طالب الخدمة", "preconditions": ["تم نشر الطلب لكافة المركبات في محيط ٥٠ كم"], "postconditions": ["طالب الخدمة استعرض العروض المقدمة"], "main_flow_steps": ["المركبات المتاحة في محيط ٥٠ كم ترسل عروضها", "النظام يجمع العروض المقدمة", "النظام يعرض العروض على طالب الخدمة", "طالب الخدمة يستعرض العروض المقدمة"]}, {"id": "4.2.34-4", "description": "إرسال العرض لطالب الخدمة", "actor": "النظام", "preconditions": ["تم تقديم عروض من المركبات", "طالب الخدمة استعرض العروض المقدمة"], "postconditions": ["تم إرسال العرض لطالب الخدمة"], "main_flow_steps": ["النظام يرسل العرض الأفضل لطالب الخدمة بناءً على معايير محددة", "طالب الخدمة يستلم العرض", "طالب الخدمة يراجع العرض", "طالب الخدمة يقبل أو يرفض العرض"]}, {"id": "4.2.34-5", "description": "قبول الطلب وحجز المبلغ الإجمالي بمجرد التأكيد على كود التحقق المرسل", "actor": "طالب الخدمة", "preconditions": ["طالب الخدمة قبل العرض المقدم", "طالب الخدمة لديه المبلغ الكافي في المحفظة"], "postconditions": ["تم حجز المبلغ الإجمالي في المحفظة", "تم تأكيد الطلب"], "main_flow_steps": ["طالب الخدمة يضغط على خيار قبول العرض", "النظام يتحقق من وجود المبلغ الكافي في المحفظة", "النظام يرسل كود التحقق لطالب الخدمة", "طالب الخدمة يدخل كود التحقق", "النظام يحجز المبلغ الإجمالي في المحفظة", "النظام يؤكد الطلب"]}]}, "4.2.35": {"title": "استلام قطع الغيار", "business_goals": [], "stakeholders": ["مح<PERSON> بيع قطع الغيار", "مركبة التوصيل"], "main_steps": ["إعداد قطع الغيار للتسليم في المحل", "بمجرد وصول مركبة التوصيل، يقدم محل القطع رمز QR أو رمز التسليم.", "يتم فحص رمز QR أو رمز التسليم من قبل سائق مركبة التوصيل.", "بعد التحقق، تتم عملية استلام قطع الغيار."], "alternative_steps": [], "user_stories": ["المحل: عندما يصل سائق التوصيل, أريد أن أتمكن من تقديم قطع الغيار مع رمز QR أو رمز التسليم حتى يمكن التحقق من عملية التسليم.", "سائق مركبة التوصيل: عند وصولي للمحل، أريد أن أتمكن من فحص رمز QR أو رمز التسليم حتى يمكنني التأكد من اكتمال عملية استلام القطع الغيار."], "performance_indicators": ["عد<PERSON> الطلبات المستلمة و نسبتها لإجمالي الطلبات التي انشاء طلب توصيل لها."], "use_cases": [{"id": "4.2.35-1", "description": "محل بيع قطع الغيار يقوم بإعداد قطع الغيار للتسليم في المحل.", "actor": "مح<PERSON> بيع قطع الغيار", "preconditions": ["المح<PERSON> يتلقى طلب التوصيل من طالب الخدمة.", "قطع الغيار المطلوبة متوفرة في المحل."], "postconditions": ["قطع الغيار جاهزة للتسليم.", "انتظار وصول مركبة التوصيل."], "main_flow_steps": ["المح<PERSON> يتلقى طلب التوصيل.", "المحل يقوم بإعداد قطع الغيار المطلوبة.", "القطع تكون جاهزة للتسليم في الوقت المحدد."]}, {"id": "4.2.35-2", "description": "سائق مركبة التوصيل يصل إلى محل بيع قطع الغيار لاستلام القطع.", "actor": "سائق مركبة التوصيل", "preconditions": ["سائق مركبة التوصيل يتلقى طلب الاستلام.", "قطع الغيار جاهزة للتسليم في المحل."], "postconditions": ["قطع الغيار تم استلامها بنجاح.", "سائق مركبة التوصيل يبدأ في رحلة التسليم."], "main_flow_steps": ["سائق مركبة التوصيل يصل إلى المحل.", "سائق مركبة التوصيل يقدم رمز QR أو رمز التسليم.", "المحل يتحقق من رمز QR أو رمز التسليم.", "سائق مركبة التوصيل يستلم قطع الغيار بعد التحقق."]}, {"id": "4.2.35-3", "description": "المحل يقوم بالتحقق من رمز QR أو رمز التسليم المقدم من سائق مركبة التوصيل.", "actor": "مح<PERSON> بيع قطع الغيار", "preconditions": ["سائق مركبة التوصيل يصل إلى المحل.", "سائق مركبة التوصيل يقدم رمز QR أو رمز التسليم."], "postconditions": ["تم التحقق من رمز QR أو رمز التسليم.", "قطع الغيار تسلمت إلى سائق مركبة التوصيل."], "main_flow_steps": ["المحل يتحقق من رمز QR أو رمز التسليم.", "المحل يتأكد من صحة الرمز.", "المحل يسلم قطع الغيار إلى سائق مركبة التوصيل."]}]}, "4.2.36": {"title": "تسليم القطع وإغلاق طلب التوصيل", "business_goals": [], "stakeholders": ["طالب الخدمة", "مركبة التوصيل", "التطبيق"], "main_steps": ["مركبة التوصيل تصل إلى وجهتها وتقدم رمز (QR) لطالب الخدمة لتأكيد الاستلام.", "طالب الخدمة يمسح رمز QR لتأكيد الاستلام وإتمام عملية التسليم.", "يتم خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة بمجرد مسح رمز QR.", "أجرة التوصيل تحول مباشرة إلى حساب محفظة مركبة التوصيل فور التأكيد عبر رمز QR.", "يتم تحويل رسوم الخدمة وضريبة القيمة المضافة إلى حساب التطبيق فور تأكيد الاستلام."], "alternative_steps": ["في حال فشل مسح رمز QR، يجب توفير وسيلة يدوية لتأكيد استلام القطع وإتمام عملية التسليم."], "user_stories": ["طالب الخدمة: عند استلامي للقطع، أريد مسح رمز QR المقدم من سائق التوصيل لتأكيد الاستلام وضمان خصم المبلغ من حسابي بشكل صحيح.", "سائق مركبة التوصيل: عند تسليم القطعة، أريد تقديم رمز QR لطالب الخدمة لضمان تحويل أجرة التوصيل إلى حسابي فور تأكيده.", "التطبيق: أريد تسهيل عملية تحويل الأموال وتأكيد الخدمات من خلال استخدام رمز QR للحفاظ على دقة وفعالية المعاملات."], "performance_indicators": ["نسبة الطلبات التي تم تأكيدها بنجاح عبر رمز QR مقارنة بإجمالي طلبات التوصيل.", "متوسط الوقت المستغرق لإتمام عملية التسليم والدفع باستخدام رمز QR."], "use_cases": [{"id": "4.2.36-1", "description": "مركبة التوصيل تصل إلى وجهتها وتقدم رمز (QR) لطالب الخدمة لتأكيد الاستلام.", "actor": "مركبة التوصيل", "preconditions": ["مركبة التوصيل تحمل قطع الغيار.", "وجهة التسليم معروفة لمركبة التوصيل."], "postconditions": ["تأكيد استلام القطع.", "إتمام عملية التسليم."], "main_flow_steps": ["مركبة التوصيل تصل إلى وجهتها.", "مركبة التوصيل تقدم رمز QR لطالب الخدمة.", "طالب الخدمة يمسح رمز QR لتأكيد الاستلام."]}, {"id": "4.2.36-2", "description": "طالب الخدمة يمسح رمز QR لتأكيد الاستلام وإتمام عملية التسليم.", "actor": "طالب الخدمة", "preconditions": ["مركبة التوصيل تصل إلى وجهتها.", "رمز QR متوفر مع سائق مركبة التوصيل."], "postconditions": ["تأكيد استلام القطع.", "خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة."], "main_flow_steps": ["طالب الخدمة يستلم القطع.", "طالب الخدمة يمسح رمز QR.", "تأكيد الاستلام وإتمام عملية التسليم."]}, {"id": "4.2.36-3", "description": "يتم خصم المبلغ الإجمالي من حساب محفظة طالب الخدمة بمجرد مسح رمز QR.", "actor": "التطبيق", "preconditions": ["تأكيد الاستلام عبر رمز QR.", "توفر المبلغ المطلوب في حساب محفظة طالب الخدمة."], "postconditions": ["خصم المبلغ من حساب طالب الخدمة.", "تحويل أجرة التوصيل ورسوم الخدمة وضريبة القيمة المضافة."], "main_flow_steps": ["مسح <PERSON><PERSON>ز QR لتأكيد الاستلام.", "خصم المبلغ الإجمالي من حساب طالب الخدمة.", "تحويل أجرة التوصيل إلى حساب مركبة التوصيل.", "تحويل رسوم الخدمة وضريبة القيمة المضافة إلى حساب التطبيق."]}, {"id": "4.2.36-4", "description": "أجرة التوصيل تحول مباشرة إلى حساب محفظة مركبة التوصيل فور التأكيد عبر رمز QR.", "actor": "التطبيق", "preconditions": ["تأكيد الاستلام عبر رمز QR.", "خصم المبلغ الإجمالي من حساب طالب الخدمة."], "postconditions": ["تحويل أجرة التوصيل إلى حساب مركبة التوصيل."], "main_flow_steps": ["تأكيد الاستلام عبر رمز QR.", "خصم المبلغ من حساب طالب الخدمة.", "تحويل أجرة التوصيل إلى حساب مركبة التوصيل."]}, {"id": "4.2.36-5", "description": "يتم تحويل رسوم الخدمة وضريبة القيمة المضافة إلى حساب التطبيق فور تأكيد الاستلام.", "actor": "التطبيق", "preconditions": ["تأكيد الاستلام عبر رمز QR.", "خصم المبلغ الإجمالي من حساب طالب الخدمة."], "postconditions": ["تحويل رسوم الخدمة وضريبة القيمة المضافة إلى حساب التطبيق."], "main_flow_steps": ["تأكيد الاستلام عبر رمز QR.", "خصم المبلغ من حساب طالب الخدمة.", "تحويل رسوم الخدمة وضريبة القيمة المضافة إلى حساب التطبيق."]}, {"id": "4.2.36-6", "description": "في حال فشل مسح رمز QR، يجب توفير وسيلة يدوية لتأكيد استلام القطع وإتمام عملية التسليم.", "actor": "التطبيق", "preconditions": ["مشكلة في مسح رمز QR.", "تواصل مع خدمة العملاء."], "postconditions": ["تأكيد استلام القطع يدوياً.", "إتمام عملية التسليم."], "main_flow_steps": ["فشل مسح رمز QR.", "تواصل مع خدمة العملاء.", "توفير وسيلة يدوية لتأكيد الاستلام.", "إتمام عملية التسليم يدوياً."]}]}, "4.2.37": {"title": "قبول القطع / رفض القطع", "business_goals": [], "stakeholders": ["طالب الخدمة", "م<PERSON><PERSON> قطع الغيار"], "main_steps": ["قبول القطع وسداد المستحقات وتحويل المبالغ المحجوزة من طالب الخدمة لحساب محفظة محل قطع الغيار.", "في حال الرفض، يقوم طالب الخدمة بإنشاء طلب إعادة القطع من خلال طلب توصيل بنفس آلية توصيله مع تغير الأشخاص بين طالب الخدمة ومزود الخدمة.", "يبقى المبلغ تحت الحجز لحين إرجاع القطع ويتم تحرير المبلغ من حساب محفظة طالب الخدمة بعد تأكيد استلام المحل للقطع.", "رسوم الخدمة تبقى مستحقة ويتم خصمها عند انتهاء خدمة الطلب."], "alternative_steps": [], "user_stories": ["كعميل طالب الخدمة، يستطيع قبول القطع وسداد المستحقات وتحويل المبالغ المحجوزة من حسابه لحساب محل قطع الغيار.", "كعميل طالب الخدمة، يستطيع في حال رفضه للقطع إنشاء طلب إعادة القطع من خلال طلب توصيل بنفس الآلية.", "كعميل طالب الخدمة، يستطيع تحرير المبلغ المحجوز من حساب محفظته بعد تأكيد استلام المحل للقطع."], "performance_indicators": ["إحصاء عدد الطلبات المقبولة والمرفوضة وحساب نسبتهما إلى إجمالي الطلبات المُسلَمة.", "ع<PERSON><PERSON> الطلبات التي تم إعادتها ونسبتها للطلبات المرفوضة."], "use_cases": [{"id": "4.2.37-1", "description": "طالب الخدمة يقوم بقبول قطع الغيار وسداد المستحقات.", "actor": "طالب الخدمة", "preconditions": ["قطع الغيار متاحة للاستلام.", "المبلغ محجوز في محفظة طالب الخدمة."], "postconditions": ["تحويل المبلغ إلى حساب محل قطع الغيار.", "تأكيد استلام قطع الغيار."], "main_flow_steps": ["طالب الخدمة يستلم قطع الغيار.", "طالب الخدمة يؤكد قبول القطع.", "يتم سداد المستحقات.", "تحويل المبالغ المحجوزة من حساب طالب الخدمة إلى حساب محل قطع الغيار."]}, {"id": "4.2.37-2", "description": "طالب الخدمة يقوم برفض قطع الغيار وإنشاء طلب إعادة القطع.", "actor": "طالب الخدمة", "preconditions": ["قطع الغيار متاحة للاستلام.", "المبلغ محجوز في محفظة طالب الخدمة."], "postconditions": ["إنشاء طلب إعادة القطع.", "المبلغ يبقى محجوزاً حتى استلام المحل للقطع."], "main_flow_steps": ["طالب الخدمة يستلم قطع الغيار.", "طالب الخدمة يرفض القطع.", "طالب الخدمة ينشئ طلب إعادة القطع.", "يتم تحرير المبلغ من حساب طالب الخدمة بعد تأكيد استلام المحل للقطع."]}, {"id": "4.2.37-3", "description": "تحويل رسوم الخدمة بعد قبول أو رفض القطع.", "actor": "النظام", "preconditions": ["قطع الغيار تم قبولها أو رفضها من قبل طالب الخدمة.", "المبلغ محجوز في محفظة طالب الخدمة."], "postconditions": ["تحويل رسوم الخدمة إلى حساب التطبيق.", "تحويل ضريبة القيمة المضافة إلى حساب التطبيق."], "main_flow_steps": ["قبول أو رفض القطع من قبل طالب الخدمة.", "تحويل رسوم الخدمة إلى حساب التطبيق.", "تحويل ضريبة القيمة المضافة إلى حساب التطبيق."]}]}, "4.2.38": {"title": "تقييم وارشفة خدمة قطع غيار", "business_goals": [], "stakeholders": ["العميل", "م<PERSON><PERSON> قطع الغيار"], "main_steps": ["كل طرف يقوم بتقييم الطرف الآخر بناء على الخدمة التي تم تقديمها.", "تعبئة نموذج التقييم المقدم من المنصة بالنجوم والتعليق.", "أرشفة البيانات الأساسية للحالة في سجلات الطلبات لكل طرف.", "يتم أرشفة كافة بيانات الحالة في سجلات المنصة بمجرد إنهاء الخدمة."], "alternative_steps": [], "user_stories": ["العميل طالب الخدمة، يستطيع تقييم الخدمة التي تقدمها محل قطع الغيار وكتابة تعليق إذا أراد.", "العميل محل قطع الغيار، يستطيع تقييم العميل استنادًا إلى تجربة الخدمة.", "العميل طالب الخدمة و محل قطع الغيار، يستطيع الاطلاع على تفاصيل الخدمة السابقة من خلال سجل الطلبات في أي لحظة.", "المنصة تقوم بأرشفة كافة بيانات الحالة في سجلات المنصة بمجرد إنهاء الخدمة."], "performance_indicators": ["عد<PERSON> الطلبات التي تم تقييمها و نسبتها لإجمالي الطلبات المكتملة.", "معدل تقييم الخدمة الإجمالي."], "use_cases": [{"id": "4.2.38-1", "description": "العميل يقوم بتقييم الخدمة التي قدمها محل قطع الغيار وكتابة تعليق إذا أراد.", "actor": "العميل", "preconditions": ["انتهاء الخدمة بنجاح", "وجود حساب للعميل على المنصة"], "postconditions": ["إضافة تقييم العميل في سجلات الطلبات", "إتاحة التقييم للعامة"], "main_flow_steps": ["العميل يفتح صفحة الطلبات السابقة.", "العميل يختار الطلب الذي يرغب في تقييمه.", "العميل يعبئ نموذج التقييم المقدم من المنصة بالنجوم ويكتب تعليقاً إذا أراد.", "العميل يرسل التقييم.", "المنصة تقوم بأرشفة التقييم وربطه بالطلب المعني."]}, {"id": "4.2.38-2", "description": "محل قطع الغيار يقوم بتقييم العميل استنادًا إلى تجربة الخدمة.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": ["انتهاء الخدمة بنجاح", "وجود حسا<PERSON> لم<PERSON><PERSON> قطع الغيار على المنصة"], "postconditions": ["إضافة تقييم محل قطع الغيار في سجلات الطلبات", "إتاحة التقييم للعامة"], "main_flow_steps": ["محل قطع الغيار يفتح صفحة الطلبات السابقة.", "محل قطع الغيار يختار الطلب الذي يرغب في تقييمه.", "محل قطع الغيار يعبئ نموذج التقييم المقدم من المنصة بالنجوم ويكتب تعليقاً إذا أراد.", "م<PERSON><PERSON> قطع الغيار يرسل التقييم.", "المنصة تقوم بأرشفة التقييم وربطه بالطلب المعني."]}, {"id": "4.2.38-3", "description": "العميل ومحل قطع الغيار يستطيعان الاطلاع على تفاصيل الخدمة السابقة من خلال سجل الطلبات.", "actor": "المستخدم", "preconditions": ["وجود حساب لكل من العميل ومحل قطع الغيار على المنصة", "وجود طلبات سابقة"], "postconditions": ["إتاحة تفاصيل الخدمة في أي وقت"], "main_flow_steps": ["العميل أو محل قطع الغيار يفتح صفحة الطلبات السابقة.", "يتم عرض تفاصيل الطلبات السابقة بما في ذلك التقييمات والتعليقات.", "العميل أو محل قطع الغيار يمكنه استعراض تفاصيل كل طلب على حدة."]}, {"id": "4.2.38-4", "description": "المنصة تقوم بأرشفة كافة بيانات الخدمة في سجلات المنصة بمجرد إنهاء الخدمة.", "actor": "المنصة", "preconditions": ["انتهاء الخدمة بنجاح", "إرسال التقييم من قبل العميل ومحل قطع الغيار"], "postconditions": ["حفظ جميع بيانات الحالة في سجلات المنصة"], "main_flow_steps": ["المنصة تتلقى التقييمات والتعليقات من العميل ومحل قطع الغيار.", "المنصة تقوم بربط التقييمات بالطلبات المعنية.", "المنصة تحفظ جميع البيانات في سجلات الطلبات.", "المنصة تتيح البيانات للمراجعة عند الحاجة."]}]}, "4.2.39": {"title": "طلب تخليص جمركي", "business_goals": [], "stakeholders": ["العميل", "المخلص الجمركي"], "main_steps": ["يقوم العميل بإرفاق وثائق التخليص الجمركي مثل شهادة المنشأ، فاتورة الحمولة، وثيقة التعبئة، شهادة الزراعة، الخ.", "العميل يحدد المنفذ الجمركي المطلوب.", "العميل يختار آلية إسناد الطلب وهي إما تخصيص مخلص جمركي أو النشر العام للطلب على المنصة.", "العميل يقوم بإرسال الطلب."], "alternative_steps": ["العميل يمكن أن يحتاج إلى تعديل الوثائق المرفقة أو المنفذ الجمركي بعد إرسال الطلب."], "user_stories": ["المستخدم، العميل، يستطيع رفع وثائق التخليص الجمركي وتحديد المنفذ الجمركي المطلوب.", "المستخدم، العميل، يستطيع اختيار آلية إسناد الطلب إما بتخصيص مخلص جمركي أو النشر العام للطلب على المنصة.", "المستخدم، العميل، يستطيع إرسال الطلب للتخليص الجمركي.", "المستخدم، يمكن للعميل تعديل الوثائق المرفقة أو المنفذ الجمركي المحدد بعد إرسال الطلب، إذا دعت الحاجة.", "المستخدم, المخلص الجمركي لديه القدرة على مراجعة الطلبات وقبولها أو رفضها بناءً على النشاط العام للطلبات الجمركية."], "performance_indicators": ["عدد طلبات التخليص الجمركي."], "use_cases": [{"id": "4.2.39-1", "description": "العميل يقوم بإرفاق وثائق التخليص الجمركي مثل شهادة المنشأ، فاتورة الحمولة، وثيقة التعبئة، شهادة الزراعة، الخ.", "actor": "العميل", "preconditions": ["توفر الوثائق اللازمة", "وجود حساب للعميل على المنصة"], "postconditions": ["إرسال الطلب بنجاح", "إتاحة الوثائق للمخلص الجمركي"], "main_flow_steps": ["العميل يفتح صفحة طلب التخليص الجمركي.", "العميل يرفق الوثائق المطلوبة (شهادة المنشأ، فاتورة الحمولة، وثيقة التعبئة، شهادة الزراعة).", "العميل يحدد المنفذ الجمركي المطلوب.", "العميل يختار آلية إسناد الطلب (تخصيص مخلص جمركي أو النشر العام للطلب على المنصة).", "العميل يرسل الطلب."]}, {"id": "4.2.39-2", "description": "العميل يحدد المنفذ الجمركي المطلوب.", "actor": "العميل", "preconditions": ["توفر الوثائق اللازمة", "وجود حساب للعميل على المنصة"], "postconditions": ["تحديد المن<PERSON>ذ الجمركي في الطلب"], "main_flow_steps": ["العميل يفتح صفحة طلب التخليص الجمركي.", "العميل يرفق الوثائق المطلوبة.", "العميل يحدد المنفذ الجمركي المطلوب."]}, {"id": "4.2.39-3", "description": "العميل يختار آلية إسناد الطلب وهي إما تخصيص مخلص جمركي أو النشر العام للطلب على المنصة.", "actor": "العميل", "preconditions": ["توفر الوثائق اللازمة", "وجود حساب للعميل على المنصة"], "postconditions": ["تحديد آلية إسناد الطلب في النظام"], "main_flow_steps": ["العميل يفتح صفحة طلب التخليص الجمركي.", "العميل يرفق الوثائق المطلوبة.", "العميل يحدد المنفذ الجمركي المطلوب.", "العميل يختار آلية إسناد الطلب (تخصيص مخلص جمركي أو النشر العام للطلب على المنصة)."]}, {"id": "4.2.39-4", "description": "العميل يقوم بإرسال الطلب.", "actor": "العميل", "preconditions": ["إرفاق جميع الوثائق المطلوبة", "تحديد المن<PERSON>ذ الجمركي وآلية إسناد الطلب"], "postconditions": ["إرسال الطلب إلى المخلص الجمركي"], "main_flow_steps": ["العميل يفتح صفحة طلب التخليص الجمركي.", "العميل يرفق الوثائق المطلوبة.", "العميل يحدد المنفذ الجمركي المطلوب.", "العميل يختار آلية إسناد الطلب.", "العميل يرسل الطلب."]}, {"id": "4.2.39-5", "description": "العميل يمكن أن يحتاج إلى تعديل الوثائق المرفقة أو المنفذ الجمركي بعد إرسال الطلب.", "actor": "العميل", "preconditions": ["وجود طلب قيد المراجعة"], "postconditions": ["تحديث الوثائق أو المنفذ الجمركي في الطلب"], "main_flow_steps": ["العميل يفتح صفحة الطلبات الحالية.", "العميل يختار الطلب الذي يريد تعديله.", "العميل يعدل الوثائق المرفقة أو المنفذ الجمركي.", "العميل يحفظ التعديلات."]}]}, "4.2.40": {"title": "إرسال الطلب", "business_goals": [], "stakeholders": ["جهات التخليص الجمركي", "الناقل", "العميل", "نظام E-SHLF"], "main_steps": ["الناقل يقوم بإرسال الطلب إلى جهات التخليص الجمركي عن طريق المنصة.", "جهات التخليص تقوم بمراجعة الطلب والوثائق المرفقة.", "إصدار السعر للطلب من قبل جهات التخليص بما في ذلك: مبلغ الأجرة، رسوم خدمة التخليص، ضريبة رسوم التخليص، والمبلغ الصافي المستحق للمخلص بعد خصم الرسوم من الأجرة."], "alternative_steps": [], "user_stories": ["الناقل، يرسل الطلب: الناقل يحتاج إلى إرسال الطلب والوثائق المرفقة لجهات التخليص الجمركي عبر المنصة، حتى يتمكن من تسهيل عملية النقل والتخليص الجمركي.", "جهة التخليص، تقوم بمراجعة الطلب: جهة التخليص تحتاج إلى مراجعة الطلب والوثائق المرفقة، الشيء الذي يعزز العمليات الجمركية الصحيحة والكفاءة.", "جهة التخليص، تصدر السعر للطلب: بعد مراجعة الطلب، تحتاج جهة التخليص الجمركي إلى تحديد الأجرة، رسوم خدمة التخليص، ضريبة رسوم التخليص، والمبلغ الصافي المستحق للمخلص بعد خصم الرسوم من الأجرة. ذلك يتيح للعميل فهم تكاليف الخدمة المقدمة وتسهيل عملية الدفع.", "العميل، يقوم بالدفع الإلكتروني للخدمة: بناءً على سعر الطلب الصادر من جهة التخليص، يحتاج العميل إلى سداد المبلغ عبر الدفع الإلكتروني في المنصة، حتى يتمكن من استكمال الخدمة."], "performance_indicators": ["عدد طلبات التخليص التي تم الاستجابة لها و نسبتها لإجمالي الطلبات.", "عد<PERSON> المستجيبين لطلبات التخليص."], "use_cases": [{"id": "4.2.40-1", "description": "الناقل يقوم بإرسال الطلب إلى جهات التخليص الجمركي عن طريق المنصة.", "actor": "الناقل", "preconditions": ["وجود حساب للناقل على المنصة", "وجود طلب تخليص جمركي مكتمل"], "postconditions": ["إرسال الطلب إلى جهات التخليص الجمركي", "تأكيد استلام الطلب من جهة التخليص"], "main_flow_steps": ["الناقل يفتح صفحة الطلبات الجمركية على المنصة.", "الناقل يختار الطلب المكتمل الذي يرغب في إرساله.", "الناقل يضغط على 'إرسال الطلب' للبدء في عملية الإرسال.", "المنصة ترسل الطلب والوثائق المرفقة إلى جهة التخليص الجمركي.", "الناقل يتلقى تأكيدًا من المنصة على استلام الطلب من قبل جهة التخليص."]}, {"id": "4.2.40-2", "description": "جهات التخليص تقوم بمراجعة الطلب والوثائق المرفقة.", "actor": "جهة التخليص الجمركي", "preconditions": ["استلام الطلب والوثائق المرفقة من الناقل", "وجود حساب لجهة التخليص الجمركي على المنصة"], "postconditions": ["مراجعة الطلب وتحديد الرسوم المستحقة"], "main_flow_steps": ["جهة التخليص الجمركي تتلقى الطلب عبر المنصة.", "جهة التخليص تفتح الطلب والوثائق المرفقة.", "جهة التخليص تقوم بمراجعة الوثائق والتأكد من صحتها.", "جهة التخليص تحدد الأجرة، رسوم خدمة التخليص، ضريبة رسوم التخليص.", "جهة التخليص تحدد المبلغ الصافي المستحق للمخلص بعد خصم الرسوم من الأجرة."]}, {"id": "4.2.40-3", "description": "إصدار السعر للطلب من قبل جهات التخليص بما في ذلك: مبلغ الأجرة، رسوم خدمة التخليص، ضريبة رسوم التخليص، والمبلغ الصافي المستحق للمخلص بعد خصم الرسوم من الأجرة.", "actor": "جهة التخليص الجمركي", "preconditions": ["مراجعة الطلب والوثائق المرفقة", "تحديد الرسوم المستحقة"], "postconditions": ["إصدار الفاتورة النهائية", "إبلاغ الناقل بالسعر النهائي"], "main_flow_steps": ["جهة التخليص الجمركي تقوم بتحديد الأجرة ورسوم الخدمة والضريبة.", "جهة التخليص تصدر الفاتورة النهائية.", "الناقل يتلقى إشعارًا بالسعر النهائي للخدمة.", "الناقل يراجع الفاتورة ويوافق على الدفع.", "يتم إصدار الفاتورة الرسمية من قبل جهة التخليص."]}]}, "4.2.41": {"title": "الموافقة على العرض", "business_goals": [], "stakeholders": ["العميل", "النظام"], "main_steps": ["استلام العرض من مقدمي الخدمات", "احتساب رسوم الخدمة على طالب الخدمة", "احتساب الضريبة على الرسوم", "حساب المبلغ الإجمالي الذي يتضمن (أجرة التخليص+رسوم الخدمة+الضريبة)", "الموافقة على العرض والمبلغ الإجمالي للخدمة"], "alternative_steps": [], "user_stories": ["العميل, بعد استلام العروض من مقدمي الخدمات، يستعرض العروض ويختار الأفضل بناءً على معاييره الخاصة (السعر، التقييم، الجودة، السرعة). بعد الاختيار، يعرض النظام تفاصيل العرض بشكل مفصل، بما في ذلك أجرة التخليص، رسوم الخدمة، الضريبة والمبلغ الإجمالي المطلوب دفعه. بعد الرضاء عن هذه البنود، يقوم طالب الخدمة بالموافقة على العرض ويستعد للدفع.", "النظام, يقوم بالعمليات الحسابية لرسوم الخدمة (الضريبة والمبلغ الإجمالي). ويعرض هذه التفاصيل لطالب الخدمة لاستعراضها و استكمال الطلب. يحدث النظام حالة العرض لتوضيح أنه تم القبول."], "performance_indicators": ["عد<PERSON> الطلبات التي تم الموافقة عليها ونسبتها إجمالي الطلبات التي تمت الاستجابة لها."], "use_cases": [{"id": "4.2.41-1", "description": "استلام العرض من مقدمي الخدمات", "actor": "العميل", "preconditions": ["أن يكون العميل قد أرسل طلبه إلى مقدمي الخدمات."], "postconditions": ["يتمكن العميل من رؤية كافة العروض المستلمة من مقدمي الخدمات."], "main_flow_steps": ["العميل يستلم العروض المقدمة من مقدمي الخدمات."]}, {"id": "4.2.41-2", "description": "احتساب رسوم الخدمة على طالب الخدمة", "actor": "النظام", "preconditions": ["أن يكون العميل قد استلم العروض من مقدمي الخدمات."], "postconditions": ["النظام يعرض رسوم الخدمة لطالب الخدمة."], "main_flow_steps": ["النظام يحتسب رسوم الخدمة بناءً على العروض المستلمة."]}, {"id": "4.2.41-3", "description": "احتساب الضريبة على الرسوم", "actor": "النظام", "preconditions": ["احتساب رسوم الخدمة على طالب الخدمة."], "postconditions": ["النظام يعرض الضريبة المحسوبة على الرسوم لطالب الخدمة."], "main_flow_steps": ["النظام يحتسب الضريبة على رسوم الخدمة."]}, {"id": "4.2.41-4", "description": "حساب المبلغ الإجمالي الذي يتضمن (أجرة التخليص+رسوم الخدمة+الضريبة)", "actor": "النظام", "preconditions": ["احتساب رسوم الخدمة والضريبة على الرسوم."], "postconditions": ["عرض المبلغ الإجمالي لطالب الخدمة."], "main_flow_steps": ["النظام يحسب المبلغ الإجمالي الذي يتضمن أجرة التخليص، رسوم الخدمة، والضريبة."]}, {"id": "4.2.41-5", "description": "الموافقة على العرض والمبلغ الإجمالي للخدمة", "actor": "العميل", "preconditions": ["عرض المبلغ الإجمالي لطالب الخدمة."], "postconditions": ["النظام يحدث حالة العرض لتوضيح أنه تم القبول."], "main_flow_steps": ["العميل يستعرض العرض والمبلغ الإجمالي.", "العميل يوافق على العرض والمبلغ الإجمالي."]}]}, "4.2.42": {"title": "اتمام الصفقة", "business_goals": [], "stakeholders": ["طالب الخدمة", "مقدم الخدمة", "المخلص الجمركي"], "main_steps": ["قبول الصفقة النهائية من طالب الخدمة", "التأكد من توفر المبلغ في المحفظة الخاصة بطالب الخدمة", "الحجز على المبلغ الإجمالي المتفق عليه وتجميده ضمن حساب طالب الخدمة", "في حالة عدم توفر المبلغ في المحفظة، يتم إضافة المبلغ للمحفظة من أحد وسائل الدفع الإلكترونية والحجز عليه مباشرة لإتمام الطلب"], "alternative_steps": ["يمكن إلغاء الطلب من قبل طالب الخدمة قبل تفعيل الربط مع المخلص الجمركي.", "يمكن إلغاء الطلب من طرف المخلص الجمركي قبل او بعد تفعيل الربط (ويتم إشعار طالب الخدمة)."], "user_stories": ["طالب الخدمة: بعد رفع طلب النقل، يمكنني تلقي العروض من مقدمي الخدمة واختيار العرض المناسب بناءً على السعر و الوقت المتوقع للتسليم، ثم أقوم بتأكيد الصفقة ودفع المبلغ المطلوب.", "مقدم الخدمة: بعد قبول العميل للعرض، أتلقى إشعارا بقبول الطلب وأتأكد من دفع العميل للمبلغ المتفق عليه. إذا لم يكن المبلغ متاحًا في المحفظة، يتم إضافته من وسائل الدفع الإلكترونية والحجز عليه مباشرة لإتمام الطلب."], "performance_indicators": ["عدد الطلبات الملغاة من (طالب الخدمة/مقدم الخدمة).", "عد<PERSON> الطلبات التي تمت الصفقة لها و نسبتها لإجمالي الطلبات المقبولة."], "use_cases": [{"id": "4.2.42-1", "description": "قبول الصفقة النهائية من طالب الخدمة", "actor": "طالب الخدمة", "preconditions": ["استلام طالب الخدمة للعرض والمبلغ الإجمالي للخدمة"], "postconditions": ["يتم تحديث حالة الطلب إلى 'مقبول'"], "main_flow_steps": ["طالب الخدمة يستعرض العرض النهائي.", "طالب الخدمة يقبل الصفقة النهائية."]}, {"id": "4.2.42-2", "description": "التأكد من توفر المبلغ في المحفظة الخاصة بطالب الخدمة", "actor": "النظام", "preconditions": ["قبول طالب الخدمة للصفقة النهائية"], "postconditions": ["النظام يعرض رسالة تأكيد توفر المبلغ في المحفظة"], "main_flow_steps": ["النظام يتحقق من توفر المبلغ المطلوب في المحفظة الخاصة بطالب الخدمة."]}, {"id": "4.2.42-3", "description": "الحجز على المبلغ الإجمالي المتفق عليه وتجميده ضمن حساب طالب الخدمة", "actor": "النظام", "preconditions": ["تأكيد توفر المبلغ في المحفظة الخاصة بطالب الخدمة"], "postconditions": ["النظام يحجز المبلغ ويجمده ضمن حساب طالب الخدمة"], "main_flow_steps": ["النظام يحجز المبلغ الإجمالي المتفق عليه.", "النظام يجمد المب<PERSON>غ ضمن حساب طالب الخدمة."]}, {"id": "4.2.42-4", "description": "في حالة عدم توفر المبلغ في المحفظة، يتم إضافة المبلغ للمحفظة من أحد وسائل الدفع الإلكترونية والحجز عليه مباشرة لإتمام الطلب", "actor": "طالب الخدمة", "preconditions": ["النظام يتحقق من عدم توفر المبلغ المطلوب في المحفظة الخاصة بطالب الخدمة"], "postconditions": ["المبلغ يضا<PERSON> إل<PERSON> المحفظة ويتم الحجز عليه"], "main_flow_steps": ["النظام يعرض رسالة لعدم توفر المبلغ في المحفظة.", "طالب الخدمة يضيف المبلغ المطلوب إلى المحفظة من خلال وسائل الدفع الإلكترونية.", "النظام يحجز المبلغ مباشرة بعد إضافته إلى المحفظة."]}, {"id": "4.2.42-5", "description": "إلغاء الطلب من قبل طالب الخدمة قبل تفعيل الربط مع المخلص الجمركي", "actor": "طالب الخدمة", "preconditions": ["الصفقة لم يتم تفعيلها بعد مع المخلص الجمركي"], "postconditions": ["الطلب يتم إلغاؤه ويعود المبلغ المحجوز إلى المحفظة"], "main_flow_steps": ["طالب الخدمة يطلب إلغاء الطلب قبل تفعيل الربط.", "النظام يلغى الطلب ويعيد المبلغ المحجوز إلى المحفظة."]}, {"id": "4.2.42-6", "description": "إلغاء الطلب من طرف المخلص الجمركي قبل أو بعد تفعيل الربط", "actor": "المخلص الجمركي", "preconditions": ["الصفقة في حالة عدم التنفيذ أو التنفيذ الجزئي"], "postconditions": ["الطلب يتم إلغاؤه ويتم إشعار طالب الخدمة"], "main_flow_steps": ["المخلص الجمركي يطلب إلغاء الطلب.", "النظام يلغى الطلب ويشعر طالب الخدمة بالإلغاء."]}]}, "4.2.43": {"title": "تفعيل الربط", "business_goals": [], "stakeholders": ["طالب الخدمة", "المخلص"], "main_steps": ["توليد رمز QR أو الكود النصي من قبل النظام.", "إرسال الرمز أو الكود إلى طالب الخدمة.", "من ثم، يقوم طالب الخدمة بمشاركة الكود أو الرمز مع المخلص.", "يقوم المخلص بتحديد الرمز أو إدخال الكود لتفعيل الربط مع طالب الخدمة."], "alternative_steps": ["واحدة من الخطوات البديلة الشائعة هي استخدام البريد الإلكتروني أو الرسائل النصية لتبادل الرمز او الكود"], "user_stories": ["طالب الخدمة: بعد قبول الصفقة، يمكنني الحصول على رمز او كود لتفعيل الربط مع المخلص، حيث يمكنني مشاركته مع المخلص لبدء الاتصال المباشر معه.", "المخلص: بعد تلقي الرمز أو الكود من طالب الخدمة، أتمكن من تفعيل الربط معه وبدء التنسيق مباشرة معه."], "performance_indicators": ["عد<PERSON> الطلبات التي تفعيل الربط لها ونسبتها إجمالي الطلبات التي تم إتمام الصفقة لها."], "use_cases": [{"id": "4.2.43-1", "description": "توليد رمز QR أو الكود النصي من قبل النظام", "actor": "النظام", "preconditions": ["قبول طالب الخدمة للعرض النهائي."], "postconditions": ["يتم توليد رمز QR أو الكود النصي."], "main_flow_steps": ["النظام يولد رمز QR أو الكود النصي."]}, {"id": "4.2.43-2", "description": "إرسال الرمز أو الكود إلى طالب الخدمة", "actor": "النظام", "preconditions": ["توليد رمز QR أو الكود النصي."], "postconditions": ["طالب الخدمة يتلقى الرمز أو الكود."], "main_flow_steps": ["النظام يرسل رمز QR أو الكود النصي إلى طالب الخدمة."]}, {"id": "4.2.43-3", "description": "مشاركة الكود أو الرمز مع المخلص", "actor": "طالب الخدمة", "preconditions": ["تلقي طالب الخدمة للرمز أو الكود."], "postconditions": ["المخلص يتلقى الرمز أو الكود."], "main_flow_steps": ["طالب الخدمة يشارك الرمز أو الكود مع المخلص."]}, {"id": "4.2.43-4", "description": "تحديد الر<PERSON>ز أو إدخال الكود لتفعيل الربط مع طالب الخدمة", "actor": "المخلص", "preconditions": ["المخلص يتلقى الرمز أو الكود من طالب الخدمة."], "postconditions": ["تفعيل الربط مع طالب الخدمة."], "main_flow_steps": ["المخلص يحد<PERSON> الر<PERSON>ز أو يدخل الكود لتفعيل الربط مع طالب الخدمة."]}, {"id": "4.2.43-5", "description": "استخدام البريد الإلكتروني أو الرسائل النصية لتبادل الرمز أو الكود", "actor": "النظام", "preconditions": ["عدم تمكن طالب الخدمة أو المخلص من استخدام الطرق العادية لتبادل الرمز أو الكود."], "postconditions": ["النظام يمكن الأطراف من تبادل الرمز أو الكود بطرق بديلة."], "main_flow_steps": ["النظام يوفر خيار استخدام البريد الإلكتروني أو الرسائل النصية لتبادل الرمز أو الكود."]}]}, "4.2.44": {"title": "إتمام عملية التخليص", "business_goals": [], "stakeholders": ["طالب الخدمة", "المخلص الجمركي"], "main_steps": ["المخلص الجمركي يرفق الوثائق التي تثبت إتمام عملية التخليص ثم يرسلها إلى طالب الخدمة.", "طالب الخدمة يقرأ التحقق ويقرر إما الموافقة على الانتهاء (وبالتالي يتم سداد المخلص)، أو الرفض (مع توضيح السبب) وإرسال الطلب مرة أخرى إلى المخلص الجمركي ما لم يتم خروج الحمولة من منفذ التخليص", "يتم إعطاء مهلة زمنية محددة للاستجابة من طالب الخدمة، وبعد ذلك يتم اعتبار الخدمة مكتملة والدفع للمخلص تلقائيا.", "يمكن اعتبار التخليص مكتملًا تلقائياً في حالة خروج الحمولة من منفذ التخليص."], "alternative_steps": ["في حالة عدم الرد من طالب الخدمة، يمكن استخدام الملاحظات الآلية لمغادرة المركبة من منفذ التخليص كمؤشر لإكمال العملية."], "user_stories": ["المخلص الجمركي: عق<PERSON> إتمام عملية التخليص الجمركي، أستطيع تحميل الوثائق الداعمة وإرسالها إلى طالب الخدمة لتأكيد الانتهاء من العملية.", "طالب الخدمة: عند تلقي الوثائق التي تثبت إتمام العملية، أملك القدرة على الموافقة وسداد مستحقات المخلص، أو أنا أستطيع رفض المصادقة وتوفير سبب لرفضي. إذا لم أستجب في الفترة الزمنية المحددة، يمكن للنظام الموافقة تلقائيًا والتأكد من دفع المخلص."], "performance_indicators": ["عد<PERSON> الطلبات المكتملة و نسبتها لإجمالي الطلبات.", "ع<PERSON><PERSON> الطلبات المعلقة."], "use_cases": [{"id": "4.2.44-1", "description": "Attach customs clearance documents and send to the service requester.", "actor": "Customs Broker", "preconditions": ["Customs clearance process completed", "Documents prepared and verified"], "postconditions": ["Documents sent to the service requester", "Awaiting approval from the requester"], "main_flow_steps": ["Attach customs clearance documents (proof of completion) to the system.", "Send the documents to the service requester for review."]}, {"id": "4.2.44-2", "description": "Approve or reject the customs clearance completion.", "actor": "Service Requester", "preconditions": ["Received customs clearance documents from the customs broker"], "postconditions": ["Customs broker paid if approved", "Request sent back to the customs broker if rejected"], "main_flow_steps": ["Review the customs clearance documents.", "Approve the documents if satisfactory, triggering payment to the customs broker.", "If not satisfactory, reject the documents and provide a reason for the rejection.", "Send the request back to the customs broker for re-evaluation if necessary."]}, {"id": "4.2.44-3", "description": "Automatically complete customs clearance after a specified time or upon exit from the customs checkpoint.", "actor": "System", "preconditions": ["Customs clearance documents submitted", "Waiting for service requester response"], "postconditions": ["Customs broker automatically paid if time elapses without response", "Clearance considered complete"], "main_flow_steps": ["Start countdown timer upon document submission.", "Monitor the response time from the service requester.", "Automatically mark the clearance as complete and trigger payment if the response time exceeds the set limit."]}]}, "4.2.45": {"title": "اغلاق الحالة", "business_goals": [], "stakeholders": ["طالب الخدمة", "مقدم الخدمة (الورشة الفنية / محل قطع الغيار / المخلص الجمركي)", "التطبيق"], "main_steps": ["تحصيل المبلغ الإجمالي من حساب طالب الخدمة بعد تأكيد كود الموافقة", "تحويل أجرة الإصلاح الى حساب الورشة الفنية", "تحويل رسوم الخدمة إلى حساب التطبيق تحت مسمى رسوم خدمات مساندة", "تحويل ضريبة القيمة المضافة الى حساب الضريبة في التطبيق"], "alternative_steps": [], "user_stories": ["طالب الخدمة: بم<PERSON>رد اتمام الخدمة، يتم خصم المبلغ الإجمالي المتفق عليه من حسابي.", "مقدم الخدمة: أستلم اجرة الخدمة التي قدمتها بمجرد إغلاق الحالة مع خصم الرسوم الخاصة بالتطبيق.", "التطبيق: ب<PERSON><PERSON>رد اتمام الخدمة، يتم تحويل رسوم الخدمة وضريبة القيمة المضافة الى الحسابات المعنية في التطبيق."], "performance_indicators": ["عد<PERSON> الطلبات المغلقة ونسبتها للطلبات التي إتمامها.", "ع<PERSON><PERSON> الطلبات المغلقة ونسبتها إجمالي الطلبات التي تم إنشاؤها من الأساس.", "قيمة إجمالي الأجور وتفاصيل رسوم الخدمة والضريبة."], "use_cases": [{"id": "4.2.45-1", "description": "Collect the total amount from the service requester’s account after confirmation of the approval code.", "actor": "System", "preconditions": ["Service completion confirmed", "Approval code provided by the service requester"], "postconditions": ["Total amount collected from the service requester’s account", "Service provider paid", "Platform service fee and VAT transferred"], "main_flow_steps": ["Generate and send approval code to the service requester.", "Service requester provides the approval code to confirm service completion.", "Collect the total amount from the service requester’s account.", "Transfer the service fee to the service provider’s account.", "Transfer the platform service fee to the platform account.", "Transfer the VAT to the tax account."]}, {"id": "4.2.45-2", "description": "Receive the service fee upon closure of the service case.", "actor": "Service Provider (workshop / spare parts store / customs broker)", "preconditions": ["Service completion confirmed", "System collected the total amount from the service requester"], "postconditions": ["Service provider’s account credited with the service fee"], "main_flow_steps": ["Confirm service completion to the system.", "Verify receipt of the service fee in the service provider’s account after the system transfers the amount."]}, {"id": "4.2.45-3", "description": "Ensure the correct distribution of service fees, platform fees, and VAT at the closure of the service case.", "actor": "System", "preconditions": ["Service completion confirmed", "Total amount collected from the service requester’s account"], "postconditions": ["Service fee transferred to the service provider", "Platform fee transferred to the platform account", "VAT transferred to the tax account"], "main_flow_steps": ["Confirm the total amount collection from the service requester’s account.", "Transfer the service fee to the service provider’s account.", "Transfer the platform service fee to the platform account.", "Transfer the VAT to the tax account."]}]}, "4.2.46": {"title": "طلب عرض منافسة نقل", "business_goals": [], "stakeholders": ["طالب الخدمة", "الشركات الناقلة", "وسطاء النقل"], "main_steps": ["اختيار طالب الخدمة لنوع المركبات المطلوبة.", "تحديد طالب الخدمة لعدد النقلات المطلوبة.", "تحديد طالب الخدمة لمدة التنفيذ المطلوبة.", "تحديد طالب الخدمة لمواقع التحميل والتنزيل.", "إرفاق طالب الخدمة لنموذج العقد المطلوب التعاقد عليه أو اختيار النموذج من طرف الآخر.", "طالب الخدمة يحدد مدة النشر المطلوبة لعرض النقل.", "التحديد غير الإجباري للمتنافسين من قبل طالب الخدمة.", "تحديد طالب الخدمة الفئات المتنافسة."], "alternative_steps": [], "user_stories": ["طالب الخدمة: أستطيع تحديد متطلبات نقلي بدقة، وتحديد الشركات المتنافسة أو تركه عامًا، وأيضًا تحديد الفئات المتنافسة، ثم أتلقى العروض وأقارنها لاختيار الأنسب.", "الشركات الناقلة / وسطاء النقل: أتلقى الطلبات المناسبة لخدماتي وأقدم عروضي على الطلبات التي تناسبني."], "performance_indicators": ["عدد طلبات عروض منافسة.", "معدل الفترة الزمنية لتعبئة طلب عرض منافسة."], "use_cases": [{"id": "4.2.46-1", "description": "اختيار طالب الخدمة لنوع المركبات المطلوبة.", "actor": "طالب الخدمة", "preconditions": ["وجود حساب مسجل لطالب الخدمة على المنصة.", "أن يكون طالب الخدمة مستعدًا لتحديد نوع المركبات المطلوبة."], "postconditions": ["حفظ نوع المركبات المطلوبة في نظام المنصة.", "إرسال إشعار لتأكيد نوع المركبات المحددة."], "main_flow_steps": ["يقوم طالب الخدمة بالدخول إلى صفحة طلب عرض منافسة النقل.", "يقوم طالب الخدمة باختيار نوع المركبات المطلوبة من القائمة المنسدلة.", "يحفظ النظام نوع المركبات المطلوبة."]}, {"id": "4.2.46-2", "description": "تحديد طالب الخدمة لعدد النقلات المطلوبة.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام باختيار نوع المركبات المطلوبة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ عدد النقلات المطلوبة في نظام المنصة.", "إرسال إشعار لتأكيد عدد النقلات المحددة."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد عدد النقلات.", "<PERSON><PERSON><PERSON><PERSON> عدد النقلات المطلوبة.", "يحفظ النظام عدد النقلات المطلوبة."]}, {"id": "4.2.46-3", "description": "تحديد طالب الخدمة لمدة التنفيذ المطلوبة.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام باختيار نوع المركبات وعدد النقلات المطلوبة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ مدة التنفيذ المطلوبة في نظام المنصة.", "إرسال إشعار لتأكيد مدة التنفيذ المحددة."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد مدة التنفيذ.", "يحدد مدة التنفيذ المطلوبة.", "يحفظ النظام مدة التنفيذ المطلوبة."]}, {"id": "4.2.46-4", "description": "تحديد طالب الخدمة لمواقع التحميل والتنزيل.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام باختيار نوع المركبات، عدد النقلات، ومدة التنفيذ المطلوبة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ مواقع التحميل والتنزيل في نظام المنصة.", "إرسال إشعار لتأكيد مواقع التحميل والتنزيل."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد مواقع التحميل والتنزيل.", "يحدد مواقع التحميل والتنزيل المطلوبة.", "يحفظ النظام مواقع التحميل والتنزيل."]}, {"id": "4.2.46-5", "description": "إرفاق طالب الخدمة لنموذج العقد المطلوب التعاقد عليه أو اختيار النموذج من طرف الآخر.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام بتحديد كافة التفاصيل السابقة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ نموذج العقد في نظام المنصة.", "إرسال إشعار لتأكيد نموذج العقد."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة إرفاق نموذج العقد.", "يرفق نموذج العقد المطلوب أو يختار نموذجًا موجودًا.", "يحفظ النظام نموذج العقد المرفق."]}, {"id": "4.2.46-6", "description": "طالب الخدمة يحدد مدة النشر المطلوبة لعرض النقل.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام بتحديد كافة التفاصيل السابقة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ مدة النشر المطلوبة في نظام المنصة.", "إرسال إشعار لتأكيد مدة النشر."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد مدة النشر.", "يحدد مدة النشر المطلوبة.", "يحفظ النظام مدة النشر المطلوبة."]}, {"id": "4.2.46-7", "description": "التحديد غير الإجباري للمتنافسين من قبل طالب الخدمة.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام بتحديد كافة التفاصيل السابقة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ المتنافسين المحددين في نظام المنصة (إن وجد).", "إرسال إشعار لتأكيد المتنافسين المحددين."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد المتنافسين.", "يحد<PERSON> المتنافسين المطلوبين (إن وجد).", "يحفظ النظام المتنافسين المحددين."]}, {"id": "4.2.46-8", "description": "تحديد طالب الخدمة الفئات المتنافسة.", "actor": "طالب الخدمة", "preconditions": ["أن يكون طالب الخدمة قد قام بتحديد كافة التفاصيل السابقة.", "وجود حساب مسجل لطالب الخدمة على المنصة."], "postconditions": ["حفظ الفئات المتنافسة في نظام المنصة.", "إرسال إشعار لتأكيد الفئات المتنافسة."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد الفئات المتنافسة.", "يحد<PERSON> الفئات المتنافسة المطلوبة.", "يحفظ النظام الفئات المتنافسة."]}]}, "4.2.47": {"title": "إعلان منافسة في مجال النقل", "business_goals": [], "stakeholders": ["طالب الخدمة", "الشركات الناقلة", "وسطاء النقل", "التطبيق", "إداري النظام"], "main_steps": ["طالب الخدمة يقدم طلبًا لنشر منافسة نقل.", "تقديم التطبيق لمعاينة النشر والخيارات المتاحة.", "تأكيد طالب الخدمة للنشر.", "إرسال التطبيق لإشعارات للشركات المتنافسة المستهدفة.", "يمكن للشركات المتنافسة الرد على الطلبات المنشورة.", "قابلية التعطيل والتفعيل لخاصية الرسوم من قبل إداري النظام."], "alternative_steps": ["إمكانية تحديد طالب الخدمة لشركات معينة لتلقي الطلب بدلاً من نشره عامة."], "user_stories": ["طالب الخدمة: بعد تحديد متطلباتي الخاصة، أستطيع نشر المنافسة حيث يمكن لجميع الشركات المتنافسة المشاركة فيها أو الشركات التي اخترتها.", "الشركات الناقلة / وسطاء النقل: أتلقى إشعارات حول المنافسات التي تناسب خدماتي وأقدم عروضي.", "إداري النظام: أستطيع تعطيل أو تفعيل خاصية الرسوم حسب الحاجة.", "التطبيق: تنظم المنافسة بنجاح على الطلبات عبر المنصة، وساعد العملاء ومقدمي الخدمة في العثور على أفضل العروض والطلبات المناسبة."], "performance_indicators": ["عدد المنافسات المنشورة ونسبتها للطلبات المكتملة."], "use_cases": [{"id": "4.2.47-1", "description": "طالب الخدمة يقدم طلبًا لنشر منافسة نقل.", "actor": "طالب الخدمة", "preconditions": ["وجود حساب مسجل لطالب الخدمة على المنصة.", "أن يكون طالب الخدمة قد حدد متطلباته الخاصة بالنقل."], "postconditions": ["نشر طلب المنافسة على المنصة.", "إرسال إشعارات للشركات المتنافسة المستهدفة."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تقديم طلب نشر منافسة نقل.", "يقوم بتقديم طلب النشر بعد ملء كافة التفاصيل.", "يحفظ النظام طلب النشر وينشره على المنصة."]}, {"id": "4.2.47-2", "description": "تقديم التطبيق لمعاينة النشر والخيارات المتاحة.", "actor": "التطبيق", "preconditions": ["وجود طلب نشر منافسة مقدم من طالب الخدمة.", "إعداد كافة الخيارات الممكنة للنشر."], "postconditions": ["عرض طلب المنافسة مع الخيارات على طالب الخدمة.", "تأكيد طالب الخدمة للنشر."], "main_flow_steps": ["يتحقق التطبيق من طلب النشر المقدم.", "يعرض التطبيق كافة الخيارات المتاحة لطالب الخدمة.", "يؤكد طالب الخدمة النشر."]}, {"id": "4.2.47-3", "description": "إرسال التطبيق لإشعارات للشركات المتنافسة المستهدفة.", "actor": "التطبيق", "preconditions": ["تأكيد طلب النشر من قبل طالب الخدمة.", "إعداد قائمة الشركات المتنافسة المستهدفة."], "postconditions": ["إرسال الإشعارات إلى الشركات المتنافسة.", "حفظ سجل الإشعارات المرسلة."], "main_flow_steps": ["يحد<PERSON> التطبيق الشركات المتنافسة المستهدفة.", "يرسل التطبيق إشعارات لطلب المنافسة.", "يحفظ النظام سجل الإشعارات المرسلة."]}, {"id": "4.2.47-4", "description": "إمكانية تحديد طالب الخدمة لشركات معينة لتلقي الطلب بدلاً من نشره عامة.", "actor": "طالب الخدمة", "preconditions": ["وجود حساب مسجل لطالب الخدمة على المنصة.", "أن يكون طالب الخدمة قد حدد الشركات المستهدفة."], "postconditions": ["حفظ الشركات المحددة في نظام المنصة.", "إرسال الإشعارات فقط للشركات المحددة."], "main_flow_steps": ["يدخل طالب الخدمة إلى صفحة تحديد الشركات المستهدفة.", "يحد<PERSON> الشركات التي يرغب في إرسال الطلب إليها.", "يحفظ النظام الشركات المحددة ويرسل الإشعارات إليها فقط."]}, {"id": "4.2.47-5", "description": "تفعيل وتعطيل خاصية الرسوم من قبل إداري النظام.", "actor": "إداري النظام", "preconditions": ["وجود حساب إداري النظام.", "تحديد السياسة الحالية لتفعيل أو تعطيل الرسوم."], "postconditions": ["تحديث حالة خاصية الرسوم في النظام.", "إبلاغ كافة الأطراف بالتحديث."], "main_flow_steps": ["يدخل إداري النظام إلى لوحة التحكم.", "يحد<PERSON> تفعيل أو تعطيل خاصية الرسوم.", "يحفظ النظام التحديثات ويبلغ الأطراف المعنية."]}]}, "4.2.48": {"title": "الاستجابة للمنافسة", "business_goals": [], "stakeholders": ["الشركات الناقلة", "وسطاء النقل", "العميل", "التطبيق"], "main_steps": ["تصفح الشركات الناقلة ووسطاء النقل للمنافسات المتاحة.", "تحميل بيانات المنافسة ومرفقاتها.", "تجهيز الرد على المنافسة بالاقتراح المناسب للعرض.", "إرسال الرد على المنافسة عبر البريد الإلكتروني الخاص بالعميل."], "alternative_steps": ["الاستجابة على المنافسة من خلال البريد الإلكتروني للشركات مباشرة."], "user_stories": ["الشركات الناقلة / وسطاء النقل: استطيع تصفح المنافسات الفعالة وتحميل بيانات المنافسة ومرفقاتها.", "الشركات الناقلة / وسطاء النقل: أجهز عروضي وأرسلها عبر البريد الإلكتروني الخاص بالعميل.", "العميل: استقبل أفضل العروض من الشركات الناقلة ووسطاء النقل من خلال البريد الإلكتروني.", "التطبيق: العمل على تيسير الاتصال بين طالب الخدمة والشركات الناقلة / وسطاء النقل، مما يؤدي إلى تبادل العروض بكفاءة."], "performance_indicators": ["عد<PERSON> الطلبات التي تمت الاستجابة لها ونسبتها للطلبات التي تم نشرها."], "use_cases": [{"id": "4.2.48-1", "description": "تصفح الشركات الناقلة ووسطاء النقل للمنافسات المتاحة.", "actor": "الشركات الناقلة / وسطاء النقل", "preconditions": ["وجود منافسات منشورة على المنصة"], "postconditions": ["تم تصفح المنافسات وتحديد المنافسة المناسبة للرد عليها"], "main_flow_steps": ["الدخول إلى المنصة", "تحديد قسم المنافسات", "تصفح القائمة المتاحة للمنافسات"]}, {"id": "4.2.48-2", "description": "تحميل بيانات المنافسة ومرفقاتها.", "actor": "الشركات الناقلة / وسطاء النقل", "preconditions": ["تحديد المنافسة المناسبة"], "postconditions": ["تم تحميل بيانات المنافسة ومرفقاتها للمراجعة"], "main_flow_steps": ["فتح تفاصيل المنافسة المحددة", "تحميل كافة المرفقات والبيانات الخاصة بالمنافسة"]}, {"id": "4.2.48-3", "description": "تجهيز الرد على المنافسة بالاقتراح المناسب للعرض.", "actor": "الشركات الناقلة / وسطاء النقل", "preconditions": ["مراجعة بيانات المنافسة والمرفقات"], "postconditions": ["تم تجهيز العرض المناسب للرد على المنافسة"], "main_flow_steps": ["مراجعة شروط ومتطلبات المنافسة", "إعداد العرض المناسب مع كافة التفاصيل المطلوبة"]}, {"id": "4.2.48-4", "description": "إرسال الرد على المنافسة عبر البريد الإلكتروني الخاص بالعميل.", "actor": "الشركات الناقلة / وسطاء النقل", "preconditions": ["تجهيز العرض المناسب"], "postconditions": ["تم إرسال العرض إلى العميل"], "main_flow_steps": ["فتح البريد الإلكتروني", "إرفاق العرض المناسب", "إرسال البريد الإلكتروني إلى العميل"]}]}, "4.2.49": {"title": "ارشفة منافسات النقل", "business_goals": [], "stakeholders": ["العميل", "التطبيق"], "main_steps": ["بعد انتهاء مدة النشر، يتم أرشفة بيانات المنافسة في حساب العميل.", "يتم أيضًا تخزين كافة البيانات المتعلقة بالمنافسة في سجلات المنصة."], "alternative_steps": [], "user_stories": ["العميل: بعد انتهاء مدة النشر لمنافسة النقل، أستطيع العودة والإطلاع على جميع المعلومات المتعلقة بالمنافسة في حسابي.", "التطبيق: بعد انتهاء مدة النشر، أأمن آلية لأرشفة بيانات المنافسة بطريقة منظمة ومتاحة لكل من العميل ومشرفي المنصة."], "performance_indicators": [], "use_cases": [{"id": "4.2.49-1", "description": "أرشفة بيانات المنافسة بعد انتهاء مدة النشر في حساب العميل.", "actor": "التطبيق", "preconditions": ["انتهاء مدة النشر للمنافسة"], "postconditions": ["تم أرشفة بيانات المنافسة في حساب العميل"], "main_flow_steps": ["التطبيق يحدد أن مدة النشر للمنافسة قد انتهت", "نقل بيانات المنافسة إلى قسم الأرشيف في حساب العميل"]}, {"id": "4.2.49-2", "description": "تخزين كافة البيانات المتعلقة بالمنافسة في سجلات المنصة.", "actor": "التطبيق", "preconditions": ["انتهاء مدة النشر للمنافسة"], "postconditions": ["تم تخزين كافة بيانات المنافسة في سجلات المنصة"], "main_flow_steps": ["تحديد انتهاء مدة النشر", "نقل بيانات المنافسة إلى السجلات الخاصة بالمنصة"]}, {"id": "4.2.49-3", "description": "استعراض العميل للمنافسات المؤرشفة في حسابه.", "actor": "العميل", "preconditions": ["وجود منافسات مؤرشفة في حساب العميل"], "postconditions": ["تم استعراض المنافسات المؤرشفة"], "main_flow_steps": ["الدخول إلى حساب العميل", "فتح قسم الأرشيف", "تصفح المنافسات المؤرشفة"]}, {"id": "4.2.49-4", "description": "توفير آلية منظمة ومؤمنة لأرشفة البيانات للمنافسات.", "actor": "التطبيق", "preconditions": ["تحديد انتهاء مدة النشر", "تجهيز آليات الأرشفة"], "postconditions": ["تم أرشفة البيانات بطريقة منظمة ومؤمنة"], "main_flow_steps": ["تحديد انتهاء مدة النشر", "تجهيز آليات الأرشفة", "نقل البيانات إلى الأرشيف"]}]}, "4.2.50": {"title": "إدارة العمليات المالية في المنصة", "business_goals": [], "stakeholders": ["جميع العملاء في التطبيق"], "main_steps": ["تقديم طلب تحويل المبالغ من محفظة العميل لحسابه البنكي أو تحويل نقدي لأحد نقاط التسليم النقدية."], "alternative_steps": [], "user_stories": [], "performance_indicators": [], "use_cases": [{"id": "4.2.50-1", "description": "Submit a request to transfer amounts from the client's wallet to their bank account or cash transfer to one of the cash delivery points.", "actor": "Client", "preconditions": ["Client has a positive balance in their wallet", "Client is logged into their account"], "postconditions": ["Request is successfully submitted and processed", "Client receives confirmation of the transfer request"], "main_flow_steps": ["Client navigates to the financial operations section of the application.", "Client selects the option to transfer funds.", "Client enters the amount to be transferred and selects the destination (bank account or cash delivery point).", "Client confirms the details and submits the request.", "System processes the request and updates the client's wallet balance.", "Client receives a notification confirming the successful submission of the transfer request."]}]}, "4.2.51": {"title": "لوحة البيانات الإدارية", "business_goals": [], "stakeholders": ["المستخدمون (مزودو خدمات النقل)", "إداري النظام"], "main_steps": ["المستخدم يقوم بإدخال بيانات الوثائق الإدارية لممارسة أعمال النقل:", "نوع الوثيقة: (هوية/إقامة، رخصة قيادة، استمارة مركبة، تأمين مركبة، تأمين صحي، بطاقة تشغيل، سجل تجاري، ترخيص حكومي، رخصة ممارسة، تفويض قيادة، تأشيرة خروج وعودة (مفرد/متعدد)، غير ذلك)", "اسم الوثيقة", "رقم الوثيقة", "جهة إصدار الوثيقة: (جهة حكومية، قطاع خاص)", "اسم الجهة المصدرة للوثيقة", "تاريخ انتهاء الوثيقة", "علاقة الوثيقة (بالسائق، بالمركبة، بالمنشأة)", "هل انتهاء الوثيقة يتسبب في تعطيل ممارسة أعمال النقل", "يقوم النظام بإصدار مؤشرات ضمن لوحة بيانات إدارية للمستخدم لتوضيح حالة الوثائق المسجلة وتاريخ انتهائها", "يقوم النظام بإرسال إشعارات للمستخدم عند قرب انتهاء الوثائق"], "alternative_steps": [], "user_stories": ["كمستخدم: أ<PERSON><PERSON><PERSON> إدخال بيانات وثائقي الإدارية لممارسة أعمال النقل، بحيث أتمكن من متابعة حالتها ومعرفة تاريخ انتهائها.", "كإداري النظام: أريد توفير مؤشرات وإشعارات تساعد المستخدمين في إدارة وثائقهم وضمان عدم انتهاء صلاحيتها دون علمهم."], "performance_indicators": ["عدد الوثائق المدخلة ونسبة الوثائق التي اقتربت من تاريخ الانتهاء."], "use_cases": [{"id": "4.2.51-1", "description": "User enters administrative document data to practice transportation activities.", "actor": "User", "preconditions": ["User is logged into their account", "User has the required administrative documents"], "postconditions": ["Administrative document data is saved and updated in the system", "User receives notifications regarding document expiry"], "main_flow_steps": ["User navigates to the administrative documents section in the application.", "User selects the type of document (ID/Residence Permit, Driving License, Vehicle Registration, Vehicle Insurance, Health Insurance, Operation Card, Commercial Registration, Government License, Practice License, Driving Authorization, Exit and Return Visa (Single/Multiple), Other).", "User enters the document name.", "User enters the document number.", "User selects the issuing authority (Government Authority, Private Sector).", "User enters the name of the issuing authority.", "User enters the document's expiry date.", "User specifies the document's relation (Driver, Vehicle, Establishment).", "User indicates if the document's expiry affects the ability to practice transportation activities.", "System issues indicators on the user's administrative dashboard showing the status of registered documents and their expiry dates.", "System sends notifications to the user when documents are nearing expiration."]}]}}