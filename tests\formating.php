<?php


// قراءة ملف JSON
$jsonFilePath = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
$jsonData = file_get_contents($jsonFilePath);
$data = json_decode($jsonData, true);

if (json_last_error() === JSON_ERROR_NONE) {
    // تكرار جميع العناصر في الملف
    foreach ($data as $key => $value) {
        if (isset($value['use_cases']) && is_array($value['use_cases']) && isset($value['advanced_use_cases']) && is_array($value['advanced_use_cases'])) {
            $mergedCases = [];

            // دمج الحالات
            foreach ($value['use_cases'] as $useCaseKey => $useCaseValue) {
                if (isset($value['advanced_use_cases'][$useCaseKey])) {
                    $advancedUseCase = $value['advanced_use_cases'][$useCaseKey];

                    // دمج العناصر
                    $mergedCases[$useCaseKey] = [
                        'description-simple' => $useCaseValue['description'] ?? '',
                        'actor-simple' => $useCaseValue['actor'] ?? '',
                        'preconditions-simple' => $useCaseValue['preconditions'] ?? [],
                        'postconditions-simple' => $useCaseValue['postconditions'] ?? [],
                        'main_flow_steps-simple' => $useCaseValue['main_flow_steps'] ?? [],
                    ];

                    // إضافة باقي العناصر من advanced_use_case
                    foreach ($advancedUseCase as $advKey => $advValue) {
                        $mergedCases[$useCaseKey][$advKey] = $advValue;
                    }
                }
            }

            // تحديث المتطلب بالحالات المدمجة
            $data[$key]['use_cases'] = $mergedCases;

            // إزالة الحقول القديمة
            unset($data[$key]['advanced_use_cases']);
        }
    }

    // حفظ النتيجة النهائية في ملف جديد
    $outputFilePath = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
    $formattedJsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($outputFilePath, $formattedJsonData);

    echo "تمت معالجة الملف بنجاح وحفظ النتيجة في 'formated-by-code.json'.";
} else {
    echo "حدث خطأ أثناء قراءة ملف JSON.";
}

?>





<?php
echo('done-before');
return;
// قراءة ملف JSON
$jsonFilePath = ANALYST_PLUGIN_DIR . 'data/brd_reqs_with-advanced.json';
$jsonData = file_get_contents($jsonFilePath);
$data = json_decode($jsonData, true);

if (json_last_error() === JSON_ERROR_NONE) {
    // تكرار جميع العناصر في الملف
    foreach ($data as $key => $value) {
        if (isset($value['advanced_use_cases']) && is_array($value['advanced_use_cases'])) {
            foreach ($value['advanced_use_cases'] as $useCaseKey => $useCaseValue) {
                // حذف حقل last_updated إذا وجد
                if (isset($useCaseValue['last_updated'])) {
                    unset($data[$key]['advanced_use_cases'][$useCaseKey]['last_updated']);
                }

                // حذف حقل additional_info إذا وجد
                if (isset($useCaseValue['additional_info'])) {
                    unset($data[$key]['advanced_use_cases'][$useCaseKey]['additional_info']);
                }
            }
        }
    }

    // حفظ النتيجة النهائية في ملف جديد
    $outputFilePath = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
    $formattedJsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($outputFilePath, $formattedJsonData);

    echo "تمت معالجة الملف بنجاح وحفظ النتيجة في 'formated-by-code.json'.";
} else {
    echo "حدث خطأ أثناء قراءة ملف JSON.";
}

?>
