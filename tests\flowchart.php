<?php
$json = '{
    "id": "*******",
    "description": "يسجل المستخدم في النظام عن طريق ملء نموذج التسجيل بمعلوماتهم الشخصية (الاسم، البريد الإلكتروني، كلمة المرور، وغيرها). بعد تقديم النموذج، يتحقق النظام من صحة البيانات ويرسل رابط تفعيل الحساب إلى البريد الإلكتروني المدخل.",
    "actor": "User",
    "preconditions": [
      "يجب أن يكون المستخدم غير مسجل في النظام.",
      "يجب أن يكون لدى المستخدم اتصال بالإنترنت.",
      "يجب أن يكون البريد الإلكتروني المستخدم غير موجود مسبقًا في النظام."
    ],
    "postconditions": [
      "يتم إنشاء حساب جديد للمستخدم في النظام.",
      "يتم إرسال رابط تفعيل الحساب إلى البريد الإلكتروني المدخل."
    ],
    "main_flow_steps": [
      "المستخدم يفتح صفحة التسجيل.",
      "المستخدم يملأ النموذج بالمعلومات الشخصية.",
      "المستخدم يضغط على زر .",
      "النظام يتحقق من صحة البيانات المدخلة.",
      "النظام ينشئ حسابًا جديدًا للمستخدم.",
      "النظام يرسل رابط تفعيل الحساب إلى البريد الإلكتروني المدخل.",
      "النظام يعرض رسالة تأكيد بأن البريد الإلكتروني قد أُرسل."
    ],
    "alternative_flow_steps": [
      {
        "condition": "إذا كان البريد الإلكتروني المدخل موجودًا مسبقًا في النظام",
        "steps": [
          "النظام يعرض رسالة خطأ توضح أن البريد الإلكتروني موجود مسبقًا.",
          "المستخدم يُطلب منه إدخال بريد إلكتروني مختلف."
        ]
      }
    ],
    "exception_flow_steps": [
      {
        "condition": "إذا فشلت عملية التحقق من صحة البيانات",
        "steps": [
          "النظام يعرض رسالة خطأ توضح نوع الخطأ.",
          "المستخدم يُطلب منه تصحيح البيانات وإعادة المحاولة."
        ]
      }
    ],
    "trigger": "المستخدم يرغب في إنشاء حساب جديد.",
    "priority": "High",
    "business_rules": [
      "يجب أن يكون البريد الإلكتروني صالحًا.",
      "يجب أن تكون كلمة المرور مطابقة لمعايير الأمان المحددة."
    ],
    "assumptions": [
      "المستخدم يمتلك بريدًا إلكترونيًا صالحًا.",
      "المستخدم لديه القدرة على الوصول إلى البريد الإلكتروني المدخل لتفعيل الحساب."
    ],
    "usage_frequency": "متوسط (عدة مرات في اليوم)",
    "special_requirements": [
      "التسجيل يجب أن يكون متوافقًا مع المعايير الأمنية لحماية البيانات الشخصية."
    ],
    "notes_and_issues": [
      "تأكد من أن النظام يمكنه التعامل مع عمليات تسجيل متكررة بكفاءة.",
      "قد يحتاج النظام إلى آلية لمنع الهجمات الآلية (مثل reCAPTCHA)."
    ],
    "event_sequence": [
      "المستخدم يفتح صفحة التسجيل",
      "المستخدم يملأ نموذج التسجيل",
      "النظام يتحقق من البيانات",
      "النظام يرسل رابط تفعيل الحساب"
    ],
    "inputs": [
      "الاسم",
      "البريد الإلكتروني",
      "كلمة المرور",
      "تأكيد كلمة المرور"
    ],
    "outputs": [
      "رسالة تأكيد البريد الإلكتروني"
    ],
    "user_interactions": [
      "نموذج التسجيل",
      "رسالة تأكيد البريد الإلكتروني"
    ],
    "special_cases": [
      "البريد الإلكتروني موجود مسبقًا",
      "فشل في التحقق من صحة البيانات"
    ],
    "usage_scenarios": [
      "سيناريو تسجيل مستخدم جديد بنجاح",
      "سيناريو استخدام بريد إلكتروني موجود مسبقًا",
      "سيناريو فشل التحقق من صحة البيانات"
    ],
    "security_requirements": [
      "تشفير البيانات أثناء النقل",
      "تشفير البيانات في التخزين"
    ],
    "integration_with_other_systems": [
      "نظام البريد الإلكتروني لإرسال رابط التفعيل",
      "قاعدة البيانات لتخزين معلومات المستخدم"
    ],
    "constraints_and_assumptions": [
      "قيود حجم البريد الإلكتروني",
      "افتراض أن المستخدم يمكنه الوصول إلى البريد الإلكتروني فورًا"
    ],
    "testing_requirements": [
      "اختبارات التحقق من صحة البريد الإلكتروني",
      "اختبارات إنشاء الحساب",
      "اختبارات إرسال رابط التفعيل"
    ],
    "backend_details": {
      "api_endpoints": [
        {
          "method": "POST",
          "endpoint": "/api/register",
          "description": "Endpoint لتسجيل المستخدم الجديد",
          "request": {
            "body": {
              "name": "string",
              "email": "string",
              "password": "string"
            },
            "headers": {
              "Content-Type": "application/json"
            }
          },
          "response": {
            "200": {
              "description": "Success",
              "body": {
                "status": "string",
                "message": "string"
              }
            },
            "400": {
              "description": "Bad Request"
            }
          }
        }
      ]
    },
    "frontend_details": {
      "screens": [
        {
          "name": "RegistrationScreen",
          "components": [
            {
              "type": "Form",
              "props": {
                "inputs": [
                  {
                    "name": "name",
                    "type": "text",
                    "label": "Name",
                    "required": true
                  },
                  {
                    "name": "email",
                    "type": "email",
                    "label": "Email",
                    "required": true
                  },
                  {
                    "name": "password",
                    "type": "password",
                    "label": "Password",
                    "required": true
                  },
                  {
                    "name": "confirm_password",
                    "type": "password",
                    "label": "Confirm Password",
                    "required": true
                  }
                ],
                "submit_btn_message": "Register",
                "success_message": "Registration successful! Please check your email to activate your account.",
                "success_redirect": "LoginScreen"
              }
            }
          ]
        }
      ]
    },
    "notifications": [
      {
        "methods": ["email"],
        "recipient": "User",
        "title": "Account Activation",
        "message": "Please check your email to activate your account."
      }
    ],
    "flowchart": "<mxGraphModel><root><mxCell id=\"0\"/><mxCell id=\"1\" parent=\"0\"/><mxCell id=\"2\" value=\"User Opens Registration Page\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"50\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"3\" value=\"User Fills Registration Form\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"100\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"4\" value=\"System Validates Data\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"150\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"5\" value=\"System Creates Account\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"200\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"6\" value=\"System Sends Activation Link\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"250\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"7\" value=\"System Displays Confirmation Message\" style=\"rounded=0;whiteSpace=wrap;html=1;\" vertex=\"1\" parent=\"1\"><mxGeometry x=\"150\" y=\"300\" width=\"100\" height=\"30\" as=\"geometry\"/></mxCell><mxCell id=\"8\" edge=\"1\" parent=\"1\" source=\"2\" target=\"3\"><mxGeometry relative=\"1\" as=\"geometry\"/></mxCell><mxCell id=\"9\" edge=\"1\" parent=\"1\" source=\"3\" target=\"4\"><mxGeometry relative=\"1\" as=\"geometry\"/></mxCell><mxCell id=\"10\" edge=\"1\" parent=\"1\" source=\"4\" target=\"5\"><mxGeometry relative=\"1\" as=\"geometry\"/></mxCell><mxCell id=\"11\" edge=\"1\" parent=\"1\" source=\"5\" target=\"6\"><mxGeometry relative=\"1\" as=\"geometry\"/></mxCell><mxCell id=\"12\" edge=\"1\" parent=\"1\" source=\"6\" target=\"7\"><mxGeometry relative=\"1\" as=\"geometry\"/></mxCell></root></mxGraphModel>"
  }
';

$json = json_decode($json, true);
echo $json['flowchart'];




$json2 = '{
    "id": "*******",
    "description": "هذه الحالة توضح كيفية قيام المستخدم بتسجيل الدخول إلى النظام باستخدام بيانات الاعتماد الخاصة به. يجب أن يتضمن الإدخال اسم المستخدم وكلمة المرور. يتم التحقق من صحة البيانات واعتمادها قبل السماح بالوصول إلى النظام.",
    "actor": "User",
    "preconditions": [
      "المستخدم مسجل مسبقًا في النظام.",
      "المستخدم لديه اسم مستخدم وكلمة مرور صالحة.",
      "نظام المصادقة متاح ويعمل بشكل صحيح."
    ],
    "postconditions": [
      "المستخدم مسجل دخول بنجاح.",
      "تم تحديث حالة المستخدم إلى .",
      "تم تسجيل وقت وتاريخ تسجيل الدخول في النظام."
    ],
    "main_flow_steps": [
      "المستخدم يفتح شاشة تسجيل الدخول.",
      "المستخدم يدخل اسم المستخدم وكلمة المرور.",
      "النظام يتحقق من صحة بيانات الاعتماد.",
      "إذا كانت بيانات الاعتماد صحيحة، يتم توجيه المستخدم إلى الشاشة الرئيسية."
    ],
    "alternative_flow_steps": [
      {
        "condition": "بيانات الاعتماد غير صحيحة.",
        "steps": [
          "النظام يعرض رسالة خطأ تطلب من المستخدم إدخال بيانات صحيحة.",
          "المستخدم يحاول إدخال بيانات الاعتماد مرة أخرى."
        ]
      }
    ],
    "exception_flow_steps": [
      {
        "condition": "النظام غير متاح.",
        "steps": [
          "النظام يعرض رسالة تعذر الاتصال بالخادم.",
          "يطلب من المستخدم المحاولة لاحقًا."
        ]
      }
    ],
    "trigger": "محاولة المستخدم لتسجيل الدخول.",
    "priority": "عالي",
    "business_rules": [
      "يجب أن تكون كلمة المرور مكونة من 8 أحرف على الأقل وتتضمن حرفًا كبيرًا ورقمًا.",
      "يجب قفل الحساب بعد ثلاث محاولات فاشلة لتسجيل الدخول."
    ],
    "assumptions": [
      "النظام سيظل متاحًا خلال فترة تسجيل الدخول.",
      "المستخدم لديه اتصال إنترنت مستقر."
    ],
    "frequency_of_use": "يوميًا",
    "special_requirements": [
      "يجب أن يتم تسجيل الدخول خلال 3 ثوانٍ.",
      "يجب تشفير بيانات الاعتماد أثناء الإرسال."
    ],
    "notes_and_issues": [
      "إذا كان المستخدم لديه مصادقة ثنائية، يجب إرسال رمز تأكيد إلى هاتفه."
    ],
    "event_sequence": [
      "المستخدم يفتح التطبيق.",
      "المستخدم يدخل بيانات الاعتماد.",
      "النظام يتحقق من البيانات.",
      "النظام يسجل الدخول."
    ],
    "inputs": [
      "اسم المستخدم",
      "كلمة المرور"
    ],
    "outputs": [
      "رسالة نجاح أو خطأ"
    ],
    "user_interactions": [
      "إدخال اسم المستخدم وكلمة المرور.",
      "استلام إشعار نجاح أو خطأ."
    ],
    "special_cases": [
      "نسيان كلمة المرور.",
      "قفل الحساب بعد محاولات فاشلة متعددة."
    ],
    "usage_scenarios": [
      "المستخدم يقوم بتسجيل الدخول بنجاح.",
      "المستخدم يدخل بيانات اعتماد خاطئة ويتم توجيهه لإعادة المحاولة."
    ],
    "security_requirements": [
      "تشفير بيانات الاعتماد.",
      "استخدام HTTPS للاتصال."
    ],
    "integration_with_other_systems": [
      "قاعدة بيانات المستخدمين للتحقق من بيانات الاعتماد.",
      "نظام المصادقة الثنائي (إن وجد)."
    ],
    "constraints_and_assumptions": [
      "يفترض أن جميع المستخدمين لديهم اتصال بالإنترنت.",
      "النظام يعمل بشكل مستمر بدون تعطل."
    ],
    "testing_requirements": [
      "اختبارات وظيفية للتحقق من عملية تسجيل الدخول.",
      "اختبارات الأمان لضمان حماية بيانات المستخدم."
    ],
    "backend_details": {
      "api_endpoints": [
        {
          "method": "POST",
          "endpoint": "/api/login",
          "description": "Endpoint لتسجيل الدخول",
          "request": {
            "body": {
              "username": "string",
              "password": "string"
            },
            "headers": {
              "Content-Type": "application/json"
            }
          },
          "response": {
            "200": {
              "description": "Success",
              "body": {
                "status": "string",
                "token": "string"
              }
            },
            "400": {
              "description": "Bad Request"
            },
            "401": {
              "description": "Unauthorized"
            }
          }
        }
      ]
    },
    "frontend_details": {
      "screens": [
        {
          "name": "LoginScreen",
          "components": [
            {
              "type": "TextBlock",
              "props": {
                "text": "Please enter your username and password to login."
              }
            },
            {
              "type": "Form",
              "props": {
                "inputs": [
                  {
                    "type": "TextInput",
                    "props": {
                      "label": "Username",
                      "id": "username",
                      "placeholder": "Enter your username"
                    }
                  },
                  {
                    "type": "PasswordInput",
                    "props": {
                      "label": "Password",
                      "id": "password",
                      "placeholder": "Enter your password",
                      "validation_error_message": "Password must be at least 8 characters and include a number and a capital letter."
                    }
                  }
                ],
                "submit_btn_message": "Login",
                "success_message": "Login successful!",
                "success_redirect": "HomeScreen"
              }
            }
          ]
        }
      ]
    },
    "notifications": [
      {
        "methods": ["push notification", "email"],
        "recipient": "User",
        "title": "Login Successful",
        "message": "You have successfully logged into the system."
      }
    ],
    "flowchart": "graph LR\nA[User] --> B[Open Login Screen]\nB --> C[Enter Username and Password]\nC --> D[Validate Credentials]\nD --> E{Credentials Valid?}\nE --> |Yes| F[Redirect to Home Screen]\nE --> |No| G[Show Error Message]"
  }
  ';
$json2 = json_decode($json2, true);
echo $json2['flowchart'];