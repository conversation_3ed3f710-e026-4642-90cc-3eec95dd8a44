# Requirements JSON Export

هذا المجلد يحتوي على مجلدات فرعية لكل متطلب، حيث يتم إنشاؤها تلقائياً عند حفظ أو تحديث المتطلبات في النظام.

## كيفية العمل

### التصدير التلقائي
- عند إنشاء متطلب جديد أو تحديث متطلب موجود، يتم تلقائياً إنشاء مجلد منفصل للمتطلب
- اسم المجلد: `{requirement_id} - {post_title}`
- داخل كل مجلد يوجد ملف `data.json` يحتوي على العنوان وجميع حقول ACF
- يتم حذف المجلدات القديمة تلقائياً عند تغيير رقم المتطلب أو العنوان

### هيكل المجلد
كل مجلد متطلب يحتوي على:
- `data.json` - ملف يحتوي على العنوان وجميع حقول ACF

### هيكل ملف data.json
كل ملف JSON يحتوي على:

```json
{
    "title": "عنوان المتطلب",
    "id": "رقم المتطلب",
    "business_goals": [
        {
            "goal": "هدف العمل الأول"
        },
        {
            "goal": "هدف العمل الثاني"
        }
    ],
    "stakeholders": [
        {
            "stakeholder": "الجهة المعنية الأولى"
        }
    ],
    "main_steps": [
        {
            "step": "الخطوة الأولى"
        }
    ],
    "alternative_steps": [
        {
            "step": "الخطوة البديلة"
        }
    ],
    "user_stories": [
        {
            "story": "قصة المستخدم"
        }
    ],
    "performance_indicators": [
        {
            "indicators": "مؤشر الأداء"
        }
    ],
    "use_cases": [
        {
            "is_new": true,
            "case_id": "رقم الحالة",
            "description_simple": "الوصف المبسط",
            "actor_simple": "الفاعل المبسط",
            "description": "الوصف التفصيلي",
            "actor": "الفاعل",
            "trigger": "المحفز",
            "priority": "الأولوية",
            "frequency_of_use": "تكرار الاستخدام",
            "preconditions_simple": [
                {
                    "condition": "شرط مسبق مبسط"
                }
            ],
            "postconditions_simple": [
                {
                    "condition": "شرط لاحق مبسط"
                }
            ],
            "main_flow_steps_simple": [
                {
                    "step": "خطوة التدفق الرئيسي المبسط"
                }
            ],
            "preconditions": [
                {
                    "condition": "شرط مسبق"
                }
            ],
            "postconditions": [
                {
                    "condition": "شرط لاحق"
                }
            ],
            "main_flow_steps": [
                {
                    "step": "خطوة التدفق الرئيسي"
                }
            ],
            "events_sequence": [
                {
                    "event": "حدث في التسلسل"
                }
            ],
            "alternative_flow_steps": [
                {
                    "condition": "شرط التدفق البديل",
                    "steps": [
                        {
                            "step": "خطوة التدفق البديل"
                        }
                    ]
                }
            ],
            "exception_flow_steps": [
                {
                    "condition": "شرط تدفق الاستثناء",
                    "steps": [
                        {
                            "step": "خطوة تدفق الاستثناء"
                        }
                    ]
                }
            ],
            "business_rules": [
                {
                    "rule": "قاعدة عمل"
                }
            ],
            "assumptions": [
                {
                    "assumption": "افتراض"
                }
            ],
            "special_requirements": [
                {
                    "requirement": "متطلب خاص"
                }
            ],
            "notes_and_issues": [
                {
                    "note": "ملاحظة أو مشكلة"
                }
            ],
            "inputs": [
                {
                    "input": "مدخل"
                }
            ],
            "outputs": [
                {
                    "output": "مخرج"
                }
            ],
            "user_interactions": [
                {
                    "interaction": "تفاعل المستخدم"
                }
            ],
            "special_conditions": [
                {
                    "condition": "شرط خاص"
                }
            ],
            "usage_scenarios": [
                {
                    "scenario": "سيناريو الاستخدام"
                }
            ],
            "security_requirements": [
                {
                    "requirement": "متطلب أمني"
                }
            ],
            "integration_with_other_systems": [
                {
                    "system": "نظام متكامل"
                }
            ],
            "constraints_and_assumptions": [
                {
                    "constraint": "قيد أو افتراض"
                }
            ],
            "testing_requirements": [
                {
                    "requirement": "متطلب اختبار"
                }
            ],
            "backend_details": [
                {
                    "api_endpoints": [
                        {
                            "method": "GET",
                            "endpoint": "/api/example",
                            "description": "وصف API",
                            "request": [
                                {
                                    "body": "محتوى الطلب",
                                    "headers": "رؤوس الطلب"
                                }
                            ],
                            "response": [
                                {
                                    "status_code": 200,
                                    "description": "وصف الاستجابة",
                                    "body": "محتوى الاستجابة"
                                }
                            ]
                        }
                    ]
                }
            ],
            "frontend_details": [
                {
                    "screens": [
                        {
                            "name": "اسم الشاشة",
                            "components": [
                                {
                                    "acf_fc_layout": "form",
                                    "inputs": [
                                        {
                                            "type": "text",
                                            "label": "تسمية الحقل",
                                            "validation": "قواعد التحقق",
                                            "options": "خيارات الحقل"
                                        }
                                    ],
                                    "submit_btn_message": "رسالة زر الإرسال",
                                    "success_message": "رسالة النجاح",
                                    "success_redirect": "صفحة إعادة التوجيه"
                                }
                            ]
                        }
                    ]
                }
            ],
            "notifications": [
                {
                    "methods": "طرق الإشعار",
                    "recipient": "المستقبل",
                    "title": "عنوان الإشعار",
                    "message": "رسالة الإشعار"
                }
            ],
            "flowchart_mermaid": "كود مخطط التدفق بصيغة Mermaid"
        }
    ]
}
```

**ملاحظة**: هذا مثال يوضح جميع الحقول المتاحة. في الواقع، سيحتوي الملف على البيانات الفعلية المدخلة في كل متطلب.

## الاختبار

يمكنك اختبار الميزة من خلال:

1. **صفحة الاختبار**: `/tests/test-json-export.php`
   - عرض جميع المتطلبات
   - تصدير متطلب واحد أو جميع المتطلبات
   - عرض حالة ملفات JSON

2. **عارض JSON**: `/tests/view-json.php`
   - عرض محتوى ملفات JSON
   - تحميل الملفات
   - التحقق من صحة JSON

## الميزات

### التصدير التلقائي
- يتم التصدير تلقائياً عند الحفظ
- لا حاجة لتدخل يدوي
- يدعم جميع أنواع الحقول المخصصة

### إدارة الملفات
- حذف الملفات القديمة تلقائياً
- تنظيف الملفات عند حذف المتطلب
- أسماء ملفات فريدة ومنظمة

### التوافق
- يعمل مع جميع حقول ACF
- يدعم الحقول المتكررة والمعقدة
- يحافظ على هيكل البيانات الأصلي

## استكشاف الأخطاء

### الملف لا يتم إنشاؤه
- تأكد من أن المتطلب من نوع `requirement`
- تحقق من صلاحيات الكتابة في مجلد `requirments`
- راجع سجل الأخطاء في WordPress

### JSON غير صحيح
- تحقق من وجود أحرف خاصة في البيانات
- تأكد من أن جميع الحقول تحتوي على بيانات صحيحة

### الملفات القديمة لا تُحذف
- تحقق من صلاحيات الحذف في المجلد
- تأكد من أن post meta يتم حفظه بشكل صحيح

## الدعم الفني

في حالة وجود مشاكل، يرجى:
1. التحقق من سجل الأخطاء
2. اختبار الميزة باستخدام صفحة الاختبار
3. التأكد من تفعيل البرنامج المساعد بشكل صحيح
