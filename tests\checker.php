<?php
$data = full_data();

// // دالة للحصول على مفاتيح الأبناء
// function getChildKeys($array, $parentKey) {
//     if (is_array($parentKey)) {
//         if( isset($parentKey[0]) ) {
//             return array_keys($parentKey[0]);
//         } else {
//             return array_keys($parentKey);
//         }
//     }
//     return [];
// }

// // دالة للمقارنة
// function compareKeys($data, $parentKey) {
//     // الحصول على مفاتيح الأبناء لأول عنصر يحتوي على المفتاح الأب كمرجع داخل use_cases
//     $referenceKeys = [];
//     foreach ($data as $element) {
//         foreach ($element['use_cases'] as $id => $use_caeses_key ) {
//             if ( isset($use_caeses_key[$parentKey]) && is_array($use_caeses_key[$parentKey]) )  {
//                 $referenceKeys = getChildKeys( $element, $use_caeses_key[$parentKey] );
//                 break;
//             }
//         }
//     }
//     // prr($referenceKeys);
//     if (empty($referenceKeys)) {
//         echo "المفتاح الأب '$parentKey' غير موجود أو لا يحتوي على بيانات صحيحة داخل 'use_cases'.\n";
//         return;
//     }

//     // مقارنة بقية العناصر مع المرجع داخل use_cases
//     foreach ($data as $key => $element) {
//         foreach ($element['use_cases'] as $k => $use_caeses_key) {
//             if ( isset($use_caeses_key[$parentKey]) && is_array($use_caeses_key[$parentKey]) )  {
//                 $currentKeys = getChildKeys( $element, $use_caeses_key[$parentKey] );
//             }
//             if(empty($currentKeys) ){
//                 continue;
//             }
//             prr($currentKeys);
//             if ( $referenceKeys !== $currentKeys ) {
//                 $differentKeys = array_diff($currentKeys, $referenceKeys);
//                 $missingKeys = array_diff($referenceKeys, $currentKeys);
//                 // echo "رقم السطر المختلف: $key\n";
//                 // echo "السطر المختلف هو: " . prr($element) . "\n";
//                 echo "المفاتيح المختلفة: " . prr($differentKeys) . "\n";
//                 echo "المفاتيح المفقودة: " . prr($missingKeys) . "\n";
//             }
//         }
//     }
// }

// // استدعاء الدالة مع اسم المفتاح الأب داخل use_cases
// compareKeys($data, 'frontend_details');


// $frontend_detail = [];

// foreach ( $data as $key => $value ) {
//       foreach ( $value['use_cases'] as $id => $usecase ) {
//         foreach ( $usecase as $k => $val ) {
//             if( $k === 'frontend_details' ) {
//                 $frontend_detail[$key][$id][] = $val;
//             }
//         }
//       }
// }

// $frontend_detail = json_encode($frontend_detail, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT);

// krumo($frontend_detail);

function reformatComponents($components) {
    $newComponents = [];
    foreach ($components as $component) {
        $newComponent = [
            'type' => $component['type'],
            'props' => []
        ];
        if (isset($component['props'])) {
            if (isset($component['props']['inputs'])) {
                $newComponent['props']['inputs'] = [];
                foreach ($component['props']['inputs'] as $input) {
                    $newInput = [
                        'label' => $input['label'],
                        'type' => $input['type'],
                        'name' => $input['name'] ?? '',
                        'validation' => []
                    ];
                    if (isset($input['validation'])) {
                        foreach ($input['validation'] as $key => $value) {
                            $newInput['validation'][$key] = $value;
                        }
                    }
                    if (isset($input['error_message'])) {
                        $newInput['validation']['error_message'] = $input['error_message'];
                    }
                    $newComponent['props']['inputs'][] = $newInput;
                }
            } else {
                foreach ($component['props'] as $key => $value) {
                    $newComponent['props'][$key] = $value;
                }
            }
        }
        $newComponents[] = $newComponent;
    }
    return $newComponents;
}

function reformatJson($data) {
    $reformattedData = ['screens' => []];

    // إعادة تنسيق الشاشات
    foreach ($data as $sectionKey => $section) {
        if (isset($section['frontend_details']['screens'])) {
            foreach ($section['frontend_details']['screens'] as $screen) {
                $newScreen = [
                    'name' => $screen['name'],
                    'components' => reformatComponents($screen['components'])
                ];
                $reformattedData['screens'][] = $newScreen;
            }
        }
    }

    return $reformattedData;
}
$jsonFilePath = ANALYST_PLUGIN_DIR . 'data/formated-by-code.json';
// قراءة محتوى الملف JSON
$inputFile = $jsonFilePath ;
$jsonContent = file_get_contents($inputFile);
$data = json_decode($jsonContent, true);

// إعادة صياغة البيانات
$reformattedData = reformatJson($data);

krumo($reformattedData);
// كتابة الملف المعاد تنسيقه
// $outputFile = 'frontend_detail_reformatted.json';
// file_put_contents($outputFile, json_encode($reformattedData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

// echo "تم إعادة صياغة الملف بنجاح وحفظه في $outputFile";