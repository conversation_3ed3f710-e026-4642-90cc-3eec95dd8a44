<?php

/**
 * Template functions for requirements - JSON-based data retrieval
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get requirement field value from JSON (replacement for get_field)
 * 
 * @param string $field_name ACF field name
 * @param int $post_id WordPress post ID (optional, uses current post if not provided)
 * @return mixed Field value or false if not found
 */
function get_requirement_field($field_name, $post_id = null) {
    if (!$post_id) {
        global $post;
        $post_id = $post ? $post->ID : 0;
    }

    if (!$post_id) {
        return false;
    }

    // Check if this is a requirement post
    if (get_post_type($post_id) !== 'requirement') {
        return get_field($field_name, $post_id); // Fallback to ACF for non-requirements
    }

    // Get JSON reader instance
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->get_field_value($post_id, $field_name);
}

/**
 * Get all requirement data from JSON
 * 
 * @param int $post_id WordPress post ID (optional, uses current post if not provided)
 * @return array|false All requirement data or false if not found
 */
function get_requirement_data($post_id = null) {
    if (!$post_id) {
        global $post;
        $post_id = $post ? $post->ID : 0;
    }

    if (!$post_id) {
        return false;
    }

    // Check if this is a requirement post
    if (get_post_type($post_id) !== 'requirement') {
        return false;
    }

    // Get JSON reader instance
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->get_requirement_data_by_post_id($post_id);
}

/**
 * Get requirement by requirement ID (ACF field)
 * 
 * @param string $requirement_id The requirement ID
 * @return array|false Requirement data or false if not found
 */
function get_requirement_by_id($requirement_id) {
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->get_requirement_data_by_id($requirement_id);
}

/**
 * Get all requirements
 * 
 * @param bool $sorted Whether to sort by requirement ID
 * @return array Array of all requirements
 */
function get_all_requirements($sorted = true) {
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    if ($sorted) {
        return $json_reader->get_requirements_sorted_by_id();
    } else {
        return $json_reader->get_all_requirements();
    }
}

/**
 * Check if requirement exists by ID
 * 
 * @param string $requirement_id The requirement ID
 * @return bool True if exists, false otherwise
 */
function requirement_exists($requirement_id) {
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->requirement_exists($requirement_id);
}

/**
 * Get requirement title by requirement ID
 * 
 * @param string $requirement_id The requirement ID
 * @return string|false Title or false if not found
 */
function get_requirement_title_by_id($requirement_id) {
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->get_requirement_title($requirement_id);
}

/**
 * Display requirement field with fallback
 * 
 * @param string $field_name ACF field name
 * @param int $post_id WordPress post ID (optional)
 * @param string $default Default value if field is empty
 * @param bool $echo Whether to echo or return the value
 * @return string|void Field value
 */
function the_requirement_field($field_name, $post_id = null, $default = '', $echo = true) {
    $value = get_requirement_field($field_name, $post_id);
    
    if (empty($value)) {
        $value = $default;
    }

    if ($echo) {
        echo esc_html($value);
    } else {
        return $value;
    }
}

/**
 * Display requirement repeater field
 *
 * @param string $field_name ACF repeater field name
 * @param int $post_id WordPress post ID (optional)
 * @param string $sub_field Sub field name to display
 * @param string $separator Separator between items
 * @param bool $echo Whether to echo or return the value
 * @return string|void Formatted repeater values
 */
function the_requirement_repeater($field_name, $post_id = null, $sub_field = null, $separator = ', ', $echo = true) {
    $repeater_data = get_requirement_field($field_name, $post_id);

    if (!is_array($repeater_data) || empty($repeater_data)) {
        $output = '';
        if ($echo) {
            echo $output;
        } else {
            return $output;
        }
        return;
    }

    $values = array();

    foreach ($repeater_data as $row) {
        if ($sub_field && is_array($row) && isset($row[$sub_field])) {
            // Standard sub-field access
            $values[] = $row[$sub_field];
        } elseif ($sub_field && is_array($row)) {
            // Try to find the sub-field with different approaches
            $found_value = null;

            // Try exact match first
            if (isset($row[$sub_field])) {
                $found_value = $row[$sub_field];
            }
            // Try case-insensitive match
            elseif (function_exists('array_change_key_case')) {
                $lower_row = array_change_key_case($row, CASE_LOWER);
                $lower_sub_field = strtolower($sub_field);
                if (isset($lower_row[$lower_sub_field])) {
                    $found_value = $lower_row[$lower_sub_field];
                }
            }
            // Try to find any field that contains the sub_field name
            else {
                foreach ($row as $key => $value) {
                    if (stripos($key, $sub_field) !== false) {
                        $found_value = $value;
                        break;
                    }
                }
            }

            if ($found_value !== null) {
                $values[] = $found_value;
            }
        } elseif (!$sub_field && is_string($row)) {
            // Direct string value
            $values[] = $row;
        } elseif (!$sub_field && is_array($row)) {
            // If no sub_field specified, try to get the first non-empty value
            foreach ($row as $value) {
                if (!empty($value) && is_string($value)) {
                    $values[] = $value;
                    break;
                }
            }
        }
    }

    // Filter out empty values and join
    $filtered_values = array_filter($values, function($value) {
        return !empty(trim($value));
    });

    $output = implode($separator, $filtered_values);

    if ($echo) {
        echo esc_html($output);
    } else {
        return $output;
    }
}

/**
 * Get requirements statistics
 * 
 * @return array Statistics array
 */
function get_requirements_stats() {
    static $json_reader = null;
    if ($json_reader === null) {
        $json_reader = new Requirements_JSON_Reader();
    }

    return $json_reader->get_requirements_stats();
}

/**
 * Display use cases for a requirement
 * 
 * @param int $post_id WordPress post ID (optional)
 * @param bool $detailed Whether to show detailed view
 * @return void
 */
function display_requirement_use_cases($post_id = null, $detailed = false) {
    $use_cases = get_requirement_field('use_cases', $post_id);
    
    if (!is_array($use_cases) || empty($use_cases)) {
        echo '<p>لا توجد حالات استخدام محددة.</p>';
        return;
    }

    echo '<div class="use-cases-container">';
    
    foreach ($use_cases as $index => $use_case) {
        $case_number = $index + 1;
        echo '<div class="use-case-item">';
        echo '<h4>حالة الاستخدام ' . $case_number . '</h4>';
        
        if (isset($use_case['case_id']) && !empty($use_case['case_id'])) {
            echo '<p><strong>رقم الحالة:</strong> ' . esc_html($use_case['case_id']) . '</p>';
        }
        
        if (isset($use_case['description']) && !empty($use_case['description'])) {
            echo '<p><strong>الوصف:</strong> ' . esc_html($use_case['description']) . '</p>';
        } elseif (isset($use_case['description_simple']) && !empty($use_case['description_simple'])) {
            echo '<p><strong>الوصف:</strong> ' . esc_html($use_case['description_simple']) . '</p>';
        }
        
        if (isset($use_case['actor']) && !empty($use_case['actor'])) {
            echo '<p><strong>الفاعل:</strong> ' . esc_html($use_case['actor']) . '</p>';
        } elseif (isset($use_case['actor_simple']) && !empty($use_case['actor_simple'])) {
            echo '<p><strong>الفاعل:</strong> ' . esc_html($use_case['actor_simple']) . '</p>';
        }
        
        if ($detailed) {
            // Display detailed information
            $detail_fields = array(
                'trigger' => 'المحفز',
                'priority' => 'الأولوية',
                'frequency_of_use' => 'تكرار الاستخدام'
            );
            
            foreach ($detail_fields as $field => $label) {
                if (isset($use_case[$field]) && !empty($use_case[$field])) {
                    echo '<p><strong>' . $label . ':</strong> ' . esc_html($use_case[$field]) . '</p>';
                }
            }
            
            // Display repeater fields
            $repeater_fields = array(
                'preconditions' => 'الشروط المسبقة',
                'postconditions' => 'الشروط اللاحقة',
                'main_flow_steps' => 'خطوات التدفق الرئيسي'
            );
            
            foreach ($repeater_fields as $field => $label) {
                if (isset($use_case[$field]) && is_array($use_case[$field]) && !empty($use_case[$field])) {
                    echo '<div class="repeater-field">';
                    echo '<strong>' . $label . ':</strong>';
                    echo '<ul>';
                    foreach ($use_case[$field] as $item) {
                        if (is_array($item)) {
                            $text = isset($item['condition']) ? $item['condition'] : (isset($item['step']) ? $item['step'] : '');
                        } else {
                            $text = $item;
                        }
                        if (!empty($text)) {
                            echo '<li>' . esc_html($text) . '</li>';
                        }
                    }
                    echo '</ul>';
                    echo '</div>';
                }
            }
        }
        
        echo '</div>';
    }
    
    echo '</div>';
}
