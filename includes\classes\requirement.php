<?php

class Requirement {

    public function __construct(){
        add_filter('template_include', array( $this ,  'custom_requirment_template' ) );
        add_filter('manage_posts_columns', [$this, 'add_requirement_column'], 10);
        add_action('manage_posts_custom_column', [$this, 'requirement_column_content'], 10, 2);
        add_filter('manage_edit-requirement_sortable_columns', [$this, 'requirement_column_sortable']);
        add_action('pre_get_posts', [$this, 'order_by_requirement_id' ]);

    }

    function custom_requirment_template($template) {
        if ( is_singular('requirement') ) {
            $plugin_template =  ANALYST_PLUGIN_DIR . 'views/single-req.php';
            if (file_exists($plugin_template)) {
                return $plugin_template;
            }
        }
        return $template;
    }
    
    public function add_requirement_column($columns) {
        $new_columns = [];
        $first = true;

        foreach ($columns as $key => $value) {
            if ($first) {
                $new_columns[$key] = $value;
                $new_columns['requirement_id'] = 'Requirement ID';
                $first = false;
            } else {
                $new_columns[$key] = $value;
            }
        }

        return $new_columns;
    }

    public function requirement_column_content($column, $post_id) {
        if ($column == 'requirement_id') {
            $requirement_id = get_field('id', $post_id);
            echo is_numeric($requirement_id) ? $requirement_id : '';
        }
    }

    public function requirement_column_sortable($columns) {
        $columns['requirement_id'] = 'requirement_id';
        return $columns;
    }

    function order_by_requirement_id( $query ) {

        if( $query->get( 'post_type')  !== 'requirement'  ) {
          return;
        }
      
        $query->set( 'orderby', 'meta_value_num' );
        $query->set( 'meta_key', 'id' );
        $query->set( 'meta_type', 'numeric' );
        $query->set( 'order', 'ASC' );
      }
}