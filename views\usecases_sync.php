<?php
function generatePostUseCasesTable($useCasesData , $label ) {
    // Translation array for the titles
    $translations = [
     "description" => "الوصف",
     "description-simple" => "الوصف",
     "actor" => "المستخدم",
     "actor-simple" => "المستخدم",
     "preconditions" => "الشروط المسبقة",
     "preconditions-simple" => "الشروط المسبقة",
     "postconditions" => "الشروط اللاحقة",
     "postconditions-simple" => "الشروط اللاحقة",
     "main_flow_steps" => "الخطوات الرئيسية",
     "main_flow_steps-simple" => "الخطوات الرئيسية",
     "alternative_flow_steps" => "الخطوات البديلة",
     "exception_flow_steps" => "الخطوات الاستثنائية",
     "trigger" => "المحفز",
     "priority" => "الأولوية",
     "business_rules" => "القواعد التجارية",
     "assumptions" => "الافتراضات",
     "frequency_of_use" => "تكرار الاستخدام",
     "special_requirements" => "المتطلبات الخاصة",
     "notes_and_issues" => "الملاحظات والمشاكل",
     "events_sequence" => "تسلسل الأحداث",
     "inputs" => "المدخلات",
     "outputs" => "المخرجات",
     "user_interactions" => "تفاعلات المستخدم",
     "special_conditions" => "الشروط الخاصة",
     "usage_scenarios" => "سيناريوهات الاستخدام",
     "security_requirements" => "متطلبات الأمان",
     "integration_with_other_systems" => "التكامل مع الأنظمة الأخرى",
     "constraints_and_assumptions" => "القيود والافتراضات",
     "testing_requirements" => "متطلبات الاختبار",
     "flowchart_mermaid" => "مخطط التدفق",
     'backend_details' => 'تفاصيل ال API',
     'frontend_details' => 'تفاصيل الواجهات',
     'notifications' => 'الاشعارات',
 ];

 $html = '';

 
 foreach ($useCasesData as $key => $useCase) {
    

     $pure_case_id = $label . '.' . $key+1;
     // Add Use Case Title
     if (isset($useCase['description'])) {
         $html .= '<h4>' . $pure_case_id . '- ' . $useCase['description'] . '</h4>';
     } else {
         $html .= '<h4>' . $pure_case_id . '</h4>';
     }

     // Add Flowchart if exists
     if (isset($useCase['flowchart_mermaid'])) {
        // krumo($useCase['flowchart_mermaid']);
        $mermaid_code = str_replace('TD', 'LR', $useCase['flowchart_mermaid']); 
        $html .= '<div class="mermaid">' .  $mermaid_code  . '</div>';
     }

     // Add Use Case Details Table
     $html .= '<table>';
     $html .= '<thead><tr><th scope="col">العنوان</th><th>التفاصيل</th scope="col"></tr></thead>';
     $html .= '<tbody>';

     foreach ($useCase as $key => $value) {
         
        // krumo($value);

         if ($key === 'flowchart_mermaid') {
             continue; // Skip flowchart_mermaid key for now
         }

         if( _not_allowed( $key )  ) {
             continue; 
         }
         if( empty($value) ) {
             continue; 
         }

         $html .= '<tr>';
         $html .= '<td>' . (isset($translations[$key]) ? $translations[$key] : $key) . '</td>';
         $html .= '<td>';
         if (is_array($value)) {
            $keyArray = ['alternative_flow_steps', 'exception_flow_steps'];
            if( in_array($key,  $keyArray)) {
                foreach ($value as $k => $val) {
                    if( isset($val['condition']) ) {
                        $html .= '<strong>'.$val['condition'].'</strong><br>' ;
                        }
                        
                        if( isset($val['steps']) && is_array(($val['steps']))) {
                            $html .= '<ul>';
                            foreach ((array)$val['steps'] as $step) {
                                $html .= '<li>'. $step['step'] .'</li>';
                            }
                        $html .= '</ul>';
                    }
                    if( isset($val['event']) ) {
                        $html .= $val['event'] . '<br>' ;
                    }
                    if( isset($val['rule']) ) {
                        $html .= $val['rule'] ;
                    }
                }
            } elseif( $key === 'notifications' ){ 
                 if( !empty($value) ) {
                     // prr($value);
                     foreach ($value as $val) {
                         // prr($val); 
                         $html .= '<p>';
                         $html .= '<strong>العنوان : </strong><span>'. $val['title'] .'</span><br>';
                         $html .= '<strong>الرسالة : </strong><span>'. $val['message'] .'</span><br>';
                         $html .= '<strong>عن طريق : </strong><span> ' .implode(', ', (array)$val['methods'] ) . '</span>&nbsp;&nbsp;';
                         $html .= '<strong>المستقبل : </strong><span>'. $val['recipient'] .'</span>';
                         $html .= '</p>';
 
                     } 
                 }
             } elseif( $key === 'backend_details' ){
                    // krumo($value);
                   $html .= BackendDetailsTable($value[0]);
             } elseif( $key === 'frontend_details' ) {
                   $html .= FrontendDetails($value);
             } elseif( $key === 'inputs' ) {
                foreach ($value as $k => $val) {
                    $html .= $val['input'] . ' | ';
                }
            } else {
                 foreach ($value as $item) {
                     $html .= (is_array($item) ? _array_to_multiline_string($item , false) : $item);
                 }
            }
         } else {
             $html .= _array_to_multiline_string($value);
         }
        $html .= '</td>';
        $html .= '</tr>';
    }
    $html .= '</tbody>';
    $html .= '</table>';

     
 }

 return $html;
}

function FrontendDetails($frontendDetails) {
    $html = '';
    if( isset($frontendDetails[0]['screens']) && is_array($frontendDetails[0]['screens']) ) {
    foreach ($frontendDetails[0]['screens'] as $key => $screen) {
        $html .= '<hr><strong>' . $screen['name'] . '</strong><br><hr>';
        $html .= '<ul>';

        $formLabelAdded = false; // تحديد إذا ما تم إضافة عنوان "form"
        if( isset($screen['components']) && is_array($screen['components']) ) {
            foreach ($screen['components'] as $component) {
                if ($component['acf_fc_layout'] === 'form') {
                    if (!$formLabelAdded) {
                        $html .= '<li><strong>' . $component['acf_fc_layout'] . '</strong>';
                        $formLabelAdded = true; // تحديد أن العنوان تم إضافته
                    }

                    // تجميع تفاصيل النموذج
                    $consolidatedForm = [
                        'inputs' => [],
                        'submit_btn_message' => '',
                        'success_message' => '',
                        'success_redirect' => '',
                    ];

                    if (isset($component['inputs'])) {
                        foreach ($component['inputs'] as $input) {
                            if (is_array($input) && isset($input['type'])) {
                                $consolidatedForm['inputs'][] = $input;
                            }
                        }
                    }
                    if (isset($component['submit_btn_message']) && !empty($component['submit_btn_message'])) {
                        $consolidatedForm['submit_btn_message'] .= $component['submit_btn_message'];
                    }
                    if (isset($component['success_message']) && !empty($component['success_message'])) {
                        $consolidatedForm['success_message'] .= $component['success_message'];
                    }
                    if (isset($component['success_redirect']) && !empty($component['success_redirect'])) {
                        $consolidatedForm['success_redirect'] .= $component['success_redirect'];
                    }

                    // Filter out empty values from the consolidated form inputs
                    $consolidatedForm['inputs'] = array_filter($consolidatedForm['inputs'], function($input) {
                        return array_filter($input, function($value) {
                            return !empty($value);
                        });
                    });

                    // Add consolidated form details to the HTML, excluding empty values
                    $consolidatedHtml = render_frontend_array($consolidatedForm['inputs'], 'form');
                    if (!empty($consolidatedHtml)) {
                        $html .= $consolidatedHtml;
                    }

                    if (!empty($consolidatedForm['submit_btn_message'])) {
                        $html .= '<li><strong>رسالة زر الإرسال:</strong> ' . $consolidatedForm['submit_btn_message'] . '</li>';
                    }
                    if (!empty($consolidatedForm['success_message'])) {
                        $html .= '<li><strong>رسالة النجاح:</strong> ' . $consolidatedForm['success_message'] . '</li>';
                    }
                    if (!empty($consolidatedForm['success_redirect'])) {
                        $html .= '<li><strong>إعادة توجيه النجاح:</strong> ' . $consolidatedForm['success_redirect'] . '</li>';
                    }

                    $html .= '</li>';
                } else {
                    $html .= '<li><strong>' . $component['acf_fc_layout'] . '</strong></li>';
                    if (isset($component['text'])) {
                        $html .= render_frontend_array($component['text'], 'text');
                    }
                    if (isset($component['items'])) {
                        $html .= render_frontend_array($component['items'], 'list');
                    }
                    if (isset($component['columns'])) {
                        $html .= render_frontend_array($component['columns'], 'table');
                    }
                }
            }
        }

        $html .= '</ul>';
    }
}

    return $html;
}

function render_frontend_array($data, $layout) {
    $html = '';
    if( !empty($layout) ) {
        switch ($layout) {
            case 'form':
                foreach ($data as $key => $value) {
                    $html .= '<ul>';
                        if( isset($value['type']) ){
                            $html .= '<li>' . $value['type'];
                            $html .= '<ul>';              
                        }
                        if( isset($value['label']) && !empty($value['label'])){
                            $html .= '<li>العنوان : ' . $value['label'] . '</li>';
                        }
                        if( isset($value['validation']) && !empty($value['validation'])){
                            $html .= '<li>التحقق : ' . $value['validation'] . '</li>';
                        }
                        if( isset($value['options']) && !empty($value['options'])){
                            $html .= '<li>الخيارات : ' . $value['options'] . '</li>';
                        }
                    $html .= '</ul>';
                    $html .= '</li>';
                    $html .= '</ul>';
                }
                break;
            case 'list':
                foreach ($data as $key => $value) {
                    $html .= '<ul>';
                        if( isset($value['content']) ){
                            $html .= '<li>النوع : ' . $value['content']  . '</li>';
                        }
                    $html .= '</ul><br>';
                }
                break;
            case 'table':
                foreach ($data as $key => $value) {
                    $html .= '<ul>';
                        if( isset($value['title']) ){
                            $html .= '<li>الاسم : ' . $value['title']  . '</li>';
                        }
                        if( isset($value['content-render-format']) ){
                            $html .= '<li>نوع المحتوي : ' . $value['content-render-format']  . '</li>';
                        }
                    $html .= '</ul><br>';
                }
                break; 
            case 'text':
                    $html .= '<ul>';
                        $html .= '<li>' . $data  . '</li>';
                    $html .= '</ul><br>';
                
                break;      
        }
    }
    return $html;
}

function BackendDetailsTable($backendDetails) {
    $html = '';

    foreach ($backendDetails['api_endpoints'] as $endpoint) {
        $html .= '<div style="direction: rtl; text-align: right;">';
        $html .= '<strong>Method</strong>: ' . $endpoint['method'] . ' ||  <strong>Endpoint</strong>: ' . $endpoint['endpoint'] . '<hr>';
        // Request Body and Headers
        $html .= '<strong>Request Headers</strong><br>';
        if (isset($endpoint['request'][0]['headers'])) {
            $html .=  $endpoint['request'][0]['headers'] . '<hr>';
        }
        $html .= '<table>';
        $html .= '<tr>';
        $html .= '<td style="vertical-align: top;">';
        $html .= '<p><strong>Request Body</strong></p>';
        if (isset($endpoint['request'][0]['body'])) {
            $html .= '<p>' . $endpoint['request'][0]['body'] . '</p>';
        }
        
        $html .= '</td>';
        
        // Response
        $html .= '<td style="vertical-align: top;">';
        $html .= '<p><strong>Response</strong></p>';
        
        // Response details header
        $html .= '<table>';
        $html .= '<tr>';
        $html .= '<th>الحالة</th>';
        $html .= '<th>المحتوى</th>';
        $html .= '</tr>';
        
        // Response details
        foreach ($endpoint['response'] as $response) {
            $html .= '<tr>';
            $html .= '<td>' . $response['status_code'] . '</td>';
            $html .= '<td>';
            if (isset($response['body'])) {
                $html .= $response['body'];
            }
            if (isset($response['description'])) {
                $html .= '<br><strong>الوصف: </strong>' . $response['description'];
            }
            $html .= '</td>';
            $html .= '</tr>';
        }
        $html .= '</table>';
        $html .= '</td>';
        $html .= '</tr>';
        $html .= '</table>';
        
        $html .= '</div>'; // Close main div
        $html .= '<hr>'; // Add a horizontal line between endpoints
    }

    return $html;
}



function _array_to_multiline_string($data, $k = true) {
    $html = '';
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                
                if ($key === 'inputs' || $key === 'items' || $key === 'columns') {
                    $html .= _inputs_to_multiline_string($value);
                } else {
                    if ($k && !is_numeric($key)) {
                        $html .= '<li><strong>' . ucfirst($key) . ':</strong> ' . _remove_ul_with_class(_array_to_multiline_string($value, $k)) . '</li>';
                    } else {
                        $html .= '<li>' . _remove_ul_with_class(_array_to_multiline_string($value, $k)) . '</li>';
                    }
                }
            } else {
                if (!is_numeric($key)) {
                    if ($k) {
                        $html .= '<li><strong>' . ucfirst($key) . ':</strong> ' . $value . '</li>';
                    } else {
                        $html .= '<li>' . $value . '</li>';
                    }
                }
            }
        }
        if ($html) {
            $html = '<ul>' . $html . '</ul>';
        }
    } else {
        $html = $data;
    }
    return $html;
}



function _inputs_to_multiline_string($inputs) {
    $html = '';
    foreach ($inputs as $input) {
        $html .= '<li>';
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                if (is_array($value)) {
                    $html .= _array_to_multiline_string($value);
                } else {
                    $html .= '<strong>' . ucfirst($key) . ':</strong> ' . $value . '<br>';
                }
            }
        } else {
            $html .= $input . '<br>';
        }
        $html .= '</li>';
    }
    if ($html) {
        $html = '<ul>' . $html . '</ul>';
    }
    return $html;
}

function _remove_ul_with_class($html) {
    return preg_replace('/<ul class="ul-numbered">/', '<ul>', $html, 1);
}


// Function to check if a label is not allowed
function _not_allowed($label) {
    $not_allowed = array(
        'case_id', 'id', 'is_new', 'description', 'description_simple', 'actor_simple', 
        'preconditions_simple', 'postconditions_simple', 'main_flow_steps_simple', 
        'trigger', 'frequency_of_use', 'flowchart_mermaid', 'رقم التسلسل', 
        'priority', 'main_flow_steps'
    );
    return in_array($label, $not_allowed);
}

// Function to handle labels (translation)
function label_handler($label) {
    $array = array(
        'goal' => false, 'stakeholder' => false, 'step' => false, 'story' => false, 
        'indicators' => false, 'condition' => false, 'rule' => false, 'assumption' => false, 
        'event' => false, 'requirement' => false, 'name' => false, 'method' => false, 
        'scenario' => false, 'input' => false, 'system' => false,
    );

    $enable_translate = false;
    if ($enable_translate) {
        $array = array_merge($array, array(
            'case_id' => 'الحالة رقم', 'description' => 'الوصف', 'actor' => 'الشخصية', 
            'preconditions' => 'الظروف المسبقة', 'postconditions' => 'النتائج', 
            'main_flow_steps' => 'الخطوات الأساسية', 'alternative_flow_steps' => 'الخطوات البديلة', 
            'exception_flow_steps' => 'خطوات استثنائية', 'steps'=> 'الخطوات', 'priority' => 'الأولوية', 
            'business_rules' => 'اشتراطات العمل', 'assumptions' => 'الافتراضات المسبقة', 
            'special_requirements' => 'متطلبات خاصة', 'notes_and_issues' => 'ملاحظات', 
            'events_sequence' => 'تسلسل الأحداث', 'inputs' => 'المدخلات', 'outputs' => 'المخرجات', 
            'user_interactions' => 'تفاعلات المستخدم', 'special_conditions' => 'حالات خاصة', 
            'usage_scenarios' => 'سيناريوهات الاستخدام', 'security_requirements' => 'متطلبات الامنية', 
            'integration_with_other_systems' => 'التكامل مع الأنظمة الأخرى', 'constraints_and_assumptions'=> 'الحدود والافتراضات', 
            'testing_requirements' => 'متطلبات الاختبار', 'backend_details' => 'تفاصيل ال API', 
            'frontend_details' => 'تفاصيل الواجهات', 'method' => 'الطريقة', 'endpoint' => 'رابط النقطة', 
            'request' => 'تفاصيل الطلب', 'screens' => 'الشاشات', 'components' => 'مكونات الشاشة', 
            'notifications' => 'الاشعارات', 'methods' => 'عن طريق', 'recipient' => 'المستلم', 
            'title' => 'عنوان', 'message' => 'نص الاشعار', 'type' => 'النوع', 'props' => 'الخصائص', 
            'response' => 'الاستجابة', 'body' => 'المحتوى', 'headers' => 'مرفقات', 'status_code' => 'كود الطلب', 
            'الجهات المعنية' => 'المستخدمين',
        ));
    }

    return $array[$label] ?? $label;
}

// Function to handle values based on labels
function value_handler($label, $value) {
    if ($label == 'alternative_flow_steps') {
        return $value['steps'];
    }
    if ($label == 'backend_details') {
        return $value[0]['api_endpoints'];
    }
    return $value;
}

// Function to display text fields
function display_text_field($value, $label = false) {
    $label = label_handler($label);
    if ($label) {
        echo "<strong>$label:</strong> $value<br>";
    } else {
        echo "$value<br>";
    }
}

// Function to generate values for main case info repeaters
function generate_main_case_info_repeater_value($repeater) {
    echo "<ul>";
    foreach ($repeater as $item) {
        echo "<li>";
        foreach ($item as $sub_label => $sub_value) {
            if (_not_allowed($sub_label) || empty($sub_value)) continue;
            $sub_value = value_handler($sub_label, $sub_value);
            if ($sub_label === 'تفاصيل ال API') {
                // Handle API details if needed
            } elseif (is_array($sub_value)) {
                display_repeater_field($sub_value, $sub_label);
            } elseif (is_string($sub_value)) {
                display_text_field($sub_value, $sub_label);
            }
        }
        echo "</li>";
    }
    echo "</ul>";
}

// Function to display repeater fields
function display_repeater_field(array $repeater, $label = false) {
    if (_not_allowed($label)) return;
    $label = label_handler($label);
    echo "<tr><td>$label</td>";
    echo "<td><ul>";
    foreach ($repeater as $item) {
        echo "<li>";
        foreach ($item as $sub_label => $sub_value) {
            if (_not_allowed($sub_label) || empty($sub_value)) continue;
            $sub_value = value_handler($sub_label, $sub_value);
            if ($sub_label === 'تفاصيل ال API') {
                // Handle API details if needed
            } elseif (is_array($sub_value)) {
                display_repeater_field($sub_value, $sub_label);
            } elseif (is_string($sub_value)) {
                display_text_field($sub_value, $sub_label);
            }
        }
        echo "</li>";
    }
    echo "</ul></td></tr>";
}

// Function to generate table for cases
function generateTableForCases($cases) {
    $html = '';
    $fields = [
        'case_id' => 'معرف الحالة', 'description' => 'الوصف', 'actor' => 'الممثل', 
        'preconditions' => 'الشروط المسبقة', 'postconditions' => 'الشروط اللاحقة', 
        'main_flow_steps' => 'خطوات التدفق الرئيسية', 'alternative_flow_steps' => 'الخطوات البديلة', 
        'exception_flow_steps' => 'خطوات تدفق الاستثناءات', 'trigger' => 'الحدث المسبب', 
        'priority' => 'الأولوية', 'business_rules' => 'القواعد التجارية', 'assumptions' => 'الافتراضات', 
        'frequency_of_use' => 'وتيرة الاستخدام', 'special_requirements' => 'المتطلبات الخاصة', 
        'notes_and_issues' => 'ملاحظات وقضايا', 'inputs' => 'المدخلات', 'outputs' => 'المخرجات', 
        'user_interactions' => 'تفاعلات المستخدم', 'special_conditions' => 'الشروط الخاصة', 
        'usage_scenarios' => 'سيناريوهات الاستخدام', 'security_requirements' => 'متطلبات الأمان', 
        'integration_with_other_systems' => 'التكامل مع الأنظمة الأخرى', 'constraints_and_assumptions' => 'القيود والافتراضات', 
        'testing_requirements' => 'متطلبات الاختبار', 'backend_details' => 'تفاصيل الخلفية', 
        'frontend_details' => 'تفاصيل الواجهة الأمامية', 'notifications' => 'الإشعارات'
    ];
    foreach ($cases as $id => $case) {
        $case_id = "3." . get_field('id') . ".2." . ($id + 1);
        $html .= '<h3>' . $case_id . ' : ' . $case['description_simple'] . '</h3>';
        $html .= '<table border="1">';
        $html .= '<thead><tr><th>العنوان</th><th>التفاصيل</th></tr></thead><tbody>';
        foreach ($fields as $key => $title) {
            if (!isset($case[$key])) continue;
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($title) . '</td>';
            $html .= '<td>';
            if (is_array($case[$key])) {
                $html .= '<ul>';
                foreach ($case[$key] as $item) {
                    if (is_array($item)) {
                        $html .= '<li>' . htmlspecialchars(implode('<br>', array_map(function ($subItem) {
                            return $subItem['step'] ?? $subItem['condition'] ?? $subItem['rule'] ?? $subItem['event'] ?? $subItem['input'] ?? $subItem['output'] ?? $subItem['requirement'] ?? '';
                        }, $item))) . '</li>';
                    } else {
                        $html .= '<li>' . htmlspecialchars($item) . '</li>';
                    }
                }
                $html .= '</ul>';
            } else {
                $html .= htmlspecialchars($case[$key]);
            }
            $html .= '</td>';
            $html .= '</tr>';
        }
        $html .= '</tbody></table>';
    }
    return $html;
}

// Function to generate values for main case info
function generate_main_case_info_value($sub_label, $sub_value) {
    if (is_string($sub_value)) {
        echo $sub_value;
    } elseif (is_array($sub_value)) {
        generate_main_case_info_repeater_value($sub_value);
    }
}

// Function to display ACF fields
function display_acf_fields($post_id) {
    $fields = get_field_objects($post_id);
    $cases = [];
    ?>
    <table>
        <thead>
            <tr>
                <th>العنوان</th>
                <th>التفاصيل</th>
            </tr>
        </thead>
        <tbody>
        <?php
        foreach ($fields as $field_name => $field) {
            $label = $field['label'];
            $value = $field['value'];
            if ( _not_allowed($label)) {
                continue;
            }
            if ($field['name'] == 'use_cases') {
                $cases = $field;
                continue;
            }
            if( !empty($value) ) {
                display_repeater_field($value, $label);
            }
        }
        ?>
        </tbody>
    </table>
    <h3>
        <?php
        $id = get_field('id');
        $id_label = "3." . $id;
        echo "<h3>" . $id_label . ".2 حالات الاستخدام</h3>";
        ?>
    </h3>
    <?php
    $cases = $fields['use_cases']['value'];
    echo generatePostUseCasesTable($cases  , $id_label . ".2" );
    // echo generateTableForCases($cases);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SRS DOC</title>
    <style>
        table {
            border: 1px solid #dededf;
            table-layout: fixed;
            border-collapse: collapse;
            border-spacing: 1px;
        }

        table caption {
            caption-side: top;
        }

        table th {
            border: 1px solid #dededf;
            background-color: #eceff1;
            color: #000000;
            padding: 0px;
        }

        table td {
            border: 1px solid #dededf;
            background-color: #ffffff;
            color: #000000;
            margin: 0px;
            padding-right: 3px
        }
        ul{
            margin-top: 0px;
            margin-bottom: 0px;
            padding-top: 0px;
            padding-bottom: 0px;
        }
        h2,h3{
            margin-top: 3px;
        }
        h4{
            margin-top: 25px;
            margin-bottom: 1px;
        }
        ul.ul-numbered {
            list-style-type: decimal;
        }
    </style>
</head>
<bod>
<?php
$args = array(
    'post_type'         => 'requirement',
    'post_status'       => 'publish',
    'posts_per_page'    => -1
);
$requirements_posts = new WP_Query($args);
if ($requirements_posts->have_posts()) :
    while ($requirements_posts->have_posts()) : $requirements_posts->the_post();
        $id = get_field('id');
        $id_label = "3." . $id;
        ?>
        <div style="direction: rtl;">
            <h2><?php echo $id_label; ?> - <?php the_title(); ?></h2>
            <h3><?php echo $id_label . '.1 المعلومات العامة'; ?></h3>
            <div>
                <?php display_acf_fields(get_the_ID()); ?>
            </div>
        </div>
        <?php
    endwhile;
else :
    echo 'لا توجد منشورات لعرضها.';
endif;
?>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Mermaid
    mermaid.initialize({
        startOnLoad: true,
        // theme: 'forest', // Change the theme name here dark forest neutral slate
        fontFamily: '"Helvetica Neue",sans-serif', // Change the font family name here
        fontSize: 16,
        theme: 'base',
        themeVariables: {
        primaryColor: '#FFFAB7',
        primaryTextColor: '#000',
        primaryBorderColor: '#8576FF',
        lineColor: '#FFA62F',
        secondaryColor: '#f9f9f9',
        tertiaryColor: '#000'
        }
    });

    // Function to convert SVG to canvas and then to image
    function convertSvgToImage(element) {
        const svgElement = element.querySelector('svg');

        if (svgElement) {
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            // Extract the max-width from the SVG's style attribute
            const style = svgElement.getAttribute('style');
            const maxWidthMatch = style.match(/max-width:\s*([\d.]+)px/);
            const maxWidth = maxWidthMatch ? parseFloat(maxWidthMatch[1]) : svgElement.clientWidth;

            img.onload = function() {
                // Calculate the aspect ratio
                const aspectRatio = img.width / img.height;

                // Set canvas dimensions based on the max-width and aspect ratio
                const scale = 1; // Adjust the scale factor as needed
                canvas.width = maxWidth * scale;
                canvas.height = (maxWidth / aspectRatio) * scale;

                // Draw the image on the canvas
                ctx.scale(scale, scale);
                ctx.drawImage(img, 0, 0, maxWidth, maxWidth / aspectRatio);

                // Convert canvas to data URL (PNG or JPEG)
                const imgData = canvas.toDataURL('image/png'); // Change to 'image/jpeg' for JPEG format

                // Create an image element
                const imgElement = document.createElement('img');
                imgElement.src = imgData;

                // Insert the image inside the Mermaid div
                element.insertBefore(imgElement, svgElement);

                // Remove the original SVG
                element.removeChild(svgElement);

                // Optionally, provide a download link
                // const link = document.createElement('a');
                // link.href = imgData;
                // link.download = 'diagram.png'; // Change to 'diagram.jpg' for JPEG format
                // link.textContent = 'Download Diagram';
                // element.parentNode.insertBefore(link, imgElement.nextSibling);
            };

            // Set the source of the image to be the SVG data
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
    }

    // Convert all Mermaid diagrams after rendering
    document.querySelectorAll('.mermaid').forEach((element) => {
        // Use a mutation observer to detect when the SVG is added to the DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeName === 'svg') {
                        observer.disconnect();
                        convertSvgToImage(element);
                    }
                });
            });
        });

        observer.observe(element, { childList: true });
    });

    // Trigger Mermaid rendering
    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
});
</script>
</body>
</html>
