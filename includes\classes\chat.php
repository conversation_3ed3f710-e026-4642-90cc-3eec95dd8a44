<?php

class ANALYST_CHAT{


    public function __construct(){
        add_filter('theme_page_templates', array( $this, 'custom_page_template_register' ) );
        add_filter('template_include', array( $this, 'custom_page_template_file_path' ) );
        add_action('rest_api_init', array($this, 'api_endpoint_register'));
    }

    public function custom_page_template_register($templates) {
        $templates['analyst-chat-page-template.php'] = 'Chat Page';
        return $templates;
    }

    public function custom_page_template_file_path($template) {
        global $post;

        if (!$post) {
            return $template;
        }

        if (get_post_meta( $post->ID, '_wp_page_template', true ) == 'analyst-chat-page-template.php') {
            $file = ANALYST_PLUGIN_DIR . 'views/analyst-chat-page-template.php';
            if (file_exists($file)) {
                return $file;
            }
        }

        return $template;
    }
 
    public function api_endpoint_register() {
        // http://analyst/wp-json/system_analyst/v1/chat
        register_rest_route( ANALYST_SLUG . '/v1', '/chat', array(
            'methods' => 'GET',
            'callback' => array($this, 'handle_api_request'),
            'permission_callback' => '__return_true', // يمكن تعديل هذا للتحقق من الأذونات
        ));
    }

    // معالجة الطلب
    public function handle_api_request( $request ) {

        require_once ANALYST_PLUGIN_DIR . 'includes/classes/OpenAIFineTuner.php';
        $message = $request->get_param('message');

        if (empty($message)) {
            return new WP_REST_Response(['error' => 'No message provided.'], 400);
        }
        $fineTuner = new OpenAIFineTuner();
        $response = $fineTuner->generateText( $message );
        $return = array(
            'message' => $response ,
            'ID'      => 1
        );
        wp_send_json_success( $return );
    }

}
new ANALYST_CHAT;