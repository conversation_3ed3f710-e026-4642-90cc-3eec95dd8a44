<?php

/**
 * Class to read requirement data from JSON files instead of database
 */
class Requirements_JSON_Reader {

    private $requirements_dir;

    public function __construct() {
        $this->requirements_dir = ANALYST_PLUGIN_DIR . 'requirments/';

        // Hook into ACF load_value to return JSON data instead of database data
        add_filter('acf/load_value', array($this, 'load_value_from_json'), 10, 3);

        // Also hook into get_field function directly
        add_filter('acf/pre_load_value', array($this, 'pre_load_value_from_json'), 10, 3);

        // Hook into format_value to ensure proper formatting
        add_filter('acf/format_value', array($this, 'format_value_from_json'), 10, 3);
    }

    /**
     * Load ACF field value from JSON instead of database for requirement posts
     * 
     * @param mixed $value The field value from database
     * @param int $post_id The post ID
     * @param array $field The field array
     * @return mixed The field value from JSON or original value
     */
    public function load_value_from_json($value, $post_id, $field) {
        // Only for requirement post type
        if (get_post_type($post_id) !== 'requirement') {
            return $value;
        }

        // Get requirement data from JSON
        $requirement_data = $this->get_requirement_data_by_post_id($post_id);
        
        if (!$requirement_data) {
            return $value; // Fallback to database value
        }

        // Return field value from JSON
        $field_name = $field['name'];
        return isset($requirement_data[$field_name]) ? $requirement_data[$field_name] : $value;
    }

    /**
     * Pre-load ACF field value from JSON instead of database for requirement posts
     * This hook runs before the database query
     *
     * @param mixed $value The field value (null at this point)
     * @param int $post_id The post ID
     * @param array $field The field array
     * @return mixed The field value from JSON or null to continue with database
     */
    public function pre_load_value_from_json($value, $post_id, $field) {
        // Only for requirement post type
        if (get_post_type($post_id) !== 'requirement') {
            return $value; // Continue with normal ACF behavior
        }

        // Get requirement data from JSON
        $requirement_data = $this->get_requirement_data_by_post_id($post_id);

        if (!$requirement_data) {
            return $value; // Continue with normal ACF behavior
        }

        // Return field value from JSON
        $field_name = $field['name'];
        if (isset($requirement_data[$field_name])) {
            $field_value = $requirement_data[$field_name];

            // Handle repeater fields specially
            if (isset($field['type']) && $field['type'] === 'repeater') {
                return $this->format_repeater_for_acf($field_value);
            }

            return $field_value;
        }

        return $value; // Continue with normal ACF behavior if field not found in JSON
    }

    /**
     * Format repeater field data for ACF compatibility
     *
     * @param mixed $repeater_data The repeater data from JSON
     * @return array Formatted repeater data
     */
    private function format_repeater_for_acf($repeater_data) {
        if (!is_array($repeater_data)) {
            return array();
        }

        // ACF expects repeater data in a specific format
        $formatted = array();

        foreach ($repeater_data as $index => $row) {
            if (is_array($row)) {
                $formatted[$index] = $row;
            } else {
                // If it's a simple value, wrap it in an array
                $formatted[$index] = array('value' => $row);
            }
        }

        return $formatted;
    }

    /**
     * Format field value from JSON for proper display
     *
     * @param mixed $value The field value
     * @param int $post_id The post ID
     * @param array $field The field array
     * @return mixed Formatted field value
     */
    public function format_value_from_json($value, $post_id, $field) {
        // Only for requirement post type
        if (get_post_type($post_id) !== 'requirement') {
            return $value;
        }

        // If we already have a value from database/cache, don't override
        if (!empty($value)) {
            return $value;
        }

        // Get requirement data from JSON
        $requirement_data = $this->get_requirement_data_by_post_id($post_id);

        if (!$requirement_data) {
            return $value;
        }

        // Return field value from JSON
        $field_name = $field['name'];
        if (isset($requirement_data[$field_name])) {
            return $requirement_data[$field_name];
        }

        return $value;
    }

    /**
     * Get requirement data by WordPress post ID
     * 
     * @param int $post_id WordPress post ID
     * @return array|false Requirement data or false if not found
     */
    public function get_requirement_data_by_post_id($post_id) {
        // Get folder name from post meta
        $folder_name = get_post_meta($post_id, '_requirement_folder', true);
        
        if (!$folder_name) {
            return false;
        }

        return $this->get_requirement_data_by_folder($folder_name);
    }

    /**
     * Get requirement data by folder name
     * 
     * @param string $folder_name Folder name
     * @return array|false Requirement data or false if not found
     */
    public function get_requirement_data_by_folder($folder_name) {
        $folder_path = $this->requirements_dir . $folder_name;
        $json_file = $folder_path . '/data.json';

        if (!file_exists($json_file)) {
            return false;
        }

        $json_content = file_get_contents($json_file);
        if ($json_content === false) {
            return false;
        }

        $data = json_decode($json_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return $data;
    }

    /**
     * Get requirement data by requirement ID (ACF field)
     * 
     * @param string $requirement_id The requirement ID from ACF
     * @return array|false Requirement data or false if not found
     */
    public function get_requirement_data_by_id($requirement_id) {
        $folders = $this->get_all_requirement_folders();
        
        foreach ($folders as $folder_name) {
            $data = $this->get_requirement_data_by_folder($folder_name);
            if ($data && isset($data['id']) && $data['id'] == $requirement_id) {
                return $data;
            }
        }

        return false;
    }

    /**
     * Get all requirements data
     * 
     * @return array Array of all requirements data
     */
    public function get_all_requirements() {
        $requirements = array();
        $folders = $this->get_all_requirement_folders();

        foreach ($folders as $folder_name) {
            $data = $this->get_requirement_data_by_folder($folder_name);
            if ($data) {
                $data['_folder_name'] = $folder_name; // Add folder name for reference
                $requirements[] = $data;
            }
        }

        return $requirements;
    }

    /**
     * Get all requirement folder names
     * 
     * @return array Array of folder names
     */
    private function get_all_requirement_folders() {
        if (!is_dir($this->requirements_dir)) {
            return array();
        }

        $folders = array();
        $directories = glob($this->requirements_dir . '*', GLOB_ONLYDIR);

        foreach ($directories as $dir) {
            $folder_name = basename($dir);
            $json_file = $dir . '/data.json';
            
            // Only include folders that have data.json
            if (file_exists($json_file)) {
                $folders[] = $folder_name;
            }
        }

        return $folders;
    }

    /**
     * Get requirements sorted by ID
     * 
     * @return array Array of requirements sorted by ID
     */
    public function get_requirements_sorted_by_id() {
        $requirements = $this->get_all_requirements();

        // Sort by ID
        usort($requirements, function($a, $b) {
            $id_a = isset($a['id']) ? $a['id'] : '';
            $id_b = isset($b['id']) ? $b['id'] : '';
            return strcmp($id_a, $id_b);
        });

        return $requirements;
    }

    /**
     * Check if requirement exists by ID
     * 
     * @param string $requirement_id The requirement ID
     * @return bool True if exists, false otherwise
     */
    public function requirement_exists($requirement_id) {
        return $this->get_requirement_data_by_id($requirement_id) !== false;
    }

    /**
     * Get requirement field value by post ID and field name
     * 
     * @param int $post_id WordPress post ID
     * @param string $field_name ACF field name
     * @return mixed Field value or false if not found
     */
    public function get_field_value($post_id, $field_name) {
        $data = $this->get_requirement_data_by_post_id($post_id);
        
        if (!$data) {
            return false;
        }

        return isset($data[$field_name]) ? $data[$field_name] : false;
    }

    /**
     * Get requirement title by requirement ID
     * 
     * @param string $requirement_id The requirement ID
     * @return string|false Title or false if not found
     */
    public function get_requirement_title($requirement_id) {
        $data = $this->get_requirement_data_by_id($requirement_id);
        
        if (!$data) {
            return false;
        }

        return isset($data['title']) ? $data['title'] : false;
    }

    /**
     * Get statistics about requirements
     * 
     * @return array Statistics array
     */
    public function get_requirements_stats() {
        $requirements = $this->get_all_requirements();
        
        $stats = array(
            'total_count' => count($requirements),
            'with_use_cases' => 0,
            'total_use_cases' => 0,
            'folders' => array()
        );

        foreach ($requirements as $req) {
            if (isset($req['use_cases']) && is_array($req['use_cases']) && !empty($req['use_cases'])) {
                $stats['with_use_cases']++;
                $stats['total_use_cases'] += count($req['use_cases']);
            }
            
            if (isset($req['_folder_name'])) {
                $stats['folders'][] = $req['_folder_name'];
            }
        }

        return $stats;
    }
}

// Initialize the JSON reader
new Requirements_JSON_Reader();
