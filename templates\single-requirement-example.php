<?php
/**
 * Example template for displaying a single requirement using JSON data
 * 
 * This template demonstrates how to use the new JSON-based functions
 * instead of get_field() for requirement posts.
 */

// Include WordPress
require_once('../../../../wp-load.php');

// Get requirement ID from URL parameter
$requirement_id = isset($_GET['req_id']) ? sanitize_text_field($_GET['req_id']) : '';

if (empty($requirement_id)) {
    echo '<h1>Please provide a requirement ID</h1>';
    echo '<p>Example: <a href="?req_id=4.2.1">?req_id=4.2.1</a></p>';
    exit;
}

// Get requirement data from JSON
$requirement = get_requirement_by_id($requirement_id);

if (!$requirement) {
    echo '<h1>Requirement not found</h1>';
    echo '<p>Requirement ID "' . esc_html($requirement_id) . '" was not found in JSON files.</p>';
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($requirement['title']); ?> - متطلب <?php echo esc_html($requirement['id']); ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 3px solid #2196f3;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .requirement-id {
            background: #2196f3;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            border-right: 4px solid #2196f3;
        }
        .section h3 {
            color: #1976d2;
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .list-item {
            background: white;
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 5px;
            border-right: 3px solid #4caf50;
        }
        .use-case {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .use-case h4 {
            color: #1976d2;
            margin-top: 0;
            background: #e3f2fd;
            padding: 10px;
            margin: -20px -20px 15px -20px;
            border-radius: 8px 8px 0 0;
        }
        .field-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        .field-value {
            margin-bottom: 15px;
            padding: 8px;
            background: #f8f8f8;
            border-radius: 4px;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #757575;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .back-link:hover {
            background: #424242;
        }
        .json-source {
            background: #fff3e0;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #e65100;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../tests/test-json-export.php" class="back-link">← العودة لصفحة الاختبار</a>
        
        <div class="json-source">
            <strong>📄 مصدر البيانات:</strong> ملف JSON - <?php echo esc_html($requirement['_folder_name'] ?? 'غير محدد'); ?>/data.json
        </div>

        <div class="header">
            <div class="requirement-id">متطلب <?php echo esc_html($requirement['id']); ?></div>
            <h1><?php echo esc_html($requirement['title']); ?></h1>
        </div>

        <?php if (isset($requirement['business_goals']) && is_array($requirement['business_goals']) && !empty($requirement['business_goals'])): ?>
        <div class="section">
            <h3>🎯 أهداف العمل</h3>
            <?php foreach ($requirement['business_goals'] as $goal): ?>
                <div class="list-item">
                    <?php echo esc_html(is_array($goal) ? $goal['goal'] : $goal); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['stakeholders']) && is_array($requirement['stakeholders']) && !empty($requirement['stakeholders'])): ?>
        <div class="section">
            <h3>👥 الجهات المعنية</h3>
            <?php foreach ($requirement['stakeholders'] as $stakeholder): ?>
                <div class="list-item">
                    <?php echo esc_html(is_array($stakeholder) ? $stakeholder['stakeholder'] : $stakeholder); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['main_steps']) && is_array($requirement['main_steps']) && !empty($requirement['main_steps'])): ?>
        <div class="section">
            <h3>📋 الخطوات الرئيسية</h3>
            <?php foreach ($requirement['main_steps'] as $index => $step): ?>
                <div class="list-item">
                    <strong><?php echo $index + 1; ?>.</strong> 
                    <?php echo esc_html(is_array($step) ? $step['step'] : $step); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['user_stories']) && is_array($requirement['user_stories']) && !empty($requirement['user_stories'])): ?>
        <div class="section">
            <h3>📖 قصص المستخدمين</h3>
            <?php foreach ($requirement['user_stories'] as $story): ?>
                <div class="list-item">
                    <?php echo esc_html(is_array($story) ? $story['story'] : $story); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($requirement['use_cases']) && is_array($requirement['use_cases']) && !empty($requirement['use_cases'])): ?>
        <div class="section">
            <h3>🔄 حالات الاستخدام</h3>
            <?php foreach ($requirement['use_cases'] as $index => $use_case): ?>
                <div class="use-case">
                    <h4>حالة الاستخدام <?php echo $index + 1; ?>
                        <?php if (isset($use_case['case_id']) && !empty($use_case['case_id'])): ?>
                            - <?php echo esc_html($use_case['case_id']); ?>
                        <?php endif; ?>
                    </h4>

                    <?php if (isset($use_case['description']) && !empty($use_case['description'])): ?>
                        <div class="field-label">الوصف:</div>
                        <div class="field-value"><?php echo esc_html($use_case['description']); ?></div>
                    <?php elseif (isset($use_case['description_simple']) && !empty($use_case['description_simple'])): ?>
                        <div class="field-label">الوصف المبسط:</div>
                        <div class="field-value"><?php echo esc_html($use_case['description_simple']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['actor']) && !empty($use_case['actor'])): ?>
                        <div class="field-label">الفاعل:</div>
                        <div class="field-value"><?php echo esc_html($use_case['actor']); ?></div>
                    <?php elseif (isset($use_case['actor_simple']) && !empty($use_case['actor_simple'])): ?>
                        <div class="field-label">الفاعل المبسط:</div>
                        <div class="field-value"><?php echo esc_html($use_case['actor_simple']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['trigger']) && !empty($use_case['trigger'])): ?>
                        <div class="field-label">المحفز:</div>
                        <div class="field-value"><?php echo esc_html($use_case['trigger']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['priority']) && !empty($use_case['priority'])): ?>
                        <div class="field-label">الأولوية:</div>
                        <div class="field-value"><?php echo esc_html($use_case['priority']); ?></div>
                    <?php endif; ?>

                    <?php if (isset($use_case['main_flow_steps']) && is_array($use_case['main_flow_steps']) && !empty($use_case['main_flow_steps'])): ?>
                        <div class="field-label">خطوات التدفق الرئيسي:</div>
                        <div class="field-value">
                            <ol>
                                <?php foreach ($use_case['main_flow_steps'] as $step): ?>
                                    <li><?php echo esc_html(is_array($step) ? $step['step'] : $step); ?></li>
                                <?php endforeach; ?>
                            </ol>
                        </div>
                    <?php elseif (isset($use_case['main_flow_steps_simple']) && is_array($use_case['main_flow_steps_simple']) && !empty($use_case['main_flow_steps_simple'])): ?>
                        <div class="field-label">خطوات التدفق الرئيسي المبسط:</div>
                        <div class="field-value">
                            <ol>
                                <?php foreach ($use_case['main_flow_steps_simple'] as $step): ?>
                                    <li><?php echo esc_html(is_array($step) ? $step['step'] : $step); ?></li>
                                <?php endforeach; ?>
                            </ol>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($use_case['preconditions']) && is_array($use_case['preconditions']) && !empty($use_case['preconditions'])): ?>
                        <div class="field-label">الشروط المسبقة:</div>
                        <div class="field-value">
                            <ul>
                                <?php foreach ($use_case['preconditions'] as $condition): ?>
                                    <li><?php echo esc_html(is_array($condition) ? $condition['condition'] : $condition); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($use_case['postconditions']) && is_array($use_case['postconditions']) && !empty($use_case['postconditions'])): ?>
                        <div class="field-label">الشروط اللاحقة:</div>
                        <div class="field-value">
                            <ul>
                                <?php foreach ($use_case['postconditions'] as $condition): ?>
                                    <li><?php echo esc_html(is_array($condition) ? $condition['condition'] : $condition); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h3>🔧 معلومات تقنية</h3>
            <div class="field-label">مجلد JSON:</div>
            <div class="field-value"><?php echo esc_html($requirement['_folder_name'] ?? 'غير محدد'); ?></div>
            
            <div class="field-label">عدد الحقول المحملة:</div>
            <div class="field-value"><?php echo count($requirement); ?> حقل</div>
            
            <div class="field-label">مصدر البيانات:</div>
            <div class="field-value">ملفات JSON (ليس قاعدة البيانات)</div>
        </div>
    </div>
</body>
</html>
