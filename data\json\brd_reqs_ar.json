{"4.2.1": {"title": "التسجيل في المنصة", "business_goals": ["التأكد من أن كل مستخدم للمنصة قادر على تقديم الخدمة أو طلب الخدمة بطريقة آمنة وفعالة.", "<PERSON><PERSON><PERSON> أن المعلومات المقدمة من المستخدمين صحيحة وحديثة."], "stakeholders": ["المستخدمون (العملاء، مقدمو الخدمات)", "إداريي النظام"], "main_steps": ["المستخدم الجديد يقوم بالتسجيل في النظام عن طريق ادخال بياناته.", "يقوم المستخدم بالقراءة والموافقة على شروط الخدمة وسياسة الخصوصية.", "المستخدم يقوم بتوثيق رقم هاتفه عن طريق رسالة OTP (اختياري لاداري النظام أن تكون من خلال منصات موثوقة مثل نفاذ)", "المستخدم يقوم بتقديم وثائق تعريفية بناء على طبيعة استخدامه للمنصة.", "المستخدم يتلقى إشعارات النظام (أو البريد في حالة استخدامه) تحتوي على تحديث حالة التسجيل (مقبول، مرفوض وسبب الرفض)"], "alternative_steps": ["إذا تم رفض حساب المستخدم، المستخدم يمكنه المراجعة والتعديل وإعادة تقديم الطلب."], "user_stories": ["كمستخدم عميل، أريد أن أقوم بالتسجيل في التطبيق بسرعة وسهولة حتى أتمكن من البدء في استخدام المنصة ، من أجل استخدام الخدمات المتوفرة.", "كمقدم خدمة أريد أن أقوم بالتسجيل في التطبيق، من أجل عرض خدماتي و الحصول على فرص عمل جديدة وبناء شراكات.", "كمستخدم، أنا بحاجة لتوثيق رقم الهاتف الخاص بي لضمان أمان حسابي.", "كمستخدم، أنا بحاجة لتقديم وثائق تحقيق الشخصية الخاصة بي للتأكد من اعتمادي من النظام.", "موظف إداري في المنصة، أرغب في تلقي إشعارات عند تسجيل مستخدم جديد أو مقدم خدمة جديد حتى أتمكن من فحص وثائقهم والتحقق منها للتأكد من التزام المنصة بمعايير الجودة والسلامة.", "موظف إداري في المنصة، يحتاج إلى مراجعة طلبات الالتحاق, مراجعتها إما القبول أو وضع سبب الرفض.", "كمستخدم، أريد تلقي تحديثات حول حالة طلب التسجيل الخاص بي، من أجل معرفة ما إذا كان مقبولًا أم مرفوضًا أم في انتظار المراجعة."], "performance_indicators": ["عدد المسجلين مع تصنيف الفئات (تاجر، ناقل، وسيط....../ تسجيل مقبول، تسجيل مرفوض، إعادة تسجيل....).", "الفترة الزمنية للتسجيل."], "case_uses": {}}, "4.2.2": {"title": "الإشعارات", "business_goals": ["للتأكيد على أن كافة المستخدمين، بغض النظر عن الدور، يتلقون الإشعارات ذات الصلة بنشاطهم في النظام ولتحسين سهولة الاستخدام وتجربة المستخدم."], "stakeholders": ["إداريي النظام", "المستخدمون (مقدمو الخدمات والعملاء)"], "main_steps": ["تلقي الإشعارات الداخلية في لوحة التحكم للإداريين.", "تلقي الإشعارات الخارجية عبر البريد الإلكتروني أو الإشعارات push notification.", "تلقي الإشعارات الداخلية في مركز الإشعارات بالتطبيق للمستخدمين.", "إعدادات الإشعارات التي تسمح للمستخدمين بتفعيل/تعطيل أنواع محددة من الإشعارات.", "إعدادات الإشعارات التي تسمح للمستخدمين بتحديد طريقة استلام الإشعارات."], "alternative_steps": ["تعطيل الإشعارات.", "تغيير طرق تلقي الإشعارات."], "user_stories": ["مدير النظام: يج<PERSON> أن يحصل على إشعار داخلي عند حدوث حدث معين يتطلب انتباهه.", "مقدم الخدمة: يج<PERSON> أن يتلقى إشعارًا عند استلام طلب جديد للخدمة، أو رسالة جديدة، تحديث في حالة خدمة.", "العميل: يج<PERSON> أن يحصل على إشعار عندما يقوم مقدم الخدمة بتغييرات مهمة في الطلب مثل قبول الطلب، أو تغيير الحالة، أو إرسال رسالة جديدة.", "العميل و مقدم الخدمة: يجب أن يحصلوا على إشعار دعائي عند تقديم عروض جديدة أو خدمات أو تحديثات في النظام."], "performance_indicators": ["عدد الاشعارات المرسلة مع التصنيف حسب نوع المستخدم."], "case_uses": {}}, "4.2.3": {"title": "تعديل الحساب وتغيير كلمة المرور", "business_goals": ["السماح للمستخدمين بتحديث معلوماتهم الشخصية بما في ذلك كلمة المرور.", "تعزيز الأمان من خلال تحديد متطلبات لكلمات المرور."], "stakeholders": ["جميع المستخدمين"], "main_steps": ["المستخدم ينقر على 'تعديل الحساب' أو 'تغيير كلمة المرور' في حسابه.", "المستخدم يمكنه تحديث المعلومات الشخصية و/أو كلمة المرور.", "لتغيير كلمة المرور، يتوجب على المستخدم إدخال كلمة المرور القديمة ثم إدخال الجديدة.", "كلمة المرور الجديدة يجب أن تكون أكثر من 8 رموز وأن تحتوي على حروف وأرقام.", "المستخدم ينقر على 'حفظ' لتأكيد التغييرات."], "alternative_steps": ["ليس هناك أي حاجة لتأكيد المستخدم عبر البريد الإلكتروني أو الرسالة النصية."], "user_stories": ["كمستخدم، أريد تحديث معلومات حسابي الشخصية (بما في ذلك كلمة المرور) بحيث يمكنني الحفاظ على الدقة والأمان.", "كمستخدم، أريد تغيير كلمة السر الخاصة بي بإدخال القديمة أولاً، للتأكد من أنني الشخص الوحيد الذي يمكنه تغييرها.", "كمستخدم، أريد أن تكون كلمة السر التي اختارها آمنة ومعقدة، وذلك لحماية حسابي من الاختراق."], "performance_indicators": ["عدد المستخدمين الذي قاموا بتعديل او تحديث حساباتهم."], "case_uses": {}}, "4.2.4": {"title": "تسجيل الدخول", "business_goals": ["تزويد المستخدمين بوسيلة آمنة وسهلة للدخول إلى حساباتهم.", "تقليل فرص الاختراق والوصول غير الشرعي من خلال استخدام OTP."], "stakeholders": ["جميع المستخدمين"], "main_steps": ["المستخدم يدخل رقم الهاتف وكلمة المرور ثم ينقر على زر 'تسجيل الدخول'.", "في حالة الرغبة في استخدام OTP، المستخدم يضغط على الزر 'إرسال OTP'. المنصة ترسل رسالة نصية مع الOTP إلى رقم الهاتف.", "المستخدم يدخل ال OTP المرسلة عبر الرسالة النصية ويضغط على 'تسجيل الدخول'.", "جعل التطبيق في حالة تسجيل (دخول للحساب) مستمرة للعمل في الخلفية (نشط – غير نشط)."], "alternative_steps": ["إذا كان المستخدم قد نسي كلمة المرور، يمكنه الضغط على 'نسيت كلمة المرور' واتباع الإرشادات لاستعادة الحساب.", "إذا كان الOTP غير صالح (مرت أكثر من 3 دقائق منذ إرسالها)، يمكن للمستخدم طلب OTP جديدة."], "user_stories": ["كمستخدم، أريد تسجيل الدخول إلى حسابي بسهولة وأمان باستخدام رقم الهاتف وكلمة السر.", "كمستخدم، أريد القدرة على استخدام OTP كطريقة بديلة لتسجيل الدخول، للحفاظ على أمان حسابي وتقديم خيار تسجيل دخول مرن.", "كمستخدم، أريد القدرة على طلب OTP جديدة إذا كانت القديمة غير صالحة."], "performance_indicators": ["عدد المستخدمين الذين قاموا بتسجيل الدخول (حالياً او خلال فترات سابقة).", "عدد المستخدمين النشطين حاليآ او خلال فترات سابقة.", "عدد المستخدمين الذين لا يمكنهم ممارسة الأعمال بسبب احد الوثائق المقدمة مثل انتهاء صلاحيتها أو عدم تقديمها."], "case_uses": {}}, "4.2.5": {"title": "إعادة تعيين كلمة السر", "business_goals": ["تزويد المستخدمين بالقدرة على استعادة الوصول إلى حساباتهم بسهولة في حالة نسيان كلمة المرور.", "تقليل فرص تعطل الخدمة أو التأخير بسبب نسيان كلمة السر وتحقيق أقصى قدر من الكفاءة والراحة للمستخدم."], "stakeholders": ["المستخدمون الذين نسوا كلمة المرور."], "main_steps": ["المستخدم ينقر على 'نسيت كلمة المرور'.", "المستخدم يدخل رقم الهاتف وينقر على 'إرسال'.", "يتم إرسال رسالة نصية إلى الهاتف المحمول للمستخدم تحتوي على OTP وكلمة سر مؤقتة.", "المستخدم يدخل OTP ويقوم بالدخول.", "بمجرد تسجيل الدخول، يتم توجيه المستخدم إلى صفحة تعديل الحساب لوضع كلمة سر جديدة."], "alternative_steps": ["إذا تم إدخال OTP أو كلمة السر المؤقتة بشكل خاطئ، يتم تقديم الرسائل الخطأ المناسبة.", "إذا شعر المستخدم بالحاجة لإعادة المحاولة، يمكن طلب OTP وكلمة السر المؤقتة مرة أخرى."], "user_stories": ["كمستخدم، أريد أن أتمكن من استعادة الوصول إلى حسابي إذا نسيت كلمة المرور.", "كمستخدم، أرغب في الحصول على OTP وكلمة سر مؤقتة بسرعة وسهولة عبر الرسائل النصية.", "كمستخدم، أرغب في تغيير كلمة السر إلى واحدة جديدة بمجرد تسجيل الدخول باستخدام كلمة المرور المؤقتة."], "performance_indicators": ["عدد المستخدمين الذين قاموا بإعادة او تغيير كلمات المرور."], "case_uses": {}}, "4.2.6": {"title": "تسجيل المركبات", "business_goals": ["<PERSON><PERSON><PERSON> أن مقدمي خدمة النقل مستعدون بشكل كافي ولديهم المركبات اللازمة للقيام بعمليات النقل.", "أن المركبات لديها التراخيص القانونية وتلبي معايير الجودة والسلامة المطلوبة."], "stakeholders": ["مقدم خدمة النقل (المستخدم)"], "main_steps": ["التحقق من أن المستخدم مقدم خدمة نقل.", "تقديم خيارات لإدخال معلومات السيارة / السيارات المستخدمة في النقل.", "التحقق من صحة المعلومات المقدمة والوثائق المرفقة."], "alternative_steps": ["في حالة كانت وثائق السيارة غير صحيحة، يتم تقديم رسالة خطأ مع توضيح الخطأ وطلب تصحيحه."], "user_stories": ["كمقدم خدمة نقل فردي، أريد إمكانية إضافة معلومات سيارتي ورفع الوثائق المطلوبة، لكي أثبت صلاحية سيارتي لأداء عمليات النقل.", "كمقدم خدمة نقل تابع إلى (مؤسسة - شركة)، أريد إمكانية إضافة معلومات السيارة التي أعمل عليها وإرفاقها إلى أسطول الشركة التي أعمل لديها.", "كمقدم خدمة نقل أعمل كسائق، في حالة وجود أي أخطاء في البيانات أو الوثائق المرفقة، أرغب في تلقي رسالة خطأ توضح المشكلة، حتى أتمكن من تصحيحها واستكمال التسجيل.", "كشركة نقل، أرغب في القدرة على إضافة عدد غير محدود من السيارات، لكي أتمكن من تسجيل كل السيارات التي تعتبر جزءًا من أسطول الشركة."], "performance_indicators": ["عدد المركبات المسجلة حسب تصنيف فئات محدد (نوع المركبة، حجم المركبة، موديل المركبة...).", "عدد المركبات المسجلة والتي لا يمكنها ممارسة الأعمال بسبب أحد الوثائق المسجلة (انتهاء الصلاحية، عدم تقديم المستند...)."], "case_uses": {}}, "4.2.7": {"title": "إضافة معلومات السائقين وربطهم بالسيارات", "business_goals": ["تحقيق القدرة على تقديم معلومات دقيقة للعملاء حول السائقين والسيارات المتوفرة لخدمات النقل."], "stakeholders": ["مقدمي الخدمات (شركات النقل)"], "main_steps": ["مقدم الخدمة يدخل إلى صفحة إدارة السائقين.", "يختار إضافة سائق جديد.", "يدخل معلومات السائق (اسم السائق، رقم الهاتف، العنوان، رقم الرخصة، صورة الرخصة).", "يتم ربط السائق مع سيارة محددة من قائمة السيارات المتاحة له."], "alternative_steps": ["مقدم الخدمة يستطيع تعديل معلومات السائق أو فصله عن السيارة في أي وقت."], "user_stories": ["مقدم الخدمة: يرغب في إضافة سائق جديد، لذا يقوم بدخول صفحة إدارة السائقين، يضغط على إضافة سائق جديد، يملأ معلومات السائق ويربطه بالسيارة المناسبة.", "مقدم الخدمة: يرغب في تعديل معلومات سائق، يقوم بالدخول إلى صفحة إدارة السائقين، يبحث عن السائق المطلوب، يقوم بتعديل المعلومات المطلوبة.", "مقدم الخدمة: يرغب في فصل سائق عن سيارة، يقوم بالدخول إلى صفحة إدارة السائقين، يبحث عن السائق المطلوب، يقوم بفصل السائق عن السيارة."], "performance_indicators": ["عد<PERSON> المركبات المضافة من شركات النقل.", "عد<PERSON> المركبات المضافة والغير مرتبطة بسائقين."], "case_uses": {}}, "4.2.8": {"title": "إضافة معلومات محطات الوقود", "business_goals": ["تزويد المستخدمين بأماكن محطات الوقود المحلية وتقديم معلومات مفصلة عنها لتعزيز التجربة الكلية للمستخدم وتوفير خدمات الدعم التقني."], "stakeholders": ["مقدمي الخدمات المتخصصة (أصحاب محطات الوقود)"], "main_steps": ["يدخل مالك المحطة إلى صفحة إدارة المحطات.", "يختار إضافة محطة جديدة.", "يدخل معلومات المحطة (اسم الفرع، العنوان).", "يحد<PERSON> الإحداثيات على الخريطة."], "alternative_steps": ["يستطيع مالك المحطة تعديل معلومات المحطة أو تحديث موقعها على الخريطة في أي وقت."], "user_stories": ["مالك المحطة: يرغب في إضافة فرع جديد، فيقوم بالدخول إلى صفحة إدارة المحطات، يضغط على 'إضافة محطة جديدة'، يقوم بتقديم معلومات الفرع وموقعه على الخريطة.", "مالك المحطة: يرغب في تعديل معلومات فرع، فيقوم بالدخول إلى صفحة إدارة المحطات، يبحث عن الفرع المطلوب، يقوم بتعديل المعلومات المطلوبة.", "مالك المحطة: في حالة تغير موقع الفرع، يقوم بدخول صفحة إدارة المحطات، يبحث عن الفرع، تعديل موقعه على الخريطة."], "performance_indicators": ["عدد المحطات المسجلة حسب المواقع (مدن أو مناطق أو مسارات طريق)."], "case_uses": {}}, "4.2.9": {"title": "طل<PERSON> خدمة النقل", "business_goals": ["تمكين العملاء من تقديم طلبات خدمة النقل مع تحديد تفاصيلها."], "stakeholders": ["طال<PERSON> خدمة النقل (التاجر، وسيط النقل، الفرد)"], "main_steps": ["تحديد نوع العميل: مرسل/مرسل إليه.", "اختيار فئة المركبة المطلوبة (نقل خفيف، نقل ثقيل).", "اختيار نوع المركبة المطلوبة حسب الفئة المحددة (قائمة منسدلة لكل فئة) - اختيار متعدد.", "وصف الحمل والوزن الإجمالي.", "تحديد قيمة الحمولة.", "تحديد موقع التحميل والتنزيل (موقع واحد أو مواقع متعددة).", "تحديد موعد التحميل (الآن/مجدول بحد أقصى ٢٠ ساعة).", "تحديد نطاق البحث (١٠كم، ٥٠كم، ١٠٠كم، ٢٠٠كم).", "اختيار آلية التسعير (طلب تسعيرة {مزايدة عامة، مزايدة خاصة}/ سعر ثابت)."], "alternative_steps": ["الخدمة غير متاحة في حال لم يكن هناك مقدمي الخدمة المتاحين وفقا للمعايير المحددة."], "user_stories": ["كطالب خدمة، أريد أن أحدد دوري في العملية (مرسل/مستلم)، لكي يتم تحديد اتجاه الحمولة ومسئوليتي فيها.", "كطالب خدمة، أريد اختيار فئة المركبة المطلوبة، لكي يتم البحث عن مقدمي الخدمة المتوافقين مع احتياجاتي.", "كطالب خدمة، أريد اختيار نوع المركبة المطلوبة بناءً على الفئة التي اخترتها، لكي يتم البحث عن مقدمي الخدمة المناسبين.", "كطالب خدمة، أريد تحديد الوزن الكلي للحمولة، لكي يتم تقدير التكلفة الكلية للخدمة.", "كطالب خدمة، أريد تحديد مواقع تحميل وتنزيل الحمولة، لكي يتم توجيه مقدم الخدمة بشكل صحيح.", "كطالب خدمة، أريد تحديد موعد التحميل (الآن / مجدول)، لضمان توفر مقدم الخدمة في الوقت المطلوب.", "كطالب خدمة، أريد أن أحدد نطاق البحث، للحصول على قائمة من مقدمي الخدمات المتاحين في المنطقة المطلوبة.", "كطالب خدمة، أريد اختيار آلية التسعير (طلب تسعير / سعر ثابت)، لكي أتمكن من التحكم في تكلفة الخدمة.", "كمقدم خدمة، أريد أن أرى تفاصيل طلبات النقل المطابقة لمعايير خاصة بي، لكي أتمكن من تقديم عروض أسعار مناسبة."], "performance_indicators": ["عدد الطلبات مع تصنيفها (نوع وفئة المركبة المستخدمة، مدينة الارسال والاستلام،....)."], "case_uses": {}}, "4.2.10": {"title": "تسعير الحمولة", "business_goals": ["تقنين وتنظيم عملية تسعير الحمولة وتوفير الشفافية."], "stakeholders": ["العميل", "مقدمي الخدمات", "إداريي المنصة"], "main_steps": ["العميل يختار بين نوع التسعير الثابت أو تلقي عروض الأسعار.", "في حالة التسعير الثابت، يتم إبلاغ العميل بالقيمة الأساسية لأجرة النقل، ومن ثم إضافة الرسوم الإضافية لتأكيد المبلغ الإجمالي.", "في حالة طلب عروض الأسعار، يتم عرض طلب النقل للمقدمين لتقديم عروضهم، سواء بشكل عام أو خاص."], "alternative_steps": ["النظام يسمح برؤية العروض في حالة المزايدة العامة من قبل جميع مقدمي الخدمات.", "العميل يمكنه ترتيب وفلترة العروض حسب السعر، التقييم، ومعايير أخرى.", "يجب تحديد آلية واضحة لعرض ومشاركة أسعار المزايدات؛ في العروض العامة، يمكن لجميع مقدمي الخدمة رؤية العروض، بينما في العروض الخاصة، تظهر التفاصيل فقط لطالب الخدمة."], "user_stories": ["العميل، يريد تحديد سعر ثابت للنقل، للتحكم الكامل في تكلفة الخدمة.", "العميل، يريد استقبال عروض الأسعار للحصول على أفضل صفقة ممكنة.", "مقدم الخدمة، يريد مشاهدة طلبات النقل لتقديم عرض تنافسي.", "الأدمن، يريد تحديد حدود للتسعير للحفاظ على استقرار وعدالة السوق."], "performance_indicators": ["عد<PERSON> الطلبات حسب تصنيف آلية التسعير (ثابت، عرض سعر)، مزايدة عامة أو خاصة وربطها بالفترات الزمنية.", "نسبة المستجيبين للطلبات إلى المستلمين."], "case_uses": {}}, "4.2.11": {"title": "إتمام الصفقة", "business_goals": ["<PERSON><PERSON><PERSON> أن يتم دفع أتعاب الناقل مقابل خدمات النقل.", "توفير آلية لحجز واحتجاز الأموال حتى يتم إتمام الخدمة بالكامل.", "تعزيز الثقة بين الأطراف عن طريق ضمان الدفع.", "تمكين العميل من الغاء طلب الحمولة بأقل خسائر للناقل."], "stakeholders": ["طالب الخدمة", "الناقل", "المنصة", "خدمة الدفع"], "main_steps": ["يتم التحقق من أن لدى طالب الخدمة مبلغ كافٍ في محفظته على المنصة.", "إرسال رسالة برمز تأكيد لإتمام عملية الحجز.", "يتم حجز مبلغ المعاملة وتجميده في حساب طالب الخدمة.", "في حالة عدم توافر الرصيد الكافي، يجب إرسال رسالة تحتوي على رمز تأكيد لإكمال عملية إضافة الأموال.", "يتم توجيه طالب الخدمة لإضافة الأموال إلى محفظته من خلال واحدة من وسائل الدفع.", "يبقى المبلغ محجوزًا حتى يتم إتمام عملية النقل."], "alternative_steps": ["في حال إلغاء طالب الخدمة لعملية النقل، يتم تحديد قيمة الاسترداد بناءً على مدى قرب الناقل من الموقع وما إذا كان قد بدأ في الرحلة وعدة عوامل أخرى تحددها الادارة."], "user_stories": ["طالب الخدمة: يريد تأكيد الصفقة، ليتيح للناقل بدأ عمله.", "الناقل: يري<PERSON> التأكد من اتمام العميل للصفقة حتى يبدأ عمله بدون تأخير.", "طالب الخدمة: يريد إمكانية إلغاء طلب النقل، حتى يظهر في امان اذا تغيرت الظروف."], "performance_indicators": ["عد<PERSON> الطلبات التي تم إتمام الصفقة لها و نسبتها لإجمالي الطلبات خلال فترات محددة.", "عد<PERSON> الطلبات التي لم يكن لها رصيد في المحفظة وتم إضافة الأموال لحظة الإتمام.", "عدد الطلبات التي لم يتم إتمامها بسبب عدم توفر مبلغ كافي في المحفظة.", "قيمة المبالغ المحجوزة."], "case_uses": {}}, "4.2.12": {"title": "إدخال بيانات الحمولة من العميل", "business_goals": ["تحقيق تجربة يسيرة ومرنة للعميل أثناء تقديم فائدة متعددة الجوانب بتحديد دقيق لتفاصيل الحمولة بما في ذلك الأصناف، الوزن، القيمة، وظروف النقل.", "تعزيز الشفافية في عملية النقل من خلال السماح للمستلم بتأكيد معرفته باستلام الحمولة.", "تعزيز حماية البضائع من خلال التدقيق الشامل والحصر الدقيق لها."], "stakeholders": ["العميل", "المرسل إليه (المستلم)"], "main_steps": ["العميل يدخل عدد الأصناف للحمولة.", "العميل يدخل البيانات التالية لكل صنف: الوصف، العدد، الوزن/لكل حبة أو وزن الصنف الإجمالي، القيمة لكل حبة أو القيمة الإجمالية للصنف.", "العميل يدخل اشتراطات النقل (ظروف التخزين والعناية المطلوبة من الناقل).", "إذا كان التحميل أو التنزيل لأكثر من موقع، العميل يوضح ذلك لكل صنف على حدة مع توضيح المُرسل إليه (المستلم) لكل صنف.", "يتم إرسال رسالة نصية للمستلم / للمستلمين تحتوي على رابط لتأكيد معرفته بالحمولة المتجهة إليه والتأكيد عليها من خلال التطبيق اذا كان مسجلا في التطبيق."], "alternative_steps": ["في حال عدم تسجيل المستلم في المنصة، يُمكن إجراء التأكيد عن طريق إرسال رمز مؤقت إلى هاتفه المحمول لضمان تعريفه. أما إذا كان المستلم مُسجلاً بالفعل في التطبيق، فيتم تنفيذ عملية التسليم باستخدام رمز QR الموفر من المنصة نفسها."], "user_stories": ["العميل، بعد إتمام الدفع، أريد أن أتمكن من توصيف الحمولة، حتي اضمن حقوقي المالية.", "العميل، أريد أن أتمكن من إضافة مستلمين الحمولة ومعلوماتهم، حتي يسهل علي الناقل توصيل الحمولة بدون مشاكل.", "المستلم، أود استلام رسالة تضم التفاصيل الشاملة للشحنة الموجهة إلى عنواني، تشمل الوزن، القيمة، وموقع التحميل، لتمكيني من تأكيد معرفتي بهذه المعلومات والاستعداد المناسب.", "العميل، في حالة النقل إلى مواقع متعددة، أريد أن أتمكن من منظمة الحمولة بشكل معكوس لتأكيد استلام كل أصناف الحمولة في الموقع المناسب."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المرسلة للمستلمين غير مسجلين في التطبيق."], "case_uses": {}}, "4.2.13": {"title": "قبول الناقل للحمولة", "business_goals": ["إتاحة الفرصة للناقلين لقبول أو رفض النقلات المقترحة، وتأكيد الربط بين العميل والناقل في حالة القبول."], "stakeholders": ["العميل", "الناقل"], "main_steps": ["العميل يؤكد معلومات الحمولة.", "النظام يرسل QR للعميل.", "الناقل يمسح QR لكي يوافق على نقل الحمولة.", "النظام يقوم بربط العميل والناقل سوياً."], "alternative_steps": [], "user_stories": ["الناقل، بمجرد تلقي إشعار من النظام، أرغب في القدرة على الموافقة على الحمولة التي تم اسنادها لي.", "العميل، بعد تأكيد معلومات الحمولة، أرغب في العلم بأن المهمة قد تم توجيهها إلى الناقل وأن الناقل قد وافق عليها."], "performance_indicators": [], "case_uses": {}}, "4.2.14": {"title": "جرد الناقل للحمولة", "business_goals": ["تأكيد استلام الناقل للحمولة المطلوب نقلها وإثبات هذا من خلال التقاط صور والايصالات الالكترونية."], "stakeholders": ["الناقل", "المستلم", "إداري النظام"], "main_steps": ["يقوم الناقل بمعاينة بيان الحمولة واختيار الأصناف التي يستلمها في شاحنته، إما كل صنف على حدة أو جميعها معًا.", "عند انتهاء استلام كافة الأصناف في بيان الحمولة، يقر الناقل باستلام الأصناف حسب وصفاتها وأعدادها في بيان الحمولة وأنها سليمة.", "يلتقط الناقل ثلاث صور للحمل (ثلث الحمولة، ثلثي الحمولة، كامل الحمولة) من خلال التطبيق.", "يرفق الناقل نسخة إلكترونية من المستندات الخاصة بالحمولة ويؤكد تسليم الأصول للمستلم."], "alternative_steps": ["في حالة وجود أية ملاحظات على الأصناف، يمكن للناقل اختيار خيار استلام الأصناف (تم الاستلام بدون ملاحظات/تم الاستلام مع ملاحظات)، حيث يجب كتابة الملاحظات المرصودة."], "user_stories": ["الناقل: أرغب في مراجعة بيان الحمولة واختيار الأصناف التي يمكنني استلامها، لضمان أنني قمت بتحميل جميع الأصناف المطلوبة.", "الناقل: أرغب في التقاط صور للشحنة ورفعها عبر التطبيق، للتحقق من استلامي للأصناف بالتطابق مع المواصفات المذكورة في بيان الحمولة.", "الناقل: أريد رفع نسخة إلكترونية من المستندات الخاصة بالحمولة، للتأكيد على استلامي للأصناف.", "الإداري: أو<PERSON> أن أمتلك القدرة على تفعيل وتعطيل عملية الجرد بأكملها أو جزء منها حسب رغبتي، بهدف مراقبة وتنظيم العملية بطريقة أكثر فاعلية."], "performance_indicators": ["المدة الزمنية المحددة لتحميل وقبول استلام الشحنة."], "case_uses": {}}, "4.2.15": {"title": "متابعة الرحلة", "business_goals": ["اتاحة الامكانية للأطراف المعنية بمتابعة الحمولة في الوقت الحقيقي."], "stakeholders": ["العميل (مرسل، مستلم)", "شركة النقل"], "main_steps": ["بدء الرحلة عند تأكيد استلام الحمولة.", "يحسب النظام الوقت المتوقع للوصول إلى الوجهة/الوجهات المقصودة بناءً على العوامل التالية: السرعة المتوسطة (65كم/ساعة)، العدد المتوقع لساعات التوقف خلال الرحلة، عدد ساعات الراحة المتوقعة للشاحن خلال الرحلة.", "يتيح النظام تتبع الموقع الحالي للشاحن على الخريطة.", "مراقبة البيانات الواردة من أجهزة الاستشعار إذا كانت متوفرة.", "مراقبة حالة العقبات المتوقعة في مسار الرحلة."], "alternative_steps": [], "user_stories": ["كمشرف، أريد أن أعرف الموقع الحالي للحمولة، حتى أتمكن من توفير تحديثات سريعة للعميل.", "كمشرف، أريد أن أتابع بيانات الاستشعار، بما في ذلك الحرارة والصور، حتى أتأكد من سلامة الحمولة.", "كمشرف، أريد أن أعرف حالة الطرق المقبلة، حتى أتمكن من إبلاغ الشاحن بالتحديثات المطلوبة.", "كعميل، أريد أن أتابع الحمولة في الوقت الحقيقي، حتى أتمكن من التخطيط بشكل أفضل لوصول الحمولة."], "performance_indicators": ["الزمن المستغرق للرحلة.", "المسافة المقطوعة.", "عدد فترات التوقف لفترات طويلة.", "قيمة أجرة النقل بالنسبة للمسافة والزمن للرحلة (ريال/كم، ريال/ساعة أو يوم)."], "case_uses": {}}, "4.2.16": {"title": "تسليم النقل", "business_goals": ["إتمام عملية نقل بسهوله مع ضمان حقوق الطرفين."], "stakeholders": ["المستلم", "المرسل", "مقدم الخدمة (الناقل)"], "main_steps": ["يتم تسليم الحمولة للمستلم.", "إذا كان المستلم مسجل في التطبيق، يتم التسليم خلال التطبيق بواسطة QR.", "إذا لم يكن المستلم مسجلاً في التطبيق، سيتم إرسال رسالة نصية إلى هاتفه المحمول تتضمن رابطًا لتأكيد استلام الشحنة وبدء عملية الاستلام.", "تتم عملية استلام الشحنة باستخدام آلية جرد تشابه تلك المستخدمة في عملية التسليم بين المرسل والناقل. بمجرد استلام كافة الأصناف وتنزيل الشحنة بالكامل، يُطلب من المستلم التأكيد على استلام الشحنة بالكامل دون أية ملاحظات، أو تسجيل أي ملاحظات متعلقة بالتسليم. هذا الإقرار مشروط بدفع قيمة النقل أو عدمه. في حالة عدم الدفع، يتوجب على المستلم رفع الملاحظات وتوثيقها بالصور مباشرةً عبر التطبيق.", "يُصدر إشعار إلى طالب الخدمة الأساسي، وكذلك إلى الأطراف ذات العلاقة مثل المرسل، يُفيد بأن الشحنة قد تم تسليمها بنجاح.", "في حالة عدم وجود أي ملاحظات تشترط عدم دفع تكلفة النقل، يتم تحويل التكاليف من حساب طالب الخدمة إلى حساب الناقل بعد مرور ساعة من وقت إتمام الإرسال.", "إضافة ارسال إشعار للنظام (المشر<PERSON>) في حال وجود ملاحظات تعيق عملية تحويل التكاليف."], "alternative_steps": [], "user_stories": ["كمستلم، عند استلام الشحنة، أود التأكد من استلام جميع الأصناف وتفريغ الحمولة بالكامل. سأقر بأن التسليم تم بدون أي ملاحظات تعوق الإجراءات، لضمان إتمام المعاملة بسلاسة وحفظ حقوقي.", "كمستلم غير مسجل في التطبيق، أريد أن أستقبل رسالة نصية تحتوي على رابط لتأكيد استلام الحمولة ووضع ملاحظات على التسليم لضمان سلامة القطع والأصناف.", "كطالب خدمة، أريد استلام إشعار بأن الحمولة قد تم تسليمها لكي أكون مطمئن على سير العملية بالشكل المطلوب.", "كمقدم خدمة (ناقل)، أريد تحويل الأجرة من حساب العميل إلى محفظتي أو حسابي البنكي بعد ساعة من تسليم الحمولة، لكي أضمن حصولي على مستحقاتي."], "performance_indicators": ["إحصاء عدد الطلبات التي تم إتمامها بنجاح، بما في ذلك تسليم الشحنة وتصنيفها إلى: مقبولة بدون ملاحظات، مقبولة مع ملاحظات مشروطة بالدفع، أو مقبولة مع ملاحظات بدون شروط الدفع.", "الفترة الزمنية للتنزيل."], "case_uses": {}}, "4.2.17": {"title": "تحويل أرباح عملية النقل", "business_goals": ["ضمان دقة وشفافية في توزيع الأرباح بين الجهات المعنية والتطبيق."], "stakeholders": ["الناقل", "التطبيق", "هيئة الزكاة والضريبة والجمارك في المملكة العربية السعودية"], "main_steps": ["يتم تحويل أجرة النقل مباشرة إلى محفظة الناقل الإلكترونية.", "يتم تحويل رسوم الخدمة إلى حساب التطبيق تحت تصنيف 'رسوم خدمات النقل'، مع تفريق بين نقل ثقيل وخفيف، وإضافة تفاصيل إضافية تتعلق بنوع المركبة المستخدمة.", "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق."], "alternative_steps": [], "user_stories": ["الناقل: بعد تنفيذ الخدمة، يتم تحويل أجرة النقل التي دفعها العميل إلى محفظة الناقل الإلكترونية."], "performance_indicators": ["عدد الطلبات التي تم اكتمال تحويل تكاليفها وقيمتها خلال فترات محددة مع تصنيف (أجرة النقل، رسوم الخدمة، نسبة الضريبة).", "عدد الطلبات المعلقة وقيمتها خلال فترات محددة مع تصنيف (أجرة النقل، رسوم الخدمة، نسبة الضريبة)."], "case_uses": {}}, "4.2.18": {"title": "تقييم خدمة النقل", "business_goals": ["نظام التقييم مطلوب لضمان الشفافية وبناء الثقة بين العملاء ومقدمي الخدمات. هذا سيخدم كوسيلة للتحسين المستمر للخدمات التي تقدم من قبل مقدمي الخدمات."], "stakeholders": ["العملاء", "إداري المنصة"], "main_steps": ["عند اكتمال عملية النقل، يتقدم النظام برسالة إلى العميل لتقييم تجربة النقل.", "يتم أرشفة التقييمات وجعلها مرئية للجمهور في صفحة مقدم الخدمة."], "alternative_steps": ["في حالة إغفال العميل للتقييم، يجب تقديم تذكيرات بالتقييم."], "user_stories": ["كعميل، بعد استلام نقلتي، أريد تقييم جودة الخدمة التي تلقيتها، حتى يتمكن الآخرون من الاطلاع على تجربتي.", "كإداري للمنصة، أريد رؤية جميع التقييمات للمعاملات التي تمت على المنصة، حتى أتمكن من مراقبة الجودة والتحسين المستمر للمنصة.", "كعميل آخر، أرغب في رؤية تقييمات العملاء والخدمات الأخرى لمقدم الخدمة، حتى أتمكن من اتخاذ قرار مستنير بشأن التعامل معهم."], "performance_indicators": ["تحديد نسبة التقييم الإجمالية وربط تصنيفها بفئة المركبة المستخدمة.", "عد<PERSON> الطلبات التي تم تقييمها من إجمالي الطلبات."], "case_uses": {}}, "4.2.19": {"title": "طلب خدمة دعم فني للناقل", "business_goals": ["توفير خدمات الرحلة بطريقة سهلة وفعالة، تمكن العملاء من طلب الخدمات المطلوبة وتحديد الموقع بطريقة سريعة ودقيقة."], "stakeholders": ["العميل: الذي سيطلب الخدمة ويحدد حالتها.", "مقدم الخدمة: سيكون له القدرة على تقديم الخدمات بناء على حالة ووصف الطلب."], "main_steps": ["الناقل يحدد حالة الخدمة (طارئة أو عادية).", "الناقل يحدد نوع الخدمة (مثل الصيانة).", "الناقل يدخل وصف الخدمة (عبر النص أو التسجيل الصوتي أو الصور أو الكل).", "يتم تحديد الموقع الجغرافي عبر النظام GPS."], "alternative_steps": ["في حالة فقدان الاتصال بالإنترنت، يمكن للمالك توفير معلومات الموقع يدوياً."], "user_stories": ["كعميل، أسعى لتوضيح حالة الخدمة (طارئة أو عادية) المطلوبة، وذلك لمنح مقدمي الخدمة فرصة كاملة لفهم السياق الخاص بطلبي، مما يمكنهم من تقديم الخدمة بأعلى مستوى من الكفاءة والجودة.", "كعميل، أريد تحديد نوع الخدمة، لأضمن أن المزود المناسب للخدمة هو من سيتعامل مع طلبي.", "كعميل، أسعى إلى تقديم وصف دقيق للخدمة المطلوبة، بهدف تمكين مزود الخدمة من فهم احتياجاتي بشكل واضح ومحدد.", "كعميل، أريد أن أكون قادرًا على تحديد موقعي تلقائيًا، لضمان أن مزود الخدمة يعرف بالضبط أين أنا.", "كمقدم خدمة، أريد أن أرى تفاصيل الطلب، بما في ذلك حالة الخدمة والنوع والوصف والموقع، حتى أتمكن من تقديم الخدمة بشكل أكثر فعالية وسرعة."], "performance_indicators": ["عدد الطلبات خلال فترات محددة مع تحديد التصنيفات (صيانة عادية، طارئة،...).", "عدد الورش الفنية التي استلمت الطلب ونسبة الاستجابة."], "case_uses": {}}, "4.2.20": {"title": "خدمة فنية لرحلة النقل", "business_goals": ["توفير خدمة تتيح للعملاء الحصول على خدمات فنية للمركبات بشكل مريح وفعال، وفي الوقت نفسه تتيح لورش العمل الفنية استقبال الطلبات التي تتفق مع مجال خبراتهم وإمكانياتهم."], "stakeholders": ["العميل (الذي يرسل طلب الخدمة)", "ورش العمل الفنية (التي تستقبل الطلبات وتجيب عليها)"], "main_steps": ["يتم تصنيف كل طلب خدمة فنية بناءً على نوع الخدمة المحددة وبيانات الخدمة، مثل نوع المركبة والمسافة التقريبية من موقع الخدمة.", "يتم إرسال الطلب إلى ورش العمل الفنية المسجلة التي تفي بالمعايير.", "يقوم المتلقي بمراجعة الطلب ويحدد رده (قبول تقديم الخدمة مع الرد وتحديد التكلفة، طلب مزيد من المعلومات، أو رفض الطلب مع ذكر الأسباب)."], "alternative_steps": [], "user_stories": ["العميل، كمستخدم: أريد تقديم طلب لخدمة فنية، حتى أستطيع الحصول على الخدمات المناسبة لحالة مركبتي.", "الورشة الفنية، كمستخدم: أريد استعراض الطلبات المتاحة، حتى أتمكن من الاختيار والاستجابة للطلبات التي هي في نطاق خبرتي وإمكانياتي.", "الورشة الفنية، كمستخدم: أريد أن أكون قادرًا على رفض الطلبات التي لا أستطيع التعامل معها، حتى لا أضيع وقتي وجهدي بلا داع."], "performance_indicators": ["عدد الطلبات خلال فترات محددة مع تحديد التصنيفات (صيانة عادية، طارئة ...)."], "case_uses": {}}, "4.2.21": {"title": "الرد على طلب خدمة النقل", "business_goals": ["توفير واجهة سهلة الاستخدام تُمكن الورشة الفنية من الرد على الطلبات بما يحقق تقديرها الخاص للتكلفة والوقت، مما يزيد فرص تلقي العميل الخدمة التي يحتاجها بشكل ملائم ومريح."], "stakeholders": ["الورشة الفنية (التي تقدم الرد على الطلب وتحدد الرسوم)"], "main_steps": ["الورشة الفنية تدخل الزمن المتوقع للرد (مثل: خلال كم ساعة خدمة).", "الورشة الفنية تدخل الأجرة المتوقعة للإصلاح المطلوب.", "يتم حساب الرسوم المقررة على الخدمة.", "يتم حساب الضريبة على الخدمة المقررة.", "يتم عرض صافي المبلغ المستحق للورشة بعد خصم الرسوم من أجرة الإصلاح.", "الموافقة وإرسال العرض لطالب الخدمة."], "alternative_steps": ["في حالة عدم الموافقة على الطلب، يمكن للورشة الفنية رفض الطلب مع ذكر السبب."], "user_stories": ["الورشة الفنية، كمستخدم: أريد إدخال الزمن المتوقع للرد وتحديد الأجرة للإصلاح، حتى أستطيع تقديم عرض ملائم ومنافس.", "الورشة الفنية، كمستخدم: أريد أن أرى صافي المبلغ المستحق بعد خصم الرسوم والضرائب من أجرة الإصلاح، حتى يكون لدي فكرة واضحة عن ما سأحصل عليه.", "الورشة الفنية، كمستخدم: أريد أن أتمكن من الموافقة على العرض وإرساله إلى العميل، لكي أحصل على الفرصة لتقديم الخدمة."], "performance_indicators": ["عدد الورش الفنية التي استلمت الطلب ونسبة الاستجابة."], "case_uses": {}}, "4.2.22": {"title": "الموافقة على خدمة فنية للرحلة", "business_goals": ["توفير واجهة سهلة الاستخدام تُمكن العميل من الاختيار بين العروض المتاحة وقبول الأنسب بناءً على احتياجاته وتوقعاته."], "stakeholders": ["العميل (طالب الخدمة)"], "main_steps": ["العميل يستعرض كافة العروض المستلمة.", "العميل يقارن بين العروض بناءً على أجرة الإصلاح، المسافة بين الورشة والزمن المتوقع للإصلاح.", "العميل يقرر قبول أحد العروض.", "يتم حساب التكلفة بإجمالي: رسوم أجرة الإصلاح + رسوم الخدمة المقررة + رسوم الضريبة على الخدمة."], "alternative_steps": ["في حالة عدم الموافقة على أي من العروض، يمكن للعميل إعادة إصدار طلب جديد أو التواصل مع الورش الفنية مباشرة."], "user_stories": ["العميل، كمستخدم: أريد مطالعة كافة العروض المستلمة والاختيار من بينها بناءً على تقديري للسعر والمدة والبعد عن الورشة، حتى أحصل على الخدمة بالوقت والمال المناسب لي.", "العميل، كمستخدم: أريد أن أعرف التكلفة الإجمالية للخدمة بما في ذلك الرسوم والضرائب، حتى يكون لدي فكرة واضحة عما سأدفعه.", "العميل، كمستخدم: أريد أن أتمكن من الموافقة على العرض، حتى نتمكن من بدء الإصلاح بأسرع وقت ممكن."], "performance_indicators": [], "case_uses": {}}, "4.2.23": {"title": "إتمام التعاقد", "business_goals": ["تأكيد حجز الخدمة وتجميد الرسوم المطلوبة للخدمة في حساب طالب الخدمة لضمان تنفيذ الخدمة وسداد الرسوم."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["عبور العميل لمرحلة القبول النهائي للصفقة عبر التطبيق.", "التحقق من متطلبات الصفقة من حيث الرسوم والخدمات المطلوبة.", "التحقق من متطلبات الدفع، بما في ذلك توافر المبلغ في المحفظة.", "في حالة عدم توافر المبلغ، يتم اتمام خطوات الدفع الالكتروني لتعبئة المحفظة والاستمرار في العملية.", "إرسال كود تأكيد للحجز على المبلغ وتجميده ضمن حساب طالب الخدمة."], "alternative_steps": ["في حالة عدم توافر المبلغ لدى طالب الخدمة يتم إيقاف العملية حتى يتم تزويد الحساب بالمبلغ المطلوب.", "قبل انتهاء الزمن المحدد للاستجابة، يمكن لطالب الخدمة أو للورشة الفنية طلب الإلغاء وبعدها يتم الرجوع للخطوات السابقة."], "user_stories": ["كعميل: أو<PERSON> إعطاء تأكيد نهائي للصفقة، لضمان حجز الخدمة وتأمين الرسوم المتفق عليها بأمان.", "كعميل: إذا كان المبلغ الكافي غير متوفر في محفظتي، أريد إضافة المبلغ المطلوب عبر وسائل الدفع الإلكترونية المتاحة لي.", "كعميل: أ<PERSON><PERSON><PERSON> ض<PERSON>ان إمكانية الإلغاء في حالة عدم استجابة الورشة الفنية في الوقت المحدد أو في حالة تغيير رأيي في الخدمة المطلوبة."], "performance_indicators": ["عدد الطلبات التي تم إتمام التعاقد عليها ونسبتها للاجمالي الطلبات التي تم الاستجابة لها."], "case_uses": {}}, "4.2.24": {"title": "مباشرة الحالة", "business_goals": ["تأكيد بدء الخدمة بين العميل ومزود الخدمة وضمان انتقال سلس وآمن للعملية إلى المرحلة التالية."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["إعطاء (العميل) رمز تأكيد فريد يتولد بشكل تلقائي من قبل النظام.", "يقوم مزود الخدمة بطلب رمز التحقق من طالب الخدمة للتحقق من بداية الخدمة.", "التحقق من نجاح التفعيل ومباشرة الخدمة من خلال التطبيق برمز QR."], "alternative_steps": ["في حالة عدم التمكن من استخدام العميل للرمز، يمكنه الاتصال بخدمة العملاء للمساعدة.", "في حالة فقدان الرمز، يتم إنشاء رمز جديد."], "user_stories": ["كعميل: بمجرد تأكيد الصفقة، أريد تلقي رمز تأكيد فريد يمكنني من التحقق من بداية الخدمة.", "كعميل: إذا واجهت مشكلة في استخدام الرمز، أريد الاتصال بخدمة العملاء للمساعدة.", "كمقدم الخدمة: عند بداية الخدمة، أريد استخدام رمز التحقق المقدم من العميل لتأكيد بدء الخدمة."], "performance_indicators": ["نسبة عدد الطلبات التي تم مباشرتها من إجمالي الطلبات التي تم التعاقد عليها."], "case_uses": {}}, "4.2.25": {"title": "إنهاء الخدمة الفنية", "business_goals": ["تأكيد انتهاء تقديم الخدمة من قبل الورشة الفنية وضمان تحصيل المبلغ المستحق بشكل صحيح."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)"], "main_steps": ["إعطاء مزود الخدمة (الورشة الفنية) القدرة على إدخال ملخص الإصلاح (اختياري).", "السماح لمزود الخدمة بعرض المبلغ المستحق أو التعديل عليه (بالتقليل فقط في حالة عدم الاتفاق).", "تأكيد العملية من طالب الخدمة من خلال استخدام كود أو رسالة استجابة."], "alternative_steps": ["في حالة عدم الاتفاق على المبلغ المطلوب، يمكن للعميل الاتصال بخدمة العملاء للمساعدة."], "user_stories": ["كمقدم الخدمة: بعد الانتهاء من تقديم الخدمة، أريد إدخال ملخص الإصلاح والمبلغ المطلوب بشكل سهل ومباشر.", "كعميل: بعد استلام ملخص الإصلاح والمبلغ المطلوب، أريد قابلية التأكيد على المبلغ المطلوب أو طلب مراجعته."], "performance_indicators": ["نسبة عدد الطلبات المنتهية من إجمالي الطلبات التي تمت مباشرتها."], "case_uses": {}}, "4.2.26": {"title": "إغلاق الخدمة الفنية", "business_goals": ["ضمان توزيع الأموال المستحقة بشكل صحيح بين الأطراف المعنية (الورشة الفنية، المنصة، الضرائب) عند إغلاق الخدمة الفنية."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)", "المنصة (التطبيق)"], "main_steps": ["خصم المبلغ الإجمالي من حساب المحفظة لطالب الخدمة.", "تحويل أجرة الإصلاح إلى حساب المحفظة الإلكترونية للورشة الفنية.", "تحويل رسوم الخدمة إلى حساب التطبيق تحت مسمى رسوم خدمات مساندة.", "تحويل ضريبة القيمة المضافة إلى حساب الضريبة في التطبيق."], "alternative_steps": [], "user_stories": ["كعميل: بعد الموافقة على المبلغ المطلوب، أريد أن يتم خصم المبلغ من محفظتي وتحويله بشكل صحيح دون أي تدخل مني.", "كمزود الخدمة: بعد تقديم الخدمة وتأكيد العميل، أريد تلقي أجرة الإصلاح بشكل مباشر وسهل في محفظتي على التطبيق.", "كإداري للمنصة: أريد تلقي رسوم الخدمة المستحقة وتوزيع الأموال المستحقة بشكل آلي وصحيح على الأطراف المعنية."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المغلقة ونسبتها للطلبات التي تم الانتهاء منها.", "إجمالي تكاليف الخدمة وتفصيلها (رسوم الخدمة والضريبة)."], "case_uses": {}}, "4.2.27": {"title": "تقييم خدمة فنية", "business_goals": ["ض<PERSON>ان تقييم شفاف للخدمات المقدمة والحفاظ على سجل دقيق وموثق للبيانات، بهدف تعزيز الجودة وتحسين الخدمات مستقبلاً."], "stakeholders": ["العميل (طالب الخدمة)", "مزود الخدمة (الورشة الفنية)", "المنصة (التطبيق)"], "main_steps": ["تقييم الخدمة من قبل كل الأطراف.", "أرشفة البيانات الأساسية (وصف الحالة، موقع الإصلاح، تاريخ الطلب) للخدمة في سجلات الطلبات لكل الأطراف.", "أرشفة كافة بيانات الخدمة في سجلات المنصة."], "alternative_steps": [], "user_stories": ["كعميل: بعد استكمال الخدمة، أريد تقييم الخدمة حتى يعرف الآخرون عن جودة العمل والكفاءة.", "كمزود الخدمة: أريد تلقي تغذية راجعة من العملاء حتى أستطيع تحسين أدائي وخدمتي في المستقبل.", "كإداري للمنصة: أريد التأكد من تقييم الجودة الشامل لاستكمال بناء منصة موثوقة وفعالة."], "performance_indicators": ["إحصاء عدد الطلبات التي خضعت للتقييم وحساب نسبتها من إجمالي الطلبات المقدمة."], "case_uses": {}}, "4.2.28": {"title": "إدخال بيانات قطع الغيار", "business_goals": ["تسهيل الوصول إلى خدمات قطع الغيار من محلات مختارة وموثوقة، وتوفير تجربة سلسة ومريحة للعملاء في اختيار وطلب قطع الغيار."], "stakeholders": ["العميل (طالب الخدمة)", "محلات القطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["العميل يختار نوع القطع (جديد/مستعمل).", "النظام يستدعي بيانات المركبة للعميل.", "العميل يدخل بيانات القطع الوصفية والكمية (يمكن استخدام النص، التسجيل الصوتي، الصورة، أو الكل).", "تحديد الموقع بشكل آلي من خلال النظام GPS."], "alternative_steps": ["العميل لا يمكنه تسجيل أرقام الهاتف للتواصل بين الأطراف."], "user_stories": ["كعميل: أ<PERSON><PERSON><PERSON> اختيار نوع المحل، البحث عن قطع الغيار وطلبها بناءً على بيانات مركبتي.", "محلات قطع الغيار: أريد استلام الطلبات من العملاء ومعرفة تفاصيلها لإمكانية تقديم الخدمة المطلوبة.", "كإداري للمنصة كمستخدم: أريد توفير منصة موثوقة وسهلة الاستخدام للعملاء لطلب قطع الغيار، وأريد تحقيق تواصل فعال بين العميل ومحلات قطع الغيار."], "performance_indicators": [], "case_uses": {}}, "4.2.29": {"title": "نشر خدمة قطع الغيار", "business_goals": ["إذا لم يتم العثور على عدد كافٍ من المحلات ضمن النطاق المحدد، يمكن للنظام توسيع نطاق البحث تدريجيًا.", "المحلات التي لا تستطيع تقديم عرض قد تقترح توفير القطعة المطلوبة في وقت لاحق أو توفير بديل."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["العميل ينشر طلبًا محددًا بالقطع التي يحتاجها، مثل فانوس لعربية نقل شيفروليه.", "النظام يبحث تلقائيًا عن المحلات الأقرب إلى موقع العميل، مستهدفًا تحديد أقرب 5 محلات ضمن نطاق 10 كيلومترات. إذا لم يجد 5 محلات ضمن هذا النطاق، يتوسع قطر البحث تلقائيًا حتى يجد على الأقل 5 محلات.", "محلات قطع الغيار تستعرض الطلب وتقدم عروضها مع تفاصيل القطع والأسعار."], "alternative_steps": ["إذا لم يجد العميل العرض المناسب، يمكنه طلب مزيد من التفاصيل أو عروض جديدة.", "إعطاء خيار إعادة البحث وتوسيع نطاق البحث (من خلال النظام يبحث عن المحلات)", "المحلات قد تقدم عروضًا ترويجية أو بدائل إضافية بناءً على المخزون المتاح."], "user_stories": ["كعميل: أ<PERSON><PERSON><PERSON> أن يحدد النظام تلقائيًا المحلات الأقرب إلي ويعرض عليّ الخيارات لضمان سرعة الحصول على القطعة.", "كمحل قطع غيار: أريد أن يعرض عليّ النظام الطلبات القريبة من موقعي لتقديم عروض تنافسية وسريعة.", "كمدير للمنصة: أريد توفير نظام يكفل توزيع الطلبات بشكل عادل وفعال بين المحلات القريبة والبعيدة، معززًا الكفاءة والرضا لدى العملاء ومقدمي الخدمة."], "performance_indicators": ["عد<PERSON> الطلبات المنشورة خلال فترات محددة.", "عدد المستلمين لطلبات المنشورة (لكل طلب لمعرفة قاعدة المستجيبين)."], "case_uses": {}}, "4.2.30": {"title": "قطع غيار بيانات إضافية", "business_goals": ["توفير تعامل مرن ومريح بين العملاء ومحلات قطع الغيار، مع ضمان الخصوصية والأمان للمعلومات الشخصية."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["مقدم الخدمة يطلب بيانات إضافية من خلال فتح محادثة مؤقتة والتي تسمح بالكتابة والتسجيل الصوتي وإرفاق الصور.", "على العميل الرد على طلب بيانات الخدمة الإضافية وتوفيرها.", "قواعد النظام تمنع تسجيل أرقام الهاتف أو بيانات الاتصال الشخصية بين الأطراف في المحادثات وتمنع ظهورها في استعراض الملفات."], "alternative_steps": ["في حالة عدم استجابة العميل، يمكن لمقدم الخدمة الرجوع إلى الطلب الأصلي ومتابعة القرار الأمثل بناءً على المعلومات المتوفرة فقط."], "user_stories": ["العميل، كمستخدم: أريد استلام طلب لتقديم معلومات إضافية، وأيضًا أن أكون قادرًا على الرد على طلبات الخدمة بسهولة.", "محلات قطع الغيار، كمستخدم: أريد أن أتمكن من طلب بيانات إضافية من العميل في حالة الحاجة، وأن يكون الاتصال تابع للمنصة ولا يتعلق ببيانات الاتصال الشخصية.", "كإداري للمنصة: أريد تزويد المستخدمين بأدوات لتوفير تحسين التواصل والتعاون، مع الحفاظ على الأمان والخصوصية لمعلومات المستخدم."], "performance_indicators": ["عد<PERSON> الطلبات التي تم طلب بيانات إضافية لها بالنسبة لإجمالي الطلبات."], "case_uses": {}}, "4.2.31": {"title": "إتمام طلب قطع الغيار", "business_goals": ["تأكيد حجز القطعة المطلوبة وضمان تسديد المبلغ المطلوب بآلية مريحة وآمنة للعميل ومقدم الخدمة."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["العميل يختار العرض المناسب له ويقوم بتأكيد الطلب.", "النظام يتحقق من توافر المبلغ المطلوب في محفظة العميل.", "إذا لم يكن المبلغ كافيًا، يتم توجيه العميل لإضافة الأموال إلى محفظته.", "يتم حجز المبلغ المطلوب وتجميده في حساب العميل.", "يتم إشعار المحل بقبول الطلب وبدء عملية تجهيز القطعة."], "alternative_steps": ["إذا لم يتمكن العميل من إتمام الدفع، يتم إلغاء الطلب وتحرير أي مبالغ مجمدة."], "user_stories": ["كعميل: أري<PERSON> تأكيد الطلب بسرعة وأمان من خلال المنصة، والتأكد من حجز المبلغ المطلوب تلقائيًا.", "كمحل قطع غيار: أ<PERSON>ي<PERSON> أن أتلقى إشعارًا بقبول الطلب وبدء عملية التجهيز بشكل فوري بعد التأكيد."], "performance_indicators": ["عدد الطلبات التي تم إتمامها بنجاح مقارنة بإجمالي الطلبات المقدمة.", "عدد الطلبات التي لم يتم إتمامها بسبب مشاكل في الدفع."], "case_uses": {}}, "4.2.32": {"title": "تسليم قطع الغيار", "business_goals": ["<PERSON>مان تسليم قطع الغيار للعميل بطريقة مريحة وآمنة مع متابعة العملية لضمان رضا العميل."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["تحديد موعد ومكان التسليم المناسب للعميل.", "تحضير القطعة المطلوبة وتجهيزها للشحن.", "تأكيد عملية التسليم بواسطة رمز QR أو توقيع إلكتروني من العميل عند الاستلام."], "alternative_steps": ["إذا لم يتمكن العميل من استلام القطعة في الموعد المحدد، يمكنه تحديد موعد جديد للتسليم."], "user_stories": ["كعميل: أريد أن أكون قادرًا على تحديد موعد ومكان مناسبين لاستلام قطع الغيار، حتى أتمكن من التخطيط لذلك بشكل مناسب.", "كمحل قطع غيار: أ<PERSON><PERSON><PERSON> التأكد من أن القطعة المطلوبة جاهزة للتسليم في الوقت المحدد وأن عملية التسليم تتم بسلاسة."], "performance_indicators": ["نسبة التسليمات الناجحة في الموعد المحدد من إجمالي الطلبات."], "case_uses": {"use_cases": [{"id": "4.2.31-1", "title": "إدخال مواصفات قطع الغيار", "description": "محل قطع الغيار يقوم بإدخال مواصفات القطعة المطلوبة بما في ذلك الحجم، الوزن التقريبي، والعدد.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": "وجود طلب لقطع الغيار من العميل.", "postconditions": "يتم إدخال المواصفات وتسجيلها في النظام.", "main_flow": ["مح<PERSON> قطع الغيار يدخل إلى النظام.", "المحل يختار طلب قطع الغيار المطلوب.", "المحل يقوم بإدخال مواصفات القطعة بما في ذلك الحجم، الوزن، والعدد.", "النظام يسجل البيانات المدخلة."]}, {"id": "4.2.31-2", "title": "إدخال تكلفة قطع الغيار", "description": "محل قطع الغيار يقوم بإدخال تكلفة القطع المطلوبة.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": "يجب أن تكون مواصفات القطعة مسجلة في النظام.", "postconditions": "يتم تسجيل تكلفة القطعة في النظام.", "main_flow": ["محل قطع الغيار يحدد طلب القطعة في النظام.", "المحل يدخل تكلفة القطعة.", "النظام يسجل تكلفة القطعة."]}, {"id": "4.2.31-3", "title": "حساب رسوم الخدمة والضريبة", "description": "النظام يقوم بحساب رسوم الخدمة والضريبة على تكلفة القطع.", "actor": "النظام", "preconditions": "يجب أن تكون تكلفة القطعة مدخلة في النظام.", "postconditions": "تكون رسوم الخدمة والضريبة محسوبة ومسجلة.", "main_flow": ["النظام يأخذ تكلفة القطعة المدخلة.", "النظام يحسب رسوم الخدمة.", "النظام يحسب الضريبة على رسوم الخدمة.", "النظام يسجل رسوم الخدمة والضريبة."]}, {"id": "4.2.31-4", "title": "عرض صافي المبلغ المستحق", "description": "النظام يعرض صافي المبلغ المستحق بعد خصم الرسوم والضريبة من تكلفة القطعة.", "actor": "النظام", "preconditions": "يجب أن تكون رسوم الخدمة والضريبة محسوبة ومسجلة.", "postconditions": "يتم عرض صافي المبلغ المستحق للمحل.", "main_flow": ["النظام يأخذ تكلفة القطعة، رسوم الخدمة، والضريبة.", "النظام يحسب صافي المبلغ بعد الخصم.", "النظام يعرض صافي المبلغ للمحل."]}, {"id": "4.2.31-5", "title": "إرسال العرض إلى طالب الخدمة", "description": "محل قطع الغيار يقدم العرض ويرسله إلى طالب الخدمة.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": "يجب أن يكون صافي المبلغ المستحق معروضاً للمحل.", "postconditions": "يتم إرسال العرض إلى طالب الخدمة.", "main_flow": ["محل قطع الغيار يوافق على العرض النهائي.", "المحل يرسل العرض إلى طالب الخدمة.", "النظام يسجل العرض المرسل."]}, {"id": "4.2.31-6", "title": "إرسال رسالة نصية للعميل في حالة عدم القدرة على تقديم القطعة", "description": "محل قطع الغيار يرسل رسالة نصية للعميل تشرح السبب في حالة عدم القدرة على تقديم القطعة المطلوبة.", "actor": "م<PERSON><PERSON> قطع الغيار", "preconditions": "يجب أن يكون هناك طلب قطع غيار من العميل.", "postconditions": "يتم إرسال رسالة نصية للعميل.", "main_flow": ["محل قطع الغيار يحدد أنه غير قادر على تقديم القطعة.", "المحل يدخل السبب في النظام.", "النظام يرسل رسالة نصية للعميل تشرح السبب."]}]}}, "4.2.33": {"title": "تقييم طلب قطع الغيار", "business_goals": ["جمع آراء العملاء حول تجربتهم في طلب واستلام قطع الغيار لضمان جودة الخدمة وتحسينها مستقبلاً."], "stakeholders": ["العميل (طالب الخدمة)", "محلا<PERSON> قطع الغيار (مقدم الخدمة)", "المنصة (التطبيق)"], "main_steps": ["إرسال طلب تقييم للعميل بعد استلام القطعة.", "جمع التقييمات وتحليلها لعرضها في سجل المحل على المنصة."], "alternative_steps": ["إرسال تذكير للعميل في حالة عدم تقديم التقييم."], "user_stories": ["كعميل: أري<PERSON> تقديم تقييم صادق حول تجربتي في طلب واستلام قطع الغيار، لضمان أن تتحسن الخدمة في المستقبل.", "كمحل قطع غيار: أريد رؤية التقييمات التي حصلت عليها، حتى أتمكن من معرفة نقاط القوة والضعف في خدمتي."], "performance_indicators": ["نسبة الطلبات التي تم تقييمها من إجمالي الطلبات."], "case_uses": {"use_cases": [{"id": "4.2.33-1", "title": "التحقق من وجود المبلغ الكافي في المحفظة", "description": "العميل يتأكد من وجود المبلغ الكافي في المحفظة الخاصة به.", "actor": "العميل", "preconditions": "يجب أن يكون هناك طلب صفقة قطع غيار معروض.", "postconditions": "يتم التحقق من وجود المبلغ الكافي في المحفظة.", "main_flow": ["العميل يفتح تطبيق المنصة.", "العميل يتوجه إلى صفحة المحفظة.", "العميل يتحقق من رصيد المحفظة."]}, {"id": "4.2.33-2", "title": "إضافة الأموال للمحفظة", "description": "إذا لم يكن المبلغ المتوافر كافياً، فإن العميل يقوم بإضافة الأموال من خلال واحدة من طرق الدفع الإلكترونية المتاحة.", "actor": "العميل", "preconditions": "يجب أن يكون رصيد المحفظة غير كافٍ لإتمام الصفقة.", "postconditions": "يتم إضافة الأموال إلى المحفظة.", "main_flow": ["العميل يفتح صفحة المحفظة في التطبيق.", "العميل يختار خيار 'إضافة أموال'.", "العميل يختار طريقة الدفع الإلكترونية المناسبة.", "العميل يقوم بإضافة المبلغ المطلوب إلى المحفظة."]}, {"id": "4.2.33-3", "title": "حجز المبلغ وتجميده", "description": "في حالة التحقق من كود التحقق يتم حجز المبلغ وتجميده في انتظار التأكيد النهائي على الصفقة.", "actor": "النظام", "preconditions": "يجب أن يكون رصيد المحفظة كافياً لإتمام الصفقة.", "postconditions": "يتم حجز المبلغ المطلوب وتجميده في المحفظة.", "main_flow": ["النظام يرسل كود التحقق إلى العميل.", "العميل يدخ<PERSON> كود التحقق في التطبيق.", "النظام يتحقق من الكود.", "النظام يحجز المبلغ المطلوب ويجمده في المحفظة."]}, {"id": "4.2.33-4", "title": "تأكيد الصفقة وتنفيذ أوامر النقل", "description": "العميل يؤكد الصفقة، وعلى إثر ذلك يتم تنفيذ أوامر النقل.", "actor": "العميل", "preconditions": "يجب أن يكون المبلغ محجوزاً ومجمداً في المحفظة.", "postconditions": "يتم تأكيد الصفقة وتنفيذ أوامر النقل.", "main_flow": ["العميل يتوجه إلى صفحة الصفقة في التطبيق.", "العميل يضغط على 'تأكيد الصفقة'.", "النظام ينفذ أوامر النقل."]}, {"id": "4.2.33-5", "title": "إعادة المبلغ المحجوز في حالة الإلغاء", "description": "إذا تم طلب الإلغاء قبل بداية عملية التوصيل، يتم إعادة المبلغ المحجوز إلى المحفظة.", "actor": "النظام", "preconditions": "يجب أن تكون الصفقة في حالة محجوزة ولم تبدأ عملية التوصيل بعد.", "postconditions": "يتم إعادة المبلغ المحجوز إلى المحفظة.", "main_flow": ["العميل يطلب إلغاء الصفقة من خلال التطبيق.", "النظام يتحقق من حالة الصفقة.", "النظام يوا<PERSON>ق على الإلغاء إذا لم تبدأ عملية التوصيل.", "النظام يعيد المب<PERSON>غ المحجوز إلى المحفظة."]}]}}, "4.2.34": {"title": "دعم فني للعملاء", "business_goals": ["توفير دعم فني سريع وفعال للعملاء لضمان حل أي مشكلات قد تواجههم أثناء استخدام المنصة."], "stakeholders": ["العملاء (مستخدمو المنصة)", "فريق الدعم الفني"], "main_steps": ["العميل يرسل طلب دعم فني من خلال التطبيق.", "فريق الدعم يتلقى الطلب ويبدأ في معالجته.", "إرسال تحديثات دورية للعميل حول حالة طلبه."], "alternative_steps": ["إتاحة خيار الدردشة المباشرة مع فريق الدعم."], "user_stories": ["كعميل: أريد طلب دعم فني بسهولة من خلال التطبيق، وأريد أن أحصل على تحديثات دورية حول حالة طلبي.", "كفريق دعم فني: أريد تلقي طلبات الدعم بسرعة ومعالجتها بشكل فعال لضمان رضا العملاء."], "performance_indicators": ["متوسط زمن الاستجابة لطلبات الدعم الفني.", "نسبة الطلبات التي تم حلها بنجاح."], "case_uses": {}}, "4.2.35": {"title": "إشعارات الدفع", "business_goals": ["إرسال إشعارات دفع منتظمة للعملاء لتذكيرهم بالمستحقات المالية وضمان تسديدها في الوقت المناسب."], "stakeholders": ["العملاء (مستخدمو المنصة)", "فريق الإدارة المالية"], "main_steps": ["إرسال إشعارات الدفع بشكل منتظم قبل موعد الاستحقاق.", "متابعة حالة المدفوعات وتحديث العملاء بأي تغييرات."], "alternative_steps": ["إرسال إشعارات تذكير إضافية في حالة عدم تسديد المدفوعات في الوقت المحدد."], "user_stories": ["كعميل: أري<PERSON> تلقي إشعارات دفع تذكيرية قبل موعد الاستحقاق لضمان عدم تفويتها.", "كفريق إدارة مالية: أريد متابعة حالة المدفوعات وتحديث العملاء بأي تغييرات لضمان تسديد المستحقات في الوقت المناسب."], "performance_indicators": ["نسبة المدفوعات المسددة في الوقت المحدد من إجمالي المستحقات."], "case_uses": {}}, "4.2.36": {"title": "إدارة المستخدمين", "business_goals": ["توفير أدوات لإدارة حسابات المستخدمين بكفاءة وضمان أمان المعلومات الشخصية."], "stakeholders": ["إداري النظام", "المستخدمون (العملاء ومقدمو الخدمات)"], "main_steps": ["إداري النظام يمكنه تعديل بيانات المستخدمين (الاسم، رقم الهاتف، البريد الإلكتروني، إلخ).", "إداري النظام يمكنه تفعيل أو تعطيل حسابات المستخدمين حسب الحاجة.", "إداري النظام يمكنه مراقبة النشاطات المشتبه بها واتخاذ الإجراءات اللازمة."], "alternative_steps": ["في حالة وجود مشكلات في الحساب، يمكن للمستخدمين التواصل مع فريق الدعم الفني."], "user_stories": ["كإداري النظام، أريد تعديل بيانات المستخدمين بسهولة لضمان تحديث المعلومات.", "كإداري النظام، أريد تفعيل أو تعطيل حسابات المستخدمين حسب الحاجة لضمان أمان النظام.", "كمستخدم، أريد التواصل مع فريق الدعم الفني في حالة وجود مشكلات في حسابي لضمان حلها بسرعة."], "performance_indicators": ["عدد الحسابات التي تم تعديل بياناتها.", "عد<PERSON> الحسابات التي تم تفعيلها أو تعطيلها."], "case_uses": {}}, "4.2.37": {"title": "تقارير الأداء", "business_goals": ["توفير تقارير أداء شاملة تساعد في تحسين الخدمة واتخاذ قرارات مدروسة."], "stakeholders": ["إداري النظام", "فرق الإدارة"], "main_steps": ["جمع البيانات المتعلقة بأداء المنصة (عدد الطلبات، عدد المستخدمين النشطين، إلخ).", "تحليل البيانات واستخراج التقارير الدورية.", "عرض التقارير على فرق الإدارة لاتخاذ القرارات المناسبة."], "alternative_steps": ["إتاحة إمكانية تخصيص التقارير حسب الحاجة."], "user_stories": ["كإداري النظام، أريد جمع وتحليل البيانات المتعلقة بأداء المنصة لضمان تحسين الخدمة.", "كفريق إدارة، أريد عرض التقارير الدورية لاتخاذ القرارات المناسبة."], "performance_indicators": ["عدد التقارير التي تم إصدارها.", "نسبة التحسين في الأداء بناءً على التقارير."], "case_uses": {}}, "4.2.38": {"title": "إدارة الرسوم والضرائب", "business_goals": ["ضمان دقة وسلامة حساب الرسوم والضرائب على المعاملات وتوفير تقارير دقيقة للجهات المختصة."], "stakeholders": ["إداري النظام", "فرق المالية", "الجهات الحكومية المختصة"], "main_steps": ["جمع بيانات المعاملات المالية وحساب الرسوم والضرائب المطلوبة.", "إعداد تقارير دقيقة تتعلق بالرسوم والضرائب.", "تقديم التقارير للجهات المختصة وضمان الامتثال للقوانين واللوائح."], "alternative_steps": ["إتاحة إمكانية مراجعة الرسوم والضرائب وتصحيحها عند الحاجة."], "user_stories": ["كإداري النظام، أريد جمع بيانات المعاملات المالية وحساب الرسوم والضرائب لضمان دقة الحسابات.", "كفريق مالية، أريد إعداد تقارير دقيقة تتعلق بالرسوم والضرائب لضمان الامتثال للقوانين.", "كجهة حكومية مختصة، أريد استلام تقارير دقيقة تتعلق بالرسوم والضرائب لضمان الامتثال للقوانين."], "performance_indicators": ["نسبة الدقة في حساب الرسوم والضرائب.", "عدد التقارير المقدمة للجهات المختصة."], "case_uses": {}}, "4.2.39": {"title": "إدارة الشكاوى", "business_goals": ["توفير نظام فعال لإدارة الشكاوى وحلها بسرعة وفعالية لضمان رضا المستخدمين."], "stakeholders": ["المستخدمون (العملاء ومقدمو الخدمات)", "فريق الدعم الفني"], "main_steps": ["استقبال الشكاوى من المستخدمين عبر المنصة.", "تحليل الشكاوى وتصنيفها حسب النوع والأولوية.", "توزيع الشكاوى على فرق الدعم المناسبة لحلها.", "إرسال تحديثات دورية للمستخدمين حول حالة الشكاوى."], "alternative_steps": ["إتاحة خيار متابعة حالة الشكوى عبر التطبيق."], "user_stories": ["كمستخدم، أريد تقديم شكوى بسهولة عبر المنصة لضمان حل المشكلة بسرعة.", "كفريق دعم فني، أريد تصنيف الشكاوى وتوزيعها على الفرق المناسبة لضمان حلها بشكل فعال.", "كمستخدم، أريد متابعة حالة الشكوى عبر التطبيق لضمان علمي بمستجدات الحل."], "performance_indicators": ["عدد الشكاوى المستلمة وحالتها (محلولة، قيد الحل، مغلقة).", "متوسط زمن الاستجابة للشكاوى."], "case_uses": {}}, "4.2.40": {"title": "إدارة الإعلانات", "business_goals": ["توفير منصة فعالة للإعلانات تُمكن مقدمي الخدمات من الوصول إلى جمهور واسع وزيادة فرص العمل."], "stakeholders": ["مقدمو الخدمات", "العملاء", "إدارة المنصة"], "main_steps": ["إنشاء حملات إعلانية عبر المنصة.", "تحديد الجمهور المستهدف بناءً على البيانات الديموغرافية والسلوكية.", "عرض الإعلانات على المستخدمين في المواقع المناسبة داخل التطبيق.", "تحليل أداء الحملات الإعلانية وتقديم تقارير لمقدمي الخدمات."], "alternative_steps": ["إتاحة إمكانية تخصيص الإعلانات بناءً على احتياجات مقدمي الخدمات."], "user_stories": ["كمقدم خدمة، أريد إنشاء حملة إعلانية عبر المنصة للوصول إلى جمهور واسع وزيادة فرص العمل.", "كعميل، أريد رؤية إعلانات ذات صلة بخدمات أبحث عنها لسهولة العثور على ما أحتاجه.", "كإدارة المنصة، أريد تحليل أداء الحملات الإعلانية لضمان فعالية الإعلانات المقدمة."], "performance_indicators": ["عد<PERSON> الحملات الإعلانية التي تم إنشاؤها.", "نسبة التفاعل مع الإعلانات.", "عد<PERSON> الطلبات التي جاءت نتيجة للإعلانات."], "case_uses": {}}, "4.2.41": {"title": "إدارة العروض الترويجية", "business_goals": ["زيادة التحفيز لدى العملاء لاستخدام المنصة عبر تقديم عروض ترويجية وحوافز جذابة."], "stakeholders": ["العملاء", "مقدمو الخدمات", "إدارة المنصة"], "main_steps": ["إنشاء عروض ترويجية جديدة وتحديد شروطها.", "إطلاق الحملات الترويجية وإرسال الإشعارات للعملاء المستهدفين.", "متابعة تأثير العروض الترويجية على سلوك العملاء وتحليل النتائج."], "alternative_steps": ["إتاحة إمكانية تخصيص العروض الترويجية بناءً على سلوك العملاء وتفضيلاتهم."], "user_stories": ["كعميل، أريد الاستفادة من العروض الترويجية لتوفير المال وزيادة القيمة المستلمة من الخدمة.", "كمقدم خدمة، أريد المشاركة في العروض الترويجية لزيادة الطلب على خدماتي.", "كإدارة المنصة، أريد تحليل تأثير العروض الترويجية على سلوك العملاء لضمان فعاليتها."], "performance_indicators": ["عدد العروض الترويجية التي تم إطلاقها.", "نسبة الاستجابة للعروض الترويجية.", "زيادة في عدد الطلبات نتيجة للعروض الترويجية."], "case_uses": {}}, "4.2.42": {"title": "إدارة البيانات والتحليلات", "business_goals": ["توفير بيانات دقيقة وتحليلات شاملة لدعم اتخاذ القرارات الاستراتيجية وتحسين الأداء."], "stakeholders": ["إدارة المنصة", "فرق الإدارة", "مقدمو الخدمات"], "main_steps": ["جمع البيانات من مختلف مصادر المنصة.", "تحليل البيانات واستخراج الرؤى والتقارير المفيدة.", "عرض البيانات والتحليلات على فرق الإدارة ومقدمي الخدمات."], "alternative_steps": ["إتاحة إمكانية تخصيص التقارير والتحليلات بناءً على احتياجات الفرق المختلفة."], "user_stories": ["كإدارة المنصة، أريد جمع وتحليل البيانات من مختلف مصادر المنصة لدعم اتخاذ القرارات الاستراتيجية.", "كفريق إدارة، أريد الاطلاع على التقارير والتحليلات لتحسين الأداء وتحديد المجالات التي تحتاج إلى تحسين.", "كمقدم خدمة، أريد الاطلاع على البيانات المتعلقة بأدائي لتحسين خدماتي وزيادة رضا العملاء."], "performance_indicators": ["عدد التقارير والتحليلات التي تم إصدارها.", "نسبة التحسين في الأداء بناءً على التحليلات."], "case_uses": {}}, "4.2.43": {"title": "إدارة التذاكر والدعم الفني", "business_goals": ["توفير نظام فعال لإدارة التذاكر والدعم الفني لضمان حل المشكلات بسرعة وفعالية."], "stakeholders": ["العملاء", "مقدمو الخدمات", "فريق الدعم الفني"], "main_steps": ["استقبال التذاكر من المستخدمين عبر المنصة.", "تصنيف التذاكر وتحديد الأولويات.", "توزيع التذاكر على فرق الدعم المناسبة لحلها.", "إرسال تحديثات دورية للمستخدمين حول حالة التذاكر."], "alternative_steps": ["إتاحة خيار الدردشة المباشرة مع فريق الدعم لحل المشكلات الفورية."], "user_stories": ["كعميل، أريد تقديم تذكرة دعم فني بسهولة عبر المنصة لضمان حل مشكلتي بسرعة.", "كمقدم خدمة، أريد تقديم تذكرة دعم فني للحصول على المساعدة في حل المشكلات المتعلقة بخدماتي.", "كفريق دعم فني، أريد تصنيف التذاكر وتوزيعها على الفرق المناسبة لضمان حلها بشكل فعال."], "performance_indicators": ["متوسط زمن الاستجابة للتذاكر.", "نسبة التذاكر التي تم حلها بنجاح."], "case_uses": {}}, "4.2.44": {"title": "إدارة الشراكات والتعاونات", "business_goals": ["تعزيز الشراكات الاستراتيجية والتعاون مع الجهات المختلفة لتحقيق نمو مستدام وتوسيع نطاق الخدمات."], "stakeholders": ["إدارة المنصة", "الشركاء الاستراتيجيون", "مقدمو الخدمات"], "main_steps": ["تحديد الفرص الجديدة للشراكات والتعاونات.", "التفاوض مع الشركاء المحتملين وتوقيع الاتفاقيات.", "متابعة تنفيذ الشراكات وضمان تحقيق الأهداف المشتركة."], "alternative_steps": ["إتاحة خيار مراجعة الشراكات الحالية وتقييم فعاليتها."], "user_stories": ["كإدارة المنصة، أريد تحديد الفرص الجديدة للشراكات والتعاونات لضمان نمو مستدام.", "كشريك استراتيجي، أريد التعاون مع المنصة لتحقيق أهدافي المشتركة وزيادة الفائدة المتبادلة.", "كمقدم خدمة، أريد الاستفادة من الشراكات لتعزيز خدماتي وزيادة قاعدة عملائي."], "performance_indicators": ["عدد الشراكات الجديدة التي تم توقيعها.", "نسبة تحقيق الأهداف المشتركة من الشراكات."], "case_uses": {}}, "4.2.45": {"title": "إدارة العلاقات مع العملاء", "business_goals": ["توفير نظام فعال لإدارة العلاقات مع العملاء لضمان رضاهم وتحسين تجربة الاستخدام."], "stakeholders": ["العملاء", "إدارة المنصة", "فريق خدمة العملاء"], "main_steps": ["جمع البيانات المتعلقة بتجربة العملاء وتحليلها.", "التفاعل مع العملاء عبر قنوات متعددة (البريد الإلكتروني، الدردشة، الهاتف).", "تقديم حلول مخصصة لاحتياجات العملاء وتحسين تجربتهم."], "alternative_steps": ["إتاحة خيار استبانات رضا العملاء لتقييم الخدمات."], "user_stories": ["كعميل، أريد التفاعل مع فريق خدمة العملاء بسهولة عبر قنوات متعددة لضمان حل مشكلتي بسرعة.", "كفريق خدمة العملاء، أريد جمع البيانات المتعلقة بتجربة العملاء وتحليلها لتقديم حلول مخصصة لاحتياجاتهم.", "كإدارة المنصة، أريد تحسين تجربة العملاء بشكل مستمر لضمان رضاهم وزيادة ولائهم."], "performance_indicators": ["نسبة رضا العملاء بناءً على الاستبانات.", "متوسط زمن الاستجابة لطلبات العملاء."], "case_uses": {}}, "4.2.46": {"title": "إدارة الفواتير والمدفوعات", "business_goals": ["توفير نظام موثوق وفعال لإدارة الفواتير والمدفوعات لضمان سداد المستحقات بسرعة وسهولة."], "stakeholders": ["العملاء", "مقدمو الخدمات", "إدارة المنصة"], "main_steps": ["إنشاء الفواتير وإرسالها إلى العملاء ومقدمي الخدمات.", "متابعة حالة المدفوعات وتحديثها في النظام.", "توفير خيارات دفع متعددة لتسهيل عملية السداد."], "alternative_steps": ["إتاحة خيار إرسال تذكيرات تلقائية في حالة تأخر السداد."], "user_stories": ["كعميل، أريد استلام الفواتير بشكل دوري ودفعها بسهولة عبر المنصة.", "كمقدم خدمة، أريد متابعة حالة المدفوعات المستحقة لي وضمان سدادها في الوقت المناسب.", "كإدارة المنصة، أريد توفير خيارات دفع متعددة وتحديث حالة المدفوعات بشكل مستمر لضمان سداد المستحقات بسرعة وسهولة."], "performance_indicators": ["نسبة الفواتير المدفوعة في الوقت المحدد.", "عدد التذكيرات التلقائية المرسلة في حالة تأخر السداد."], "case_uses": {}}, "4.2.47": {"title": "إدارة العقود والاتفاقيات", "business_goals": ["توفير نظام فعال لإدارة العقود والاتفاقيات لضمان التزام الأطراف بالشروط المتفق عليها."], "stakeholders": ["العملاء", "مقدمو الخدمات", "إدارة المنصة"], "main_steps": ["إنشاء العقود والاتفاقيات وتوقيعها إلكترونيًا.", "متابعة حالة العقود وضمان التزام الأطراف بالشروط المتفق عليها.", "تخزين العقود والاتفاقيات بشكل آمن وسهل الوصول إليه."], "alternative_steps": ["إتاحة خيار مراجعة العقود وتعديلها عند الحاجة."], "user_stories": ["كعميل، أريد توقيع العقود والاتفاقيات إلكترونيًا لضمان سهولة الوصول إليها والالتزام بالشروط المتفق عليها.", "كمقدم خدمة، أريد متابعة حالة العقود وضمان التزام الأطراف بالشروط المتفق عليها.", "كإدارة المنصة، أريد تخزين العقود والاتفاقيات بشكل آمن وسهل الوصول إليه لضمان مرونة التعامل مع الأطراف."], "performance_indicators": ["عد<PERSON> العقود الموقعة إلكترونيًا.", "نسبة العقود التي تم الالتزام بها."], "case_uses": {}}, "4.2.48": {"title": "إدارة الموارد البشرية", "business_goals": ["توفير نظام فعال لإدارة الموارد البشرية لضمان تنظيم العمل وتحسين أداء الموظفين."], "stakeholders": ["إدارة المنصة", "الموظفون"], "main_steps": ["توظيف الموظفين الجدد وتدريبهم.", "متابعة أداء الموظفين وتقييمهم بشكل دوري.", "توفير برامج تطوير مهني للموظفين."], "alternative_steps": ["إتاحة خيار التقييم الذاتي للموظفين."], "user_stories": ["كإدارة المنصة، أريد توظيف الموظفين الجدد وتدريبهم لضمان تنظيم العمل وتحسين الأداء.", "كموظف، أريد متابعة أدائي وتقييمه بشكل دوري لضمان تحقيق الأهداف المهنية.", "كإدارة المنصة، أريد توفير برامج تطوير مهني للموظفين لضمان تحسين مهاراتهم وأدائهم."], "performance_indicators": ["نسبة الموظفين الجدد الذين تم توظيفهم وتدريبهم.", "عدد التقييمات الدورية للموظفين."], "case_uses": {}}, "4.2.49": {"title": "إدارة المخاطر والامتثال", "business_goals": ["تحديد وتقييم المخاطر المحتملة ووضع خطط للامتثال لضمان سلامة وأمان المنصة."], "stakeholders": ["إدارة المنصة", "الجهات الرقابية"], "main_steps": ["تحديد المخاطر المحتملة وتقييمها.", "وضع خطط للامتثال للقوانين واللوائح.", "متابعة تنفيذ خطط الامتثال وتحديثها حسب الحاجة."], "alternative_steps": ["إتاحة خيار مراجعة المخاطر وتحديثها بشكل دوري."], "user_stories": ["كإدارة المنصة، أريد تحديد المخاطر المحتملة وتقييمها لضمان سلامة وأمان المنصة.", "كإدارة المنصة، أريد وضع خطط للامتثال للقوانين واللوائح لضمان التزام المنصة بالمعايير المطلوبة.", "كإدارة المنصة، أريد متابعة تنفيذ خطط الامتثال وتحديثها حسب الحاجة لضمان استمرارية الامتثال."], "performance_indicators": ["عدد المخاطر التي تم تحديدها وتقييمها.", "نسبة الامتثال للقوانين واللوائح."], "case_uses": {}}, "4.2.50": {"title": "إدارة الابتكار والتطوير", "business_goals": ["تشجيع الابتكار والتطوير المستمر لضمان تحسين الخدمات وزيادة رضا العملاء."], "stakeholders": ["إدارة المنصة", "الموظفون", "العملاء"], "main_steps": ["تشجيع الموظفين على تقديم أفكار جديدة ومبتكرة.", "تقييم الأفكار وتحديد الأفكار القابلة للتنفيذ.", "تطوير الأفكار المبتكرة وتحويلها إلى مشاريع قابلة للتنفيذ."], "alternative_steps": ["إتاحة خيار مشاركة الأفكار مع الفرق المختلفة لتقييمها وتطويرها بشكل جماعي."], "user_stories": ["كإدارة المنصة، أريد تشجيع الموظفين على تقديم أفكار جديدة ومبتكرة لضمان تحسين الخدمات وزيادة رضا العملاء.", "كموظف، أريد تقديم أفكاري والمشاركة في تطويرها لضمان تحقيق الأهداف المشتركة.", "كعميل، أريد الاستفادة من الخدمات المحسنة والمبتكرة لضمان تجربة استخدام أفضل."], "performance_indicators": ["عدد الأفكار الجديدة التي تم تقديمها.", "نسبة الأفكار القابلة للتنفيذ التي تم تحويلها إلى مشاريع."], "case_uses": {}}, "4.2.51": {"title": "لوحة البيانات الإدارية", "business_goals": ["توفير معلومات واضحة لكافة الوثائق الهامة لممارسة أعمال النقل.", "توفير بيانات واضحة للمستخدم لاتخاذ القرارات المناسبة لتأدية أعماله."], "stakeholders": ["المستخدمون (مزودو خدمات النقل)", "إداري النظام"], "main_steps": ["المستخدم يقوم بإدخال بيانات الوثائق الإدارية لممارسة أعمال النقل.", "يقوم النظام بإصدار مؤشرات ضمن لوحة بيانات إدارية للمستخدم لتوضيح حالة الوثائق المسجلة وتاريخ انتهائها.", "يقوم النظام بإرسال إشعارات للمستخدم عند قرب انتهاء الوثائق."], "alternative_steps": [], "user_stories": ["كمستخدم، أريد أن أكون على علم بوضع ممارستي لأعمال النقل والجدوى الاقتصادية منها وتوفر المعلومات التي تساعدني في اتخاذ القرارات.", "كمستخدم، أريد تلقي إشعارات عند قرب انتهاء الوثائق."], "performance_indicators": ["مؤشر التكاليف التشغيلية على المركبة.", "مؤشر التكاليف الإدارية على المركبة.", "مؤشر تكاليف سائق المركبة.", "مؤشر انخفاض قيمة المركبة السنوي.", "العوائد المالية من المركبة.", "الربح المتحقق من المركبة."], "case_uses": {}}, "4.2.52": {"title": "استجابة لطلب قطع الغيار", "business_goals": ["تسهيل عملية طلب واستلام قطع الغيار بين العميل ومحل قطع الغيار."], "stakeholders": ["محلا<PERSON> قطع الغيار (مقدم الخدمة)", "العميل (طالب الخدمة)", "المنصة (التطبيق)"], "main_steps": ["محل قطع الغيار يقوم بإدخال مواصفات القطعة المطلوبة.", "محل قطع الغيار يقوم بإدخال تكلفة القطع.", "المنصة تقوم بحساب رسوم الخدمة والضريبة.", "عرض صافي المبلغ المستحق للمحل بعد خصم الرسوم والضريبة.", "محل قطع الغيار يقدم الموافقة ويرسل العرض إلى طالب الخدمة."], "alternative_steps": [], "user_stories": ["كمحل قطع غيار، أريد إدخال مواصفات وتكلفة القطع وعرض المبلغ الصافي المستحق بعد خصم الرسوم والضريبة.", "كعميل، أريد استلام عرض من محل قطع الغيار يشمل التفاصيل الدقيقة للمواصفات والتكاليف."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المستلمة ونسبتها لإجمالي الطلبات.", "عد<PERSON> الطلبات التي تم إتمام الصفقة عليها."], "case_uses": {}}, "4.2.53": {"title": "قبول القطع / رفض القطع", "business_goals": ["تسهيل عملية قبول أو رفض القطع وضمان تحويل الأموال المستحقة بشكل صحيح."], "stakeholders": ["طالب الخدمة", "م<PERSON><PERSON> قطع الغيار"], "main_steps": ["قبول القطع وسداد المستحقات وتحويل المبالغ المحجوزة من طالب الخدمة لحساب محل قطع الغيار.", "في حال الرفض، يقوم طالب الخدمة بإنشاء طلب إعادة القطع من خلال طلب توصيل.", "تحرير المبلغ من حساب محفظة طالب الخدمة بعد تأكيد استلام المحل للقطع.", "خصم رسوم الخدمة عند انتهاء طلب إعادة القطع."], "alternative_steps": [], "user_stories": ["كطالب خدمة، أريد قبول القطع وسداد المستحقات أو رفضها وإنشاء طلب إعادة القطع وتحرير المبلغ المحجوز بعد التأكيد.", "كمحل قطع غيار، أريد ض<PERSON>ان استلام المستحقات عند قبول القطع أو معالجة طلب إعادة القطع في حال الرفض."], "performance_indicators": ["ع<PERSON><PERSON> الطلبات المقبولة ورفضها ونسبتها لإجمالي الطلبات.", "إجمالي المبالغ المحجوزة والمحررة."], "case_uses": {}}, "4.2.54": {"title": "ارشفة منافسات النقل", "business_goals": ["توفير بيئة آمنة ومنظمة لأرشفة بيانات المنافسات وتمكين العملاء من الرجوع إليها في أي وقت."], "stakeholders": ["العميل", "المنصة (التطبيق)"], "main_steps": ["بعد انتهاء مدة النشر، يتم أرشفة بيانات المنافسة في حساب العميل.", "تخزين كافة البيانات المتعلقة بالمنافسة في سجلات المنصة."], "alternative_steps": [], "user_stories": ["كعميل، أريد الرجوع إلى بيانات المنافسة في حسابي بعد انتهاء مدة النشر.", "كمنصة، أريد تأمين بيانات المنافسة وتوفيرها للعملاء عند الحاجة."], "performance_indicators": ["عد<PERSON> المنافسات المؤرشفة.", "نسبة الرجوع إلى البيانات المؤرشفة."], "case_uses": {}}, "4.2.55": {"title": "إتمام صفقة قطع الغيار", "business_goals": ["تأكيد التعاملات المالية وضمان إتمام الصفقة بنجاح."], "stakeholders": ["العميل (طالب الخدمة)", "المنصة (التطبيق)"], "main_steps": ["التحقق من وجود المبلغ الكافي في محفظة العميل.", "إضافة الأموال إذا لم يكن المبلغ كافيًا.", "حجز المبلغ وتجميده بعد إرسال كود التحقق.", "تأكيد الصفقة وتنفيذ أوامر النقل.", "إعادة المبلغ المحجوز في حالة طلب الإلغاء قبل بدء عملية التوصيل."], "alternative_steps": ["اختيار عرض آخر أو إلغاء العملية إذا لم يكن المبلغ متوافر."], "user_stories": ["كعميل، أريد التأكد من وجود المبلغ الكافي في المحفظة وإتمام الصفقة بنجاح.", "كمنصة، أريد ضمان إتمام التعاملات المالية وحجز المبلغ بعد التأكيد."], "performance_indicators": ["نسبة الصفقات المؤكدة والمكتملة.", "عد<PERSON> الصفقات الملغاة ومبالغها."]}}