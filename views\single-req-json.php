<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SRS DOC</title>
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous"> -->
    <style>
        table {
            border: 1px solid #dededf;
            table-layout: fixed;
            border-collapse: collapse;
            border-spacing: 1px;
        }

        table caption {
            caption-side: top;
        }

        table th {
            border: 1px solid #dededf;
            background-color: #eceff1;
            color: #000000;
            padding: 0px;
        }

        table td {
            border: 1px solid #dededf;
            background-color: #ffffff;
            color: #000000;
            margin: 0px;
            padding-right: 3px
        }
        ul{
            margin-top: 0px;
            margin-bottom: 0px;
            padding-top: 0px;
            padding-bottom: 0px;
        }
        h2,h3{
            margin-top: 3px;
        }
        h4{
            margin-top: 25px;
            margin-bottom: 1px;
        }
    </style>
</head>
<bod>
<?php
include 'req-helper.php';
if (have_posts()) : 
    while (have_posts()) : the_post(); 
    $id = get_field('id');
    $id_label =  "3." . $id;
    ?>
    <?php if( isset($_GET['json']) && $_GET['json'] === '1') { ?>
        <div style="direction: rtl;">
            <h2><?php echo $id_label; ?> - <?php the_title(); ?></h2>
                <?php
                $requirementData = getRequirementById($id);
                echo "<h3>".$id_label.".1 المعلومات العامة</h3>";
                echo generateRequirementTable($requirementData);
                if (isset($requirementData['use_cases'])) {
                    echo "<h3>".$id_label.".2 حالات الاستخدام</h3>";
                    echo generateUseCasesTable($requirementData['use_cases']  , $id_label . ".2" );
                } else {
                    echo "No use cases found.";
                }
                ?>
        </div>
    <?php  } ?>
     <?php endwhile;
else :
    echo 'لا توجد منشورات لعرضها.';
endif;
?>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Mermaid
    mermaid.initialize({
        startOnLoad: true,
        // theme: 'forest', // Change the theme name here dark forest neutral slate
        fontFamily: '"Helvetica Neue",sans-serif', // Change the font family name here
        fontSize: 16,
        theme: 'base',
        themeVariables: {
        primaryColor: '#FFFAB7',
        primaryTextColor: '#000',
        primaryBorderColor: '#8576FF',
        lineColor: '#FFA62F',
        secondaryColor: '#f9f9f9',
        tertiaryColor: '#000'
        }
    });

    // Function to convert SVG to canvas and then to image
    function convertSvgToImage(element) {
        const svgElement = element.querySelector('svg');

        if (svgElement) {
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            // Extract the max-width from the SVG's style attribute
            const style = svgElement.getAttribute('style');
            const maxWidthMatch = style.match(/max-width:\s*([\d.]+)px/);
            const maxWidth = maxWidthMatch ? parseFloat(maxWidthMatch[1]) : svgElement.clientWidth;

            img.onload = function() {
                // Calculate the aspect ratio
                const aspectRatio = img.width / img.height;

                // Set canvas dimensions based on the max-width and aspect ratio
                const scale = 1; // Adjust the scale factor as needed
                canvas.width = maxWidth * scale;
                canvas.height = (maxWidth / aspectRatio) * scale;

                // Draw the image on the canvas
                ctx.scale(scale, scale);
                ctx.drawImage(img, 0, 0, maxWidth, maxWidth / aspectRatio);

                // Convert canvas to data URL (PNG or JPEG)
                const imgData = canvas.toDataURL('image/png'); // Change to 'image/jpeg' for JPEG format

                // Create an image element
                const imgElement = document.createElement('img');
                imgElement.src = imgData;

                // Insert the image inside the Mermaid div
                element.insertBefore(imgElement, svgElement);

                // Remove the original SVG
                element.removeChild(svgElement);

                // Optionally, provide a download link
                // const link = document.createElement('a');
                // link.href = imgData;
                // link.download = 'diagram.png'; // Change to 'diagram.jpg' for JPEG format
                // link.textContent = 'Download Diagram';
                // element.parentNode.insertBefore(link, imgElement.nextSibling);
            };

            // Set the source of the image to be the SVG data
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
    }

    // Convert all Mermaid diagrams after rendering
    document.querySelectorAll('.mermaid').forEach((element) => {
        // Use a mutation observer to detect when the SVG is added to the DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeName === 'svg') {
                        observer.disconnect();
                        convertSvgToImage(element);
                    }
                });
            });
        });

        observer.observe(element, { childList: true });
    });

    // Trigger Mermaid rendering
    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
});
</script>
</body>
</html>
