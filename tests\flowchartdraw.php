<!DOCTYPE html>
<html>
<head>
    <title>mxGraph Example</title>
    <script type="text/javascript" src="https://unpkg.com/mxgraph/javascript/mxClient.js"></script>
</head>
<body>
    <div id="graphContainer" style="width:100%;height:400px;"></div>
    <script type="text/javascript">
        function main(container)
        {
            var graph = new mxGraph(container);
            var parent = graph.getDefaultParent();

            graph.getModel().beginUpdate();
            try
            {
                var v1 = graph.insertVertex(parent, null, 'User Opens Registration Page', 150, 50, 100, 30);
                var v2 = graph.insertVertex(parent, null, 'User Fills Registration Form', 150, 100, 100, 30);
                var v3 = graph.insertVertex(parent, null, 'System Validates Data', 150, 150, 100, 30);
                var v4 = graph.insertVertex(parent, null, 'System Creates Account', 150, 200, 100, 30);
                var v5 = graph.insertVertex(parent, null, 'System Sends Activation Link', 150, 250, 100, 30);
                var v6 = graph.insertVertex(parent, null, 'System Displays Confirmation Message', 150, 300, 100, 30);

                graph.insertEdge(parent, null, '', v1, v2);
                graph.insertEdge(parent, null, '', v2, v3);
                graph.insertEdge(parent, null, '', v3, v4);
                graph.insertEdge(parent, null, '', v4, v5);
                graph.insertEdge(parent, null, '', v5, v6);
            }
            finally
            {
                graph.getModel().endUpdate();
            }
        }

        var container = document.getElementById('graphContainer');
        main(container);
    </script>
</body>
</html>
