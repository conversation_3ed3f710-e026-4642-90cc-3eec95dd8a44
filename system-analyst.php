<?php
/**
 * system analyst
 *
 * @package       ANALYST
 * <AUTHOR>
 * @version       0.0.2
 *
 * @wordpress-plugin
 * Plugin Name:   system analyst
 * Plugin URI:    https://systemanalyst.com
 * Description:   system analyst
 * Version:       0.0.2
 * Author:        <PERSON>
 * Author URI:    https://github.com/mohamedyassin07
 * Text Domain:   system-analyst
 * Domain Path:   /languages
 */

 // Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) exit;

// Definations
define( 'ANALYST_NAME',			'system analyst' );
define( 'ANALYST_SLUG',			'system_analyst' );
define( 'ANALYST_VERSION',		'0.0.2' );
define( 'ANALYST_PLUGIN_FILE', 	__FILE__ );
define( 'ANALYST_PLUGIN_BASE',	plugin_basename( ANALYST_PLUGIN_FILE ) );
define( 'ANALYST_PLUGIN_DIR',	plugin_dir_path( ANALYST_PLUGIN_FILE ) );
define( 'ANALYST_PLUGIN_URL',	plugin_dir_url( ANALYST_PLUGIN_FILE ) );
define('ALLOW_UNFILTERED_UPLOADS', true);

// add_action('acf/init', 'my_acfe_modules');
function my_acfe_modules(){

    // enable developer mode
    acf_update_setting('acfe/dev', true);
    
}
/**
 * Load the main class for the core functionality
 */
require_once ANALYST_PLUGIN_DIR . 'class-system-analyst.php';
/**
 * The main function to load the only instance
 * of our master class.
 *
 * <AUTHOR> Yassin
 * @since   0.0.1
 * @return  object|System_Analyst
 */
function ANALYST() {
	return System_Analyst::instance();
}

ANALYST();


